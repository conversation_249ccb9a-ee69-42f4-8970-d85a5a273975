#include "ServoControlManager.hpp"
#include "SyslogManager.hpp"
#include <chrono>
#include <thread>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <fstream>
#include <syslog.h>
#include <google/protobuf/util/json_util.h>
#include "motor_control.pb.h"


ServoControlManager::ServoControlManager(CANopenMaster *master)
        : canopen_master_(master),
          bed_owner_(BedOwnerType::NONE),
          isMonitorRunning_(false),
          stop_flag_(false),
          is_heartbeat_monitor_running_(false) {

    if (!canopen_master_) {
        throw std::runtime_error("CANopenMaster指针为空");
    }

    // 初始化Trigger信息
    current_trigger_info_ = {0, 0, 0, false};

    // 初始化床位运动状态
    bed_motion_status_[BedType::Primary] = BedMotionStatus::Ready;
    bed_motion_status_[BedType::Secondary] = BedMotionStatus::Ready;

    // 初始化默认心跳配置
    heartbeat_config_.timeout_ms = 30000;              // 30秒超时
    heartbeat_config_.check_interval_ms = 1000;        // 1秒检查间隔
    heartbeat_config_.enable_auto_homing = true;       // 启用自动回零
    heartbeat_config_.enable_heartbeat_monitor = false; // 启用心跳监控
    heartbeat_config_.max_missed_heartbeats = 3;       // 最大丢失3次心跳
}

ServoControlManager::~ServoControlManager() {
    // 停止心跳监控
    stopHeartbeatMonitor();

    // 停止监控线程
    if (isMonitorRunning_) {
        isMonitorRunning_ = false;
        if (statusMonitorThread_.joinable()) {
            statusMonitorThread_.join();
        }
    }

    // 关闭所有电机
    for (const auto &motor : motors_) {
        disableMotor(motor.first);
        resetNmtState(motor.first);
    }
}

bool ServoControlManager::addServoMotor(const ServoConfig &config) {
    // 检查节点是否已经存在
    if (motors_.find(config.nodeId) != motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机已经存在", static_cast<int>(config.nodeId));
        return false;
    }

    // 初始化NMT状态管理
    if (!canopen_master_->initializeNMTState(config.nodeId)) {
        LOG_ERROR_MSG(domain_, "初始化节点ID为 %d 的电机NMT状态失败", static_cast<int>(config.nodeId));
        return false;
    }

    // 检查节点是否可访问
    if (!canopen_master_->findNode(config.nodeId)) {
        LOG_ERROR_MSG(domain_, "找不到节点ID为 %d 的电机", static_cast<int>(config.nodeId));
        return false;
    }

    // 添加节点
    if (!canopen_master_->addNode(config.nodeId)) {
        LOG_ERROR_MSG(domain_, "添加节点ID为 %d 的电机失败", static_cast<int>(config.nodeId));
        return false;
    }

    // 存储电机配置
    motors_[config.nodeId] = config;
    motor_status_[config.nodeId] = ServoMotorStatus{};

    LOG_INFO_MSG(domain_, "添加节点ID为 %d 的电机成功", static_cast<int>(config.nodeId));
    return true;
}

ServoConfig ServoControlManager::getServoConfig(uint8_t nodeId) const {
    // std::lock_guard<std::mutex> lock(mutex_);

    auto it = motors_.find(nodeId);
    if (it == motors_.end()) {
        std::stringstream ss;
        ss << "节点ID " << static_cast<int>(nodeId) << " 的电机不存在";
        throw std::runtime_error(ss.str());
    }

    return it->second;
}

bool ServoControlManager::enableMotor(uint8_t nodeId) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    return canopen_master_->sendControlWord(nodeId, DS402_CW_ENABLE_OPERATION);
}

bool ServoControlManager::disableMotor(uint8_t nodeId) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    return canopen_master_->sendControlWord(nodeId, DS402_CW_DISABLE_OPERATION);
}

bool ServoControlManager::enterNmtOperational(uint8_t nodeId) {
    return canopen_master_->sendNMTCommand(NMT_ENTER_OPERATIONAL, nodeId);
}

bool ServoControlManager::resetNmtState(uint8_t nodeId) {
    return canopen_master_->sendNMTCommand(NMT_ENTER_PRE_OPERATIONAL, nodeId);
}

bool ServoControlManager::setOperationMode(uint8_t nodeId, DS402OperationMode mode) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 设置操作模式
    if (!canopen_master_->setOperationMode(nodeId, mode)) {
        LOG_ERROR_MSG(domain_, "设置操作模式失败");
        return false;
    }

    // 验证操作模式是否设置成功
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    DS402OperationMode currentMode = canopen_master_->getOperationMode(nodeId);

    if (currentMode != mode) {
        LOG_ERROR_MSG(domain_, "验证操作模式失败，设置值与读取值不符");
        return false;
    }

    LOG_INFO_MSG(domain_, "操作模式设置成功");
    return true;
}

bool ServoControlManager::moveToPosition(uint8_t nodeId, int32_t position, int32_t velocity, bool absolute = true, bool immediate = true) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 清除StopFlag
    setStopFlag(false);

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    // 设置为位置模式
    if (!setOperationMode(nodeId, DS402OperationMode::PROFILE_POSITION)) {
        LOG_ERROR_MSG(domain_, "设置位置模式失败");
        return false;
    }

    // 设置位置模式参数
    uint32_t profileVelocity = velocity == 0 ? motors_[nodeId].positionConfig.profileVelocity : velocity;
    if (!canopen_master_->setProfileVelocity(nodeId, profileVelocity)) {
        LOG_ERROR_MSG(domain_, "设置位置模式速度失败");
        return false;
    }

    if (!canopen_master_->setProfileAcceleration(nodeId, motors_[nodeId].positionConfig.profileAcceleration)) {
        LOG_ERROR_MSG(domain_, "设置位置模式加速度失败");
        return false;
    }

    if (!canopen_master_->setProfileDeceleration(nodeId, motors_[nodeId].positionConfig.profileDeceleration)) {
        LOG_ERROR_MSG(domain_, "设置位置模式减速度失败");
        return false;
    }

    if (!canopen_master_->setPositionTrackingWindow(nodeId, motors_[nodeId].positionConfig.positionTrackingWindow)) {
        LOG_ERROR_MSG(domain_, "设置位置模式跟踪窗口失败");
        return false;
    }

    if (!canopen_master_->setPositionTrackingWindowTime(nodeId, motors_[nodeId].positionConfig.positionTrackingWindowTime)) {
        LOG_ERROR_MSG(domain_, "设置位置模式跟踪窗口时间失败");
        return false;
    }

    if (!canopen_master_->setMotionProfileType(nodeId, 0)) {
        LOG_ERROR_MSG(domain_, "Failed to set motion profile type in position mode!");
        return false;
    }

    // 设置目标位置
    if (!canopen_master_->setTargetPosition(nodeId, position)) {
        LOG_ERROR_MSG(domain_, "设置目标位置失败");
        return false;
    }

    //  初始化控制位
    enableMotor(nodeId);

    // 生成控制字并发送
    uint16_t controlWord = 0x000F; // 基础控制字: 操作使能
    // 根据绝对/相对位置和立即/非立即执行设置对应位
    if (!absolute)
        controlWord |= (1 << 6); // 设置相对位置位
    if (immediate)
        controlWord |= (1 << 5); // 设置立即执行位

    // 设置启动位，开始运动
    controlWord |= (1 << 4);

    if (!canopen_master_->sendControlWord(nodeId, controlWord)) {
        LOG_ERROR_MSG(domain_, "发送控制字失败");
        return false;
    }

    LOG_INFO_MSG(domain_, "位置控制命令已发送，目标位置: %d", position);

    // 等待位置到达目标
    if (!waitForTargetReached(nodeId, position, DEFAULT_POSITION_TOLERANCE, DEFAULT_POSITION_TIMEOUT_MS)) {
        logOperationError(nodeId, "位置控制", "等待位置到达超时");
        handleOperationTimeout(nodeId, "位置控制");
        return false;
    }

    //  结束运动后失能电机
    disableMotor(nodeId);

    LOG_INFO_MSG(domain_, "位置控制完成，已到达目标位置: %d", position);
    return true;
}

bool ServoControlManager::moveWithVelocity(uint8_t nodeId, int32_t velocity, int32_t position) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 清除StopFlag
    setStopFlag(false);

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    // 以速度模式控制前先关闭电机
    disableMotor(nodeId);

    //  设置为速度模式
    if (!setOperationMode(nodeId, DS402OperationMode::PROFILE_VELOCITY)) {
        LOG_ERROR_MSG(domain_, "设置速度模式失败");
        return false;
    }

    // 设置目标速度
    if (!canopen_master_->setTargetVelocity(nodeId, velocity)) {
        LOG_ERROR_MSG(domain_, "设置目标速度失败");
        return false;
    } else
        LOG_INFO_MSG(domain_, "[Velocity Mode] Has set target velocity to %d", velocity);

    // 设置加速度
    if (!canopen_master_->setProfileAcceleration(nodeId, motors_[nodeId].velocityConfig.profileAcceleration)) {
        LOG_ERROR_MSG(domain_, "设置加速度失败");
        return false;
    }

    // 设置减速度
    if (!canopen_master_->setProfileDeceleration(nodeId, motors_[nodeId].velocityConfig.profileDeceleration)) {
        LOG_ERROR_MSG(domain_, "设置减速度失败");
        return false;
    }

    // 使能速度控制
    uint16_t controlWord = 0x000F; // 基础控制字: 操作使能

    // 设置启动位
    controlWord |= (1 << 4);

    if (!canopen_master_->sendControlWord(nodeId, controlWord)) {
        LOG_ERROR_MSG(domain_, "发送控制字失败");
        return false;
    }

    LOG_INFO_MSG(domain_, "速度控制命令已发送，目标速度: %d", velocity);

    // 等待速度到达目标值
    if (!waitForTargetVelocity(nodeId, velocity, position, DEFAULT_VELOCITY_TIMEOUT_MS, DEFAULT_VELOCITY_TOLERANCE)) {
        logOperationError(nodeId, "速度控制", "等待速度到达超时");
        handleOperationTimeout(nodeId, "速度控制");
        return false;
    }

    LOG_INFO_MSG(domain_, "速度控制完成，已到达目标速度: %d", velocity);
    return true;
}

bool ServoControlManager::moveWithHoming(uint8_t nodeId) {
    // std::lock_guard<std::mutex> lock(mutex_);

    // 清除StopFlag
    setStopFlag(false);

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    // 获取回零配置
    const ServoConfig &config = motors_[nodeId];
    const HomingConfig &homingConfig = config.homingConfig;

    // 设置为回零模式
    if (!setOperationMode(nodeId, DS402OperationMode::HOMING)) {
        LOG_ERROR_MSG(domain_, "设置回零模式失败");
        return false;
    }

    // 设置回零方法
    if (!canopen_master_->setHomingMethod(nodeId, homingConfig.homingMethod)) {
        LOG_ERROR_MSG(domain_, "设置回零模式失败");
        return false;
    }

    // 设置回零速度
    if (!canopen_master_->setHomingSpeeds(nodeId, FAST_SPEED, homingConfig.homingSpeed_switch) ||
        !canopen_master_->setHomingSpeeds(nodeId, SLOW_SPEED, homingConfig.homingSpeed_zero)) {
        LOG_ERROR_MSG(domain_, "设置回零速度失败");
        return false;
    }

    // 设置回零加速度
    if (!canopen_master_->setHomingAcceleration(nodeId, homingConfig.homingAcceleration)) {
        LOG_ERROR_MSG(domain_, "设置回零加速度失败");

        return false;
    }

    // 设置回零偏移
    if (!canopen_master_->setHomingOffset(nodeId, homingConfig.homeOffset)) {
        LOG_ERROR_MSG(domain_, "设置回零偏移失败");
        return false;
    }

    // 开始回零操作
    uint16_t controlWord = 0x000F; // 基础控制字: 操作使能

    // 设置启动回零位
    controlWord |= (1 << 4);

    if (!canopen_master_->sendControlWord(nodeId, controlWord)) {
        LOG_ERROR_MSG(domain_, "发送回零控制字失败");
        return false;
    }
    LOG_INFO_MSG(domain_, "回零操作已开始");

    // 等待回零完成
    if (!waitForHomingComplete(nodeId, DEFAULT_HOMING_TIMEOUT_MS)) {
        logOperationError(nodeId, "回零操作", "等待回零完成超时");
        handleOperationTimeout(nodeId, "回零操作");

        disableMotor(nodeId);
        return false;
    }

    //  运动结束后失能电机
    disableMotor(nodeId);

    LOG_INFO_MSG(domain_, "回零操作结束！");
    return true;
}

bool ServoControlManager::emergencyStop(uint8_t nodeId) {
    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    // 发送紧急停止命令
    if (!canopen_master_->sendControlWordCommand(nodeId, DS402Command::QUICK_STOP)) {
        LOG_ERROR_MSG(domain_, "发送紧急停止命令失败");
        return false;
    }

    LOG_INFO_MSG(domain_, "紧急停止命令已发送");
    return true;
}

bool ServoControlManager::clearFault(uint8_t nodeId) {

    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    // 发送故障复位命令
    if (!canopen_master_->sendControlWordCommand(nodeId, DS402Command::FAULT_RESET)) {
        LOG_ERROR_MSG(domain_, "发送故障复位命令失败");
        return false;
    }

    // 等待复位完成
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // 检查状态
    DS402State state = canopen_master_->getState(nodeId);
    if (state == DS402State::FAULT) {
        LOG_ERROR_MSG(domain_, "故障复位失败");
        return false;
    }

    LOG_INFO_MSG(domain_, "故障已清除");
    return true;
}

bool ServoControlManager::updateMotorStatus(uint8_t nodeId) {
    try {
        // 获取PDO数据
        PDOData pdoData = canopen_master_->getPDOData(nodeId);

        std::lock_guard<std::mutex> lock(status_mutex_);
        auto &status = motor_status_[nodeId];

        // 复制数据
        status.nodeId = nodeId;
        status.actualPosition = pdoData.actualPosition;
        status.actualVelocity = pdoData.actualVelocity;
        status.statusWord = pdoData.statusWord;
        status.ioStatus = pdoData.inputPinStatus;

        // 解析状态字
        status.isTargetReached = (pdoData.statusWord & DS402_SW_TARGET_REACHED) != 0;
        status.hasFault = (pdoData.statusWord & DS402_SW_FAULT) != 0;

        return true;
    }
    catch (const std::exception &e) {
        LOG_ERROR_MSG(domain_, "获取电机状态时发生错误: %s", e.what());
        return false;
    }
}

ServoMotorStatus ServoControlManager::getMotorStatus(uint8_t nodeId) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    if (motors_.find(nodeId) == motors_.end() && motor_status_.find(nodeId) == motor_status_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return ServoMotorStatus{};
    }

    return motor_status_[nodeId];
}

std::map<uint8_t, ServoMotorStatus> ServoControlManager::getAllMotorStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);

    return motor_status_;
}

bool ServoControlManager::waitForTargetReached(uint8_t nodeId, int32_t targetPosition, int32_t tolerance, int timeout_ms) {
    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    auto start_time = std::chrono::steady_clock::now();

    while (true) {
        // 检查StopFlag
        if (getStopFlag()) {
            LOG_INFO_MSG(domain_, "检测到StopFlag,提前结束运动");
            emergencyStop(nodeId);
            disableMotor(nodeId);
            return false; // 被停止也算成功
        }

        // 获取PDO数据
        PDOData pdoData = canopen_master_->getPDOData(nodeId);

        auto statusWord = pdoData.statusWord;
        auto actualPosition = pdoData.actualPosition;
        bool flag = (std::abs(actualPosition - targetPosition) < tolerance);
        LOG_DEBUG_MSG(domain_, "[Position mode] Actual position: %d, Target position: %d, StatusWord: %#04x", actualPosition, targetPosition, statusWord);

        // 检查目标到达位 (statusWord bit 10)
        if ((statusWord & DS402_SW_TARGET_REACHED) && tolerance) {
            LOG_INFO_MSG(domain_, "目标位置已到达");
            return true;
        }

        // 检查是否超时
        auto current_time = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count() > timeout_ms) {
            LOG_ERROR_MSG(domain_, "等待目标到达超时");
            return false;
        }

        // 等待一段时间再次检查
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

bool ServoControlManager::waitForTargetVelocity(uint8_t nodeId, int32_t targetVelocity, int32_t targetPosition, int timeout_ms, int32_t tolerance) {

    //  1. Wait for reaching target velocity.
    if (!waitForVelocityReached(nodeId, targetVelocity, tolerance, timeout_ms)) {
        return false;
    }

    auto start_time = std::chrono::steady_clock::now();
    // 2. Wait for reaching target position, according to calculate the difference between set and get.
    while (true) {
        try {
            // 检查StopFlag
            if (getStopFlag()) {
                LOG_INFO_MSG(domain_, "检测到StopFlag,提前结束运动");
                emergencyStop(nodeId);
                disableMotor(nodeId);
                return false; // 被停止也算成功
            }

            // 获取PDO数据
            PDOData pdoData = canopen_master_->getPDOData(nodeId);

            int32_t actualPosition = pdoData.actualPosition;
            int32_t positionDiff = actualPosition - targetPosition;

            if (actualPosition - targetPosition > 0) {
                LOG_INFO_MSG(domain_, "目标位置已到达 in velocity mode，实际位置: %d, 目标位置: %d", actualPosition, targetPosition);
                return true;
            }

            // 检查是否有故障
            if (pdoData.statusWord & DS402_SW_FAULT) { // 故障位
                LOG_ERROR_MSG(domain_, "电机故障，停止等待速度到达");
                return false;
            }

            // 检查是否超时
            auto current_time = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count() > timeout_ms) {
                LOG_ERROR_MSG(domain_, "等待VelocityMode到达超时!");
                return false;
            }

            // 等待一段时间再次检查
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
        catch (const std::exception &e) {
            LOG_ERROR_MSG(domain_, "等待VelocityMode到达时发生错误: %s", e.what());
            return false;
        }
    }
}

bool ServoControlManager::waitForVelocityReached(uint8_t nodeId, int32_t targetVelocity, int32_t tolerance, uint32_t timeout) {
    auto start_time = std::chrono::steady_clock::now();
    targetVelocity = targetVelocity / GEAR_RATIO / VELOCITY_FACTOR;

    while (true) {
        // 检查StopFlag
        if (getStopFlag()) {
            LOG_INFO_MSG(domain_, "检测到StopFlag, 提前结束运动");
            emergencyStop(nodeId);
            disableMotor(nodeId);
            return false; // 被停止也算成功
        }

        // 获取PDO数据
        auto pdoData = canopen_master_->getPDOData(nodeId);

        // 检查当前速度是否在目标速度的容差范围内
        int32_t actualVelocity = pdoData.actualVelocity;
        int32_t statusWord = pdoData.statusWord;
        bool flag = fabs(targetVelocity - actualVelocity / 100000.0) < std::max(0.1 * targetVelocity, 0.1);
        // bool flag = ((abs(targetVelocity - actualVelocity) / VELOCITY_FACTOR) < tolerance);
        LOG_DEBUG_MSG(domain_, "[Velocity mode] actual velocity: %d, target velocity: %d", actualVelocity / 10, targetVelocity * GEAR_RATIO * VELOCITY_FACTOR);

        if (flag && (pdoData.statusWord & DS402_SW_TARGET_REACHED)) {
            LOG_INFO_MSG(domain_, "速度已到达, 实际速度: %d, 目标速度: %d", actualVelocity, targetVelocity);
            return true;
        }

        // 检查是否有故障
        if (pdoData.statusWord & DS402_SW_FAULT) { // 故障位
            LOG_ERROR_MSG(domain_, "电机故障，停止等待速度到达");
            return false;
        }

        // 检查是否超时
        auto current_time = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count() > timeout) {
            LOG_ERROR_MSG(domain_, "等待VelocityMode到达超时!");
            return false;
        }

        // 等待一段时间再次检查
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

bool ServoControlManager::waitForHomingComplete(uint8_t nodeId, int timeout_ms) {
    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    auto start_time = std::chrono::steady_clock::now();
    while (true) {
        try {
            // 检查StopFlag
            if (getStopFlag()) {
                LOG_INFO_MSG(domain_, "检测到StopFlag,提前结束运动");
                emergencyStop(nodeId);
                disableMotor(nodeId);
                return false; // 被停止也算成功
            }

            // 获取PDO数据
            uint16_t statusWord = canopen_master_->getStatusWord(nodeId);

            // 检查回零完成状态位 (statusWord bit 12) 和 目标到达位 (bit 10)
            bool homingComplete = (statusWord & DS402_SW_HOMING_ATTAINED) != 0;
            bool targetReached = (statusWord & DS402_SW_TARGET_REACHED) != 0;
            LOG_INFO_MSG(domain_, "Homing status world: %#04x", statusWord);

            if (homingComplete && targetReached) {
                LOG_INFO_MSG(domain_, "回零操作完成");
                return true;
            }

            // 检查是否有故障
            if (statusWord & DS402_SW_FAULT) { // 故障位
                LOG_ERROR_MSG(domain_, "电机故障，回零操作失败");
                return false;
            }

            // 检查回零错误位 (statusWord bit 13)
            if (statusWord & DS402_SW_HOMING_ERROR) {
                LOG_ERROR_MSG(domain_, "回零操作出现错误");
                return false;
            }

            // 检查是否超时
            auto current_time = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count() > timeout_ms) {
                LOG_ERROR_MSG(domain_, "等待回零完成超时");
                return false;
            }

            // 等待一段时间再次检查
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        catch (const std::exception &e) {
            LOG_ERROR_MSG(domain_, "等待回零完成时发生错误: %s", e.what());
            return false;
        }
    }
}

bool ServoControlManager::configTouchProbe(uint8_t nodeId) {
    auto touchProbeConfigArray = motors_[nodeId].touchProbeConfig;

    bool result{true};
    for (auto &touchProbeConfig : touchProbeConfigArray) {
        result &= canopen_master_->setTouchProbeFunction(nodeId, touchProbeConfig.function);
        result &= canopen_master_->setTouchProbeSelect(nodeId, touchProbeConfig.touchProbeIndex, touchProbeConfig.ioSelect);
        result &= canopen_master_->setInputPinDebounceValues(nodeId, touchProbeConfig.touchProbeIndex, touchProbeConfig.debounceValues);
    }

    return result;
}

bool ServoControlManager::configTPDODefault(uint8_t nodeId) {
    // 默认TPDO配置（保持向后兼容）
    PDOConfig configTpdo1{};
    configTpdo1.transmissionType = OD_PDO_TRANS_SYNC_1;
    // 添加映射条目
    PDOMappingEntry statusWord = {OD_STATUS_WORD, 0, OD_MAP_LEN_16_BIT};
    PDOMappingEntry actualPosition = {OD_ACTUAL_POSITION, 0, OD_MAP_LEN_32_BIT};
    PDOMappingEntry actualVelocity = {OD_ACTUAL_VELOCITY, 0, OD_MAP_LEN_32_BIT};

    configTpdo1.mappingEntries.push_back(statusWord);
    configTpdo1.mappingEntries.push_back(actualPosition);

    PDOConfig configTpdo2{};
    configTpdo2.transmissionType = OD_PDO_TRANS_SYNC_1;
    configTpdo2.mappingEntries.push_back(statusWord);
    configTpdo2.mappingEntries.push_back(actualVelocity);

    bool result = canopen_master_->configureTPDO(nodeId, 1, configTpdo1);
    result &= canopen_master_->configureTPDO(nodeId, 2, configTpdo2);

    //  注册回调函数
    canopen_master_->registerPDOCallback(PDOType::RPDO, 1, nullptr);
    canopen_master_->registerPDOCallback(PDOType::RPDO, 2, nullptr);

    //  配置一级床TouchProbe， 通过回调函数来及时获取TPDO数据
    if (nodeId == bedTypeToNodeId(BedType::Primary)) {
        PDOConfig configTpdo3{};
        configTpdo3.transmissionType = OD_PDO_TRANS_EVENT_PROFILE;
        PDOMappingEntry touchProbePos1PosValue = {OD_TOUCH_PROBE_POS_1_POS_VALUE, 0, OD_MAP_LEN_32_BIT};
        PDOMappingEntry touchProbeTime1PosValue = {OD_TOUCH_PROBE_TIME_1_POS_VALUE, 0, OD_MAP_LEN_32_BIT};
        configTpdo3.mappingEntries.push_back(touchProbePos1PosValue);
        configTpdo3.mappingEntries.push_back(touchProbeTime1PosValue);
        result &= canopen_master_->configureTPDO(nodeId, 3, configTpdo3);
        canopen_master_->registerPDOCallback(PDOType::RPDO, 3, [&](uint8_t nodeId, const uint8_t *data, size_t length) {
            if (length >= 8) {
                uint8_t dataPos = 0;
                int32_t position = data[dataPos] | (data[dataPos + 1] << 8) |
                                   (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                dataPos += 4;
                uint32_t timestamp = data[dataPos] | (data[dataPos + 1] << 8) |
                                     (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                LOG_ERROR_MSG(domain_, "RPDO3 received message from %d, touchProbePos1PosValue: %d, touchProbeTime1PosValue: %u", static_cast<int>(nodeId), position, timestamp);
                setTriggerInfo(position, timestamp);
            }
        });
    }

    return result;
}

bool ServoControlManager::configInputPin(uint8_t nodeId) {
    auto inputPinConfigArray = motors_[nodeId].inputPinConfig;

    bool result{true};
    for (auto &inputPinConfig : inputPinConfigArray) {
        result &= canopen_master_->setInputPinConfiguration(nodeId, inputPinConfig.pinIndex, inputPinConfig.configuration);
        result &= canopen_master_->setInputPinDebounceValues(nodeId, inputPinConfig.pinIndex, inputPinConfig.debounceValues);
    }

    return result;
}

bool ServoControlManager::configTPDO(uint8_t nodeId) {
    // 检查电机是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    const ServoConfig &config = motors_[nodeId];
    bool result = true;

    // 如果配置文件中有TPDO配置，使用配置文件的设置
    if (!config.tpdoConfig.empty()) {
        LOG_INFO_MSG(domain_, "使用配置文件中的TPDO配置，节点ID: %d", static_cast<int>(nodeId));

        for (const auto &tpdoEntry : config.tpdoConfig) {
            PDOConfig pdoConfig{};
            pdoConfig.transmissionType = static_cast<uint8_t>(tpdoEntry.transmissionType);

            // 转换映射条目
            for (const auto &mappingEntry : tpdoEntry.mappingEntries) {
                PDOMappingEntry entry{};
                entry.index = static_cast<uint16_t>(mappingEntry.objectIndex);
                entry.subIndex = static_cast<uint8_t>(mappingEntry.subIndex);
                entry.length = static_cast<uint8_t>(mappingEntry.dataLength);
                pdoConfig.mappingEntries.push_back(entry);
            }

            // 配置TPDO
            bool configResult = canopen_master_->configureTPDO(nodeId, tpdoEntry.pdoNumber, pdoConfig);
            if (!configResult) {
                LOG_ERROR_MSG(domain_, "配置TPDO[%u] 失败，节点ID: %d", tpdoEntry.pdoNumber, static_cast<int>(nodeId));
            }
            result &= configResult;

            // 注册回调函数（清空之前的回调）
            canopen_master_->registerPDOCallback(PDOType::RPDO, tpdoEntry.pdoNumber, [this](uint8_t nodeId, const uint8_t *data, size_t length) {
                handleTPDO1Data(nodeId, data, length);
            });
        }

        // 为一级床的TPDO3注册特殊的TouchProbe回调
        if (nodeId == bedTypeToNodeId(BedType::Primary)) {
            // 查找TPDO3配置
            for (const auto &tpdoEntry : config.tpdoConfig) {
                if (tpdoEntry.pdoNumber == 3) {
                    canopen_master_->registerPDOCallback(PDOType::RPDO, tpdoEntry.pdoNumber, [this](uint8_t nodeId, const uint8_t *data, size_t length) {
                        handleTouchProbeData(nodeId, data, length);
                    });
                    break;
                }
            }
        }
    } else {
        // 如果配置文件中没有TPDO配置，使用默认配置（保持向后兼容）
        LOG_INFO_MSG(domain_, "使用默认TPDO配置，节点ID: %d", static_cast<int>(nodeId));
        result = configTPDODefault(nodeId);
    }

    return result;
}

// ==== 动物床控制功能的实现 ====

bool ServoControlManager::startMoveBed(BedType bedType, motor_control::MotionMode mode, float position, float velocity, BedOwnerType requiredOwner) {
    // 权限检查
    if (!checkBedControlPermission(requiredOwner, "StartMove")) {
        return false;
    }

    // 处理两张床一起移动的情况 - 必须先完成二级床，再执行一级床
    if (bedType == BedType::Both) {
        return executeBedOperationSequentially(
                {BedType::Secondary, BedType::Primary},
                [this, mode, position, velocity, requiredOwner](BedType singleBedType) {
                    return startMoveBed(singleBedType, mode, position, velocity, requiredOwner);
                },
                "移床操作",
                100 // 操作间隔100ms
        );
    }

    // 获取节点ID
    uint8_t nodeId = bedTypeToNodeId(bedType);
    if (nodeId == 0) {
        LOG_ERROR_MSG(domain_, "无效的床类型: %d", static_cast<int>(bedType));
        return false;
    }

    // Clear the faults.
    clearFault(nodeId);

    // 设置床位状态为运动中
    if (!SetBedMotionStatus(bedType, BedMotionStatus::Moving, "开始移床操作")) {
        LOG_ERROR_MSG(domain_, "设置床位状态失败 - 床类型: %d", static_cast<int>(bedType));
        return false;
    }

    try {
        bool result = false;
        // 转换位置和速度为电机脉冲单位
        int32_t positionTicks = positionToTicks(position);
        int32_t velocityTicks = velocityToTicks(velocity);

        // 根据运动模式执行相应的操作
        switch (mode) {
            case motor_control::MotionMode::PositionMode:
                // 位置模式：调用位置控制
            {
                result = moveToPosition(nodeId, positionTicks, velocityTicks);
            }
                break;

            case motor_control::MotionMode::VelocityMode:
                // 速度模式：直接调用速度控制
            {
                result = moveWithVelocity(nodeId, velocityTicks, positionTicks);
            }
                break;

            case motor_control::MotionMode::HomingMode:
                // 回零模式：执行回零操作
                result = moveWithHoming(nodeId);
                break;

            default:
                LOG_ERROR_MSG(domain_, "未知的运动模式: %d", static_cast<int>(mode));
                return false;
        }

        if (result) {
            // 运动成功完成，设置状态为Ready
            SetBedMotionStatus(bedType, BedMotionStatus::Ready, "移床操作完成");
            LOG_INFO_MSG(domain_, "床移动完成 - 床类型: %d, 模式: %d, 位置: %.2f, 速度: %.2f",
                         static_cast<int>(bedType), static_cast<int>(mode), position, velocity);
        } else {
            // 运动失败，设置状态为Error
            SetBedMotionStatus(bedType, BedMotionStatus::Error, "移床操作失败");
            LOG_ERROR_MSG(domain_, "床移动失败 - 床类型: %d, 模式: %d",
                          static_cast<int>(bedType), static_cast<int>(mode));
        }

        return result;
    }
    catch (const std::exception &e) {
        // 异常情况，设置状态为Error
        SetBedMotionStatus(bedType, BedMotionStatus::Error, "移床操作异常");
        LOG_ERROR_MSG(domain_, "移床操作异常: %s", e.what());
        return false;
    }
}

bool ServoControlManager::stopMoveBed(BedType bedType, BedOwnerType requiredOwner) {
    // 权限检查
    if (!checkBedControlPermission(requiredOwner, "StopMove")) {
        return false;
    }


    // 处理两张床一起停止的情况
    if (bedType == BedType::Both) {
        // 对于停止操作，我们同时停止两张床，不需要等待顺序
        return executeBedStopOperationForBoth(
                [this, requiredOwner](BedType singleBedType) {
                    return stopMoveBed(singleBedType, requiredOwner);
                },
                "停床操作");
    }

    // 获取对应的节点ID
    uint8_t nodeId = bedTypeToNodeId(bedType);
    if (nodeId == 0) {
        LOG_ERROR_MSG(domain_, "无效的床类型: %d", static_cast<int>(bedType));
        return false;
    }

    try {
        // 执行紧急停止
        bool result = emergencyStop(nodeId);

        if (result) {
            // 停止成功，设置状态为Estop（紧急停止状态）
            // SetBedMotionStatus(bedType, BedMotionStatus::Estop, "执行停床操作");
            LOG_INFO_MSG(domain_, "床停止成功 - 床类型: %d!", static_cast<int>(bedType));
        } else {
            // 停止失败，设置状态为Error
            SetBedMotionStatus(bedType, BedMotionStatus::Error, "停床操作失败");
            LOG_ERROR_MSG(domain_, "床停止失败 - 床类型: %d!", static_cast<int>(bedType));
        }

        return result;
    }
    catch (const std::exception &e) {
        // 异常情况，设置状态为Error
        SetBedMotionStatus(bedType, BedMotionStatus::Error, "停床操作异常");
        LOG_ERROR_MSG(domain_, "停床操作异常:  %s", e.what());
        return false;
    }
}

bool ServoControlManager::homingBed(BedType bedType) {
    // 处理两张床一起回零的情况
    if (bedType == BedType::Both) {
        // 回零操作需要按顺序进行，先二级床后一级床
        return executeBedOperationSequentially(
                {BedType::Secondary, BedType::Primary},
                [this](BedType singleBedType) -> bool {
                    return homingBed(singleBedType);
                },
                "回零操作",
                100 // 操作间隔100ms
        );
    }

    // 获取对应的节点ID
    uint8_t nodeId = bedTypeToNodeId(bedType);
    if (nodeId == 0) {
        LOG_ERROR_MSG(domain_, "无效的床类型: %d", static_cast<int>(bedType));
        return false;
    }

    // 设置床位状态为运动中
    if (!SetBedMotionStatus(bedType, BedMotionStatus::Moving, "开始回零操作")) {
        LOG_ERROR_MSG(domain_, "设置床位状态失败 - 床类型: %d", static_cast<int>(bedType));
        return false;
    }

    try {
        // 执行回零操作（现在会等待完成）
        bool result = moveWithHoming(nodeId);

        if (result) {
            // 回零成功，设置状态为Ready
            SetBedMotionStatus(bedType, BedMotionStatus::Ready, "回零操作完成");
            LOG_INFO_MSG(domain_, "床回零完成 - 床类型: %d", static_cast<int>(bedType));
        } else {
            // 回零失败，设置状态为Error
            SetBedMotionStatus(bedType, BedMotionStatus::Error, "回零操作失败");
            LOG_ERROR_MSG(domain_, "床回零失败 - 床类型: %d", static_cast<int>(bedType));
        }

        return result;
    }
    catch (const std::exception &e) {
        // 异常情况，设置状态为Error
        SetBedMotionStatus(bedType, BedMotionStatus::Error, "回零操作异常");
        LOG_ERROR_MSG(domain_, "回零操作异常: %s", e.what());
        return false;
    }
}

BedStatus ServoControlManager::getBedStatus(BedType bedType) {

    BedStatus status{};
    // 如果是两张床，返回默认值
    if (bedType == BedType::Both) {
        return status;
    }

    // 获取对应的节点ID
    uint8_t nodeId = bedTypeToNodeId(bedType);
    if (nodeId == 0) {
        LOG_ERROR_MSG(domain_, "无效的床类型: %d", static_cast<int>(bedType));
        return status;
    }

    // 获取电机状态
    ServoMotorStatus motorStatus = getMotorStatus(nodeId);

    // 填充运动信息
    status.motionInfo.position = ticksToPosition(motorStatus.actualPosition);
    status.motionInfo.velocity = ticksToVelocity(motorStatus.actualVelocity);

    // 设置床运动状态
    status.status = GetBedMotionStatus(bedType);

    // 设置运动能力限制，这里使用默认值
    // 实际应用中应根据电机配置设置
    if (bedType == BedType::Primary) {
        status.positionMin = 0.0f;
        status.positionMax = 0.0f;
        status.velocityMin = 0.0f;
        status.velocityMax = 0.0f;
        status.accelerationMax = 0.0f;
        status.decelerationMax = 0.0f;
    } else if (bedType == BedType::Secondary) {
        status.positionMin = 0.0f;
        status.positionMax = 0.0f;
        status.velocityMin = 0.0f;
        status.velocityMax = 0.0f;
        status.accelerationMax = 0.0f;
        status.decelerationMax = 0.0f;
    }

    return status;
}

PostIdInfo ServoControlManager::getPostId(BedType bedType) {
    // std::lock_guard<std::mutex> lock(mutex_);

    PostIdInfo postId{0, 0, 0, 0};

    // 根据床类型设置不同的PostId
    switch (bedType) {
        case BedType::Primary:
            postId.vid = 0x12345678;
            postId.did = 0x87654321;
            postId.hwid = 0x11223344;
            postId.rid = 0x44332211;
            break;

        case BedType::Secondary:
            postId.vid = 0x9ABCDEF0;
            postId.did = 0x0FEDCBA9;
            postId.hwid = 0x55667788;
            postId.rid = 0x88776655;
            break;

        default:
            // 对于无效类型或Both类型，返回全零
            break;
    }

    return postId;
}

BedOwnerType ServoControlManager::getBedOwner() {
    return bed_owner_;
}

bool ServoControlManager::gainBedOwner(BedOwnerType owner) {
    // 如果已经有控制权且尝试再次设置相同控制权，直接返回成功
    if (bed_owner_ == owner) {
        LOG_INFO_MSG(domain_, "控制权已存在，拥有者: %d", static_cast<int>(owner));
        return true;
    }

    // 如果当前有控制权且不是NONE，则需要先释放
    if (bed_owner_ != BedOwnerType::NONE && owner != BedOwnerType::NONE) {
        LOG_ERROR_MSG(domain_, "设备已被其他主机控制，无法获取控制权，当前拥有者: %d", static_cast<int>(bed_owner_.load()));
        return false;
    }

    // 设置控制权
    bed_owner_ = owner;
    LOG_INFO_MSG(domain_, "控制权已设置为: %d", static_cast<int>(owner));
    return true;
}

bool ServoControlManager::releaseBedOwner(BedOwnerType owner) {
    if (bed_owner_ == owner) {
        bed_owner_ = BedOwnerType::NONE;
        LOG_INFO_MSG(domain_, "控制权已释放，原拥有者: %d", static_cast<int>(owner));
        return true;
    }

    LOG_ERROR_MSG(domain_, "释放控制权失败，当前拥有者: %d, 尝试释放: %d",
                  static_cast<int>(bed_owner_.load()), static_cast<int>(owner));
    return false;
}

// ==== 私有辅助方法 ====

uint8_t ServoControlManager::bedTypeToNodeId(BedType bedType) {
    switch (bedType) {
        case BedType::Primary:
            return primary_bed_node_id_;

        case BedType::Secondary:
            return secondary_bed_node_id_;

        default:
            LOG_ERROR_MSG(domain_, "无效的床类型: %d", static_cast<int>(bedType));
            return 0; // 无效ID
    }
}

int32_t ServoControlManager::positionToTicks(float position) {
    // 将用户单位(mm)转换为电机脉冲
    // 这里使用简单的比例因子，实际应用中应该根据电机和机构参数计算
    return static_cast<int32_t>(position * GEAR_RATIO);
}

int32_t ServoControlManager::velocityToTicks(float velocity) {
    // 将用户单位(mm/s)转换为电机脉冲/秒
    return static_cast<int32_t>(velocity * GEAR_RATIO * VELOCITY_FACTOR);
}

float ServoControlManager::ticksToPosition(int32_t ticks) {
    // 将电机脉冲转换为用户单位(mm)
    return static_cast<float>(ticks) / GEAR_RATIO;
}

float ServoControlManager::ticksToVelocity(int32_t ticks) {
    // 将电机脉冲/秒转换为用户单位(mm/s)
    return static_cast<float>(ticks) / GEAR_RATIO / VELOCITY_FACTOR;
}

bool ServoControlManager::isValidPosition(BedType bedType, float position) {
    // 根据床类型检查位置参数是否在有效范围内
    switch (bedType) {
        case BedType::Primary:
            return position >= -1000.0f && position <= 1000.0f;

        case BedType::Secondary:
            return position >= -500.0f && position <= 500.0f;

        default:
            return false;
    }
}

bool ServoControlManager::isValidVelocity(BedType bedType, float velocity) {
    // 根据床类型检查速度参数是否在有效范围内
    switch (bedType) {
        case BedType::Primary:
            return velocity >= 0.0f && velocity <= 100.0f;

        case BedType::Secondary:
            return velocity >= 0.0f && velocity <= 80.0f;

        default:
            return false;
    }
}

// ==== 床控制辅助方法实现 ====

bool ServoControlManager::validateSingleBedParameters(BedType bedType, float position, float velocity) {
    // 检查位置参数是否有效
    if (!isValidPosition(bedType, position)) {
        LOG_ERROR_MSG(domain_, "无效的位置参数: %.2f (床类型: %d)", position, static_cast<int>(bedType));
        return false;
    }

    // 检查速度参数是否有效
    if (!isValidVelocity(bedType, velocity)) {
        LOG_ERROR_MSG(domain_, "无效的速度参数: %.2f (床类型: %d)", velocity, static_cast<int>(bedType));
        return false;
    }

    return true;
}

bool ServoControlManager::executeBedOperationForBoth(std::function<bool(BedType)> operation, const std::string &operationName, bool allowPartialSuccess) {
    LOG_INFO_MSG(domain_, "开始执行两张床的%s - 先执行二级床，完成后再执行一级床", operationName.c_str());

    // 第一步：执行二级床操作并等待完成
    LOG_INFO_MSG(domain_, "正在执行二级床的%s...", operationName.c_str());
    bool secondarySuccess = operation(BedType::Secondary);

    if (!secondarySuccess) {
        LOG_ERROR_MSG(domain_, "二级床的%s失败，停止执行一级床操作", operationName.c_str());
        return false; // 二级床失败，不继续执行一级床
    }

    LOG_INFO_MSG(domain_, "二级床的%s成功完成，开始执行一级床操作", operationName.c_str());

    // 第二步：二级床成功后，执行一级床操作
    std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 操作间隔
    bool primarySuccess = operation(BedType::Primary);

    bool overallSuccess = allowPartialSuccess ? (secondarySuccess || primarySuccess) : (secondarySuccess && primarySuccess);

    if (overallSuccess) {
        LOG_INFO_MSG(domain_, "两张床的%s完成 (二级床: %s, 一级床: %s)", operationName.c_str(),
                             secondarySuccess ? "成功" : "失败", primarySuccess ? "成功" : "失败");
    } else {
        LOG_ERROR_MSG(domain_, "两张床的%s失败", operationName.c_str());
        
    }

    return overallSuccess;
}

bool ServoControlManager::executeBedOperationSequentially(const std::vector<BedType> &bedTypes, std::function<bool(BedType)> operation, const std::string &operationName, int intervalMs) {
    LOG_INFO_MSG(domain_, "开始顺序执行%s", operationName.c_str());

    bool overallSuccess = true;

    for (size_t i = 0; i < bedTypes.size(); ++i) {
        BedType bedType = bedTypes[i];
        bool success = operation(bedType);

        if (!success) {
            LOG_ERROR_MSG(domain_, "床类型 %d 的%s失败", static_cast<int>(bedType), operationName.c_str());
            overallSuccess = false;
            break; // 顺序操作中，如果一个失败就停止
        }

        // 如果不是最后一个操作，添加间隔
        if (i < bedTypes.size() - 1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(intervalMs));
        }
    }

    if (overallSuccess) {
        LOG_INFO_MSG(domain_, "顺序%s全部完成", operationName.c_str());
    }

    return overallSuccess;
}

bool ServoControlManager::executeBedStopOperationForBoth(std::function<bool(BedType)> operation, const std::string &operationName) {
    LOG_INFO_MSG(domain_, "开始执行两张床的%s - 同时停止两张床", operationName.c_str());

    // 对于停止操作，我们同时执行两张床的停止，不需要等待顺序
    bool secondarySuccess = operation(BedType::Secondary);
    bool primarySuccess = operation(BedType::Primary);

    // 对于停止操作，只要有一张床停止成功就算成功
    bool overallSuccess = secondarySuccess || primarySuccess;

    if (overallSuccess) {
        LOG_INFO_MSG(domain_, "两张床的%s完成 (二级床: %s, 一级床: %s)", operationName.c_str(),
                     secondarySuccess ? "成功" : "失败", primarySuccess ? "成功" : "失败");
    } else {
        LOG_ERROR_MSG(domain_, "两张床的%s全部失败", operationName.c_str());
    }

    return overallSuccess;
}

// ==== 错误处理和清理方法实现 ====

bool ServoControlManager::handleOperationTimeout(uint8_t nodeId, const std::string &operation) {
    LOG_INFO_MSG(domain_, "处理%s超时，节点ID: %d", operation.c_str(), static_cast<int>(nodeId));

    // 获取对应的床位类型
    BedType bedType = nodeIdToBedType(nodeId);

    try {
        // 尝试紧急停止
        bool stopSuccess = emergencyStop(nodeId);

        // 检查电机状态
        DS402State state = canopen_master_->getState(nodeId);

        if (stopSuccess && state == DS402State::QUICK_STOP_ACTIVE) {
            LOG_INFO_MSG(domain_, "超时处理成功，电机已紧急停止");
            // emergencyStop方法已经设置了Estop状态，这里不需要重复设置
            return true;
        } else {
            LOG_ERROR_MSG(domain_, "超时处理失败，电机状态: %d", static_cast<int>(state));
            // 设置床位状态为Error
            SetBedMotionStatus(bedType, BedMotionStatus::Error, operation + "超时处理失败");
            return performEmergencyCleanup(nodeId, operation);
        }
    }
    catch (const std::exception &e) {
        LOG_ERROR_MSG(domain_, "处理超时异常: %s", e.what());
        // 设置床位状态为Error
        SetBedMotionStatus(bedType, BedMotionStatus::Error, operation + "超时处理异常");
        return performEmergencyCleanup(nodeId, operation);
    }
}

bool ServoControlManager::performEmergencyCleanup(uint8_t nodeId, const std::string &operation) {
    LOG_INFO_MSG(domain_, "执行紧急清理，操作: %s, 节点ID: %d", operation.c_str(), static_cast<int>(nodeId));

    // 获取对应的床位类型
    BedType bedType = nodeIdToBedType(nodeId);
    bool cleanupSuccess = true;

    try {
        // 1. 尝试禁用电机
        if (!disableMotor(nodeId)) {
            LOG_ERROR_MSG(domain_, "紧急清理：禁用电机失败");
            cleanupSuccess = false;
        }

        // 2. 尝试清除故障（如果有）
        DS402State state = canopen_master_->getState(nodeId);
        if (state == DS402State::FAULT) {
            if (!clearFault(nodeId)) {
                LOG_ERROR_MSG(domain_, "紧急清理：清除故障失败");
                cleanupSuccess = false;
            }
        }

        // 3. 等待一段时间让系统稳定
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // 4. 检查最终状态
        state = canopen_master_->getState(nodeId);
        if (state == DS402State::FAULT) {
            LOG_ERROR_MSG(domain_, "紧急清理后电机仍处于故障状态");
            cleanupSuccess = false;
        }
    }
    catch (const std::exception &e) {
        LOG_ERROR_MSG(domain_, "紧急清理异常: %s", e.what());
        cleanupSuccess = false;
    }

    if (cleanupSuccess) {
        LOG_INFO_MSG(domain_, "紧急清理完成");
        // 清理成功，可以尝试恢复到Ready状态
        SetBedMotionStatus(bedType, BedMotionStatus::Ready, "紧急清理完成，状态恢复");
    } else {
        LOG_ERROR_MSG(domain_, "紧急清理失败，需要人工干预");
        // 清理失败，保持Error状态
        SetBedMotionStatus(bedType, BedMotionStatus::Error, "紧急清理失败");
    }

    return cleanupSuccess;
}

void ServoControlManager::logOperationError(uint8_t nodeId, const std::string &operation, const std::string &error) {
    // 获取当前时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    LOG_ERROR_MSG(domain_, "节点ID: %d, 操作: %s, 错误: %s", static_cast<int>(nodeId), operation.c_str(), error.c_str());

    // 可以在这里添加日志文件写入或其他日志记录机制
}

bool ServoControlManager::initializeNode(uint8_t nodeId) {
    // 检查节点是否存在
    if (motors_.find(nodeId) == motors_.end()) {
        LOG_ERROR_MSG(domain_, "节点ID %d 的电机不存在", static_cast<int>(nodeId));
        return false;
    }

    LOG_INFO_MSG(domain_, "开始初始化节点ID为 %d 的电机...", static_cast<int>(nodeId));

    // 1. 配置TPDO
    if (!configTPDO(nodeId)) {
        LOG_ERROR_MSG(domain_, "配置节点ID为 %d 的电机TPDO失败", static_cast<int>(nodeId));
        return false;
    }
    LOG_INFO_MSG(domain_, "配置TPDO成功");
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 2. 配置IO
    if (!configInputPin(nodeId)) {
        LOG_ERROR_MSG(domain_, "配置节点ID为 %d 的电机IO失败", static_cast<int>(nodeId));
        return false;
    }
    LOG_INFO_MSG(domain_, "配置IO成功");
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 3. 根据电机类型配置探针操作
    if (motors_[nodeId].type == ServoMotorType::COPLEY_FIRST) {
        if (!configTouchProbe(nodeId)) {
            LOG_ERROR_MSG(domain_, "配置节点ID为 %d 的电机探针操作失败", static_cast<int>(nodeId));
            return false;
        }
        LOG_INFO_MSG(domain_, "配置探针操作成功");
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 4. 清除驱动器故障
    if (!clearFault(nodeId)) {
        LOG_ERROR_MSG(domain_, "清除节点ID为 %d 的电机故障失败", static_cast<int>(nodeId));
        return false;
    }
    LOG_INFO_MSG(domain_, "清除驱动器故障成功");
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // 5. 切换NMT状态至Operational
    if (!enterNmtOperational(nodeId)) {
        LOG_ERROR_MSG(domain_, "切换节点ID为 %d 的电机至Operational状态失败", static_cast<int>(nodeId));
        return false;
    }
    LOG_INFO_MSG(domain_, "切换节点至Operational状态成功");
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // 6. 执行回零操作
    // if (!moveWithHoming(nodeId)) {
    //     LOG_ERROR_MSG(domain_, "节点ID为 %d 的电机回零失败", static_cast<int>(nodeId));
    //     return false;
    // }
    // LOG_INFO_MSG(domain_, "执行回零操作成功");

    LOG_INFO_MSG(domain_, "节点ID为 %d 的电机初始化完成", static_cast<int>(nodeId));
    return true;
}

bool ServoControlManager::startManager(const std::string &configFile) {
    // 1. 读取配置文件
    std::ifstream file(configFile);
    if (!file.is_open()) {
        LOG_ERROR_MSG(domain_, "无法打开配置文件: %s", configFile.c_str());
        return false;
    }

    // 读取文件内容到字符串
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string json_str = buffer.str();
    file.close();

    // 2. 解析配置文件，使用protobuf的JSON工具
    ServoConfigProto::ServoConfigMessage servoConfig;
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;

    auto status = google::protobuf::util::JsonStringToMessage(json_str, &servoConfig, options);
    if (!status.ok()) {
        LOG_ERROR_MSG(domain_, "解析配置文件失败: %s", status.ToString().c_str());
        return false;
    }

    // 3. 根据配置文件添加节点
    for (const auto &motor : servoConfig.servo_motors()) {
        // 创建电机配置
        ServoConfig config;
        config.nodeId = motor.node_id();
        config.name = motor.name();

        // 设置电机类型并更新对应的床位节点ID
        if (motor.type() == "COPLEY_FIRST") {
            config.type = ServoMotorType::COPLEY_FIRST;
            primary_bed_node_id_ = config.nodeId;  // 更新一级床节点ID
            LOG_INFO_MSG(domain_, "设置一级床节点ID: 0x%02x", static_cast<int>(config.nodeId));
        } else if (motor.type() == "COPLEY_SECOND") {
            config.type = ServoMotorType::COPLEY_SECOND;
            secondary_bed_node_id_ = config.nodeId;  // 更新二级床节点ID
            LOG_INFO_MSG(domain_, "设置二级床节点ID: 0x%02x", static_cast<int>(config.nodeId));
        } else {
            config.type = ServoMotorType::GENERIC;
        }

        // 设置编码器分辨率
        config.encoderResolution = motor.encoder_resolution();

        // 设置位置模式参数
        if (motor.has_position_mode()) {
            const auto &pos = motor.position_mode();
            config.positionConfig.profileVelocity = pos.profile_velocity();
            config.positionConfig.profileAcceleration = pos.profile_acceleration();
            config.positionConfig.profileDeceleration = pos.profile_deceleration();
            config.positionConfig.useLimitSwitches = pos.use_limit_switches();
            config.positionConfig.positiveLimit = pos.positive_limit();
            config.positionConfig.negativeLimit = pos.negative_limit();
            config.positionConfig.positionTrackingWindow = pos.position_tracking_window();
            config.positionConfig.positionTrackingWindowTime = pos.position_tracking_window_time();
        }

        // 设置速度模式参数
        if (motor.has_velocity_mode()) {
            const auto &vel = motor.velocity_mode();
            config.velocityConfig.profileAcceleration = vel.profile_acceleration();
            config.velocityConfig.profileDeceleration = vel.profile_deceleration();
            config.velocityConfig.maxVelocity = vel.max_velocity();
            config.velocityConfig.minVelocity = vel.min_velocity();
        }

        // 设置回零参数
        if (motor.has_homing()) {
            const auto &homing = motor.homing();
            config.homingConfig.homingMethod = (int8_t) homing.method();
            config.homingConfig.homingSpeed_switch = homing.speed_switch();
            config.homingConfig.homingSpeed_zero = homing.speed_zero();
            config.homingConfig.homingAcceleration = homing.acceleration();
            config.homingConfig.homeOffset = homing.offset();
        }

        // 设置输入引脚配置
        for (const auto &pin : motor.input_pins()) {
            InputPinConfig pinConfig{};
            pinConfig.pinIndex = pin.pin_index();
            pinConfig.configuration = pin.configuration();
            pinConfig.debounceValues = (uint16_t) pin.debounce_values();
            config.inputPinConfig.push_back(pinConfig);
        }

        // 设置探针配置
        for (const auto &probe : motor.touch_probes()) {
            TouchProbeConfig probeConfig{};
            probeConfig.touchProbeIndex = probe.probe_index();
            probeConfig.function = probe.function();
            probeConfig.ioSelect = (int16_t) probe.io_select();
            probeConfig.debounceValues = probe.debounce_values();
            config.touchProbeConfig.push_back(probeConfig);
        }

        // 设置TPDO配置
        for (const auto &tpdo : motor.tpdo_config()) {
            TPDOConfigEntry tpdoConfig{};
            tpdoConfig.pdoNumber = tpdo.pdo_number();
            tpdoConfig.transmissionType = tpdo.transmission_type();

            // 设置映射条目
            for (const auto &entry : tpdo.mapping_entries()) {
                PDOMappingEntryConfig mappingEntry{};
                mappingEntry.objectIndex = entry.object_index();
                mappingEntry.subIndex = entry.sub_index();
                mappingEntry.dataLength = entry.data_length();
                tpdoConfig.mappingEntries.push_back(mappingEntry);
            }
            config.tpdoConfig.push_back(tpdoConfig);
        }

        // 添加电机
        if (!addServoMotor(config)) {
            LOG_ERROR_MSG(domain_, "添加电机失败，节点ID: %d", static_cast<int>(config.nodeId));
        }
    }

    // 4. 分别初始化各子节点
    bool allNodesInitialized = true;
    for (const auto &motor : motors_) {
        if (!initializeNode(motor.first)) {
            LOG_ERROR_MSG(domain_, "初始化节点ID为 %d 的电机失败", static_cast<int>(motor.first));
            allNodesInitialized = false;
        }
    }

    if (!allNodesInitialized) {
        LOG_ERROR_MSG(domain_, "部分节点初始化失败");
    }

    // 5. 加载心跳配置
    if (!loadHeartbeatConfig(servoConfig)) {
        LOG_ERROR_MSG(domain_, "加载心跳配置失败");
        // 心跳配置失败不影响基本功能，继续启动
    }

    // 6. 启动心跳监控
    if (!startHeartbeatMonitor()) {
        LOG_ERROR_MSG(domain_, "启动心跳监控失败");
        // 心跳监控失败不影响基本功能，继续启动
    }

    // 7. 开启状态监控线程
    startStatusMonitorThread();

    LOG_INFO_MSG(domain_, "ServoManager启动完成");
    return allNodesInitialized;
}

void ServoControlManager::startStatusMonitorThread() {
    // 确保之前的线程已经结束
    if (isMonitorRunning_) {
        isMonitorRunning_ = false;
        if (statusMonitorThread_.joinable()) {
            statusMonitorThread_.join();
        }
    }

    // 启动新的监控线程
    isMonitorRunning_ = true;
    statusMonitorThread_ = std::thread([this]() {
        LOG_INFO_MSG(domain_, "状态监控线程已启动");
        std::map<uint8_t, ServoMotorStatus> motorStatusList{};

        while (isMonitorRunning_) {
            try {
                // 获取所有电机状态
                {
                    std::lock_guard<std::mutex> lock(status_mutex_);
                    motorStatusList = motor_status_;
                }

                // 在这里可以处理状态，如检查故障、发送通知等
                for (const auto &status : motorStatusList) {
                    uint8_t nodeId = status.second.nodeId;
                    BedType bedType = nodeIdToBedType(nodeId);

                    // 检查电机故障状态
                    if (status.second.hasFault) {
                        LOG_ERROR_MSG(domain_, "节点ID为 %d 的电机出现故障",static_cast<int>(nodeId));
                        // 设置床位状态为Error
                        SetBedMotionStatus(bedType, BedMotionStatus::Error, "电机故障检测");
                    }

                    // 检查IO状态（ESTOP信号）
                    if (!(status.second.ioStatus & 0x0001)) {
                        LOG_INFO_MSG(domain_, "节点ID为 %d 的电机ESTOP信号触发",static_cast<int>(nodeId));
                        // 设置床位状态为Estop
                        SetBedMotionStatus(bedType, BedMotionStatus::Estop, "ESTOP信号触发");
                    }

                    // 同步床位状态与电机状态
                    BedMotionStatus currentBedStatus = GetBedMotionStatus(bedType);

                    // 如果电机状态正常且床位状态为Error，尝试恢复到Ready状态
                    if (currentBedStatus == BedMotionStatus::Error &&
                        status.second.hasFault) {
                        clearFault(nodeId);
                        SetBedMotionStatus(bedType, BedMotionStatus::Ready, "电机状态恢复正常");
                    }
                }

                // 周期性检查，每500ms一次
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            } catch (const std::exception &e) {
                LOG_ERROR_MSG(domain_, "状态监控线程异常: %s", e.what());
                // 异常恢复后继续运行
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }

        LOG_INFO_MSG(domain_, "状态监控线程已停止");
    });
}

// ==== 新增业务逻辑控制方法实现 ====

bool ServoControlManager::checkBedControlPermission(BedOwnerType requiredOwner, const std::string &operation) {
    BedOwnerType currentOwner = bed_owner_.load();

    // 调试模式，客户端类型为NONE
    if (currentOwner == BedOwnerType::NONE && requiredOwner == BedOwnerType::NONE) {
        LOG_INFO_MSG(domain_, "权限检查通过 - 调试模式，操作: %s", operation.c_str());
        return true;
    }

    // 如果请求的主机类型与当前控制权归属匹配，则允许操作
    if (currentOwner == requiredOwner) {
        LOG_INFO_MSG(domain_, "权限检查通过 - 操作: %s, 权限: %d",
                     operation.c_str(), static_cast<int>(requiredOwner));
        return true;
    }

    LOG_ERROR_MSG(domain_, "权限检查失败 - 操作: %s, 需要权限: %d, 当前权限: %d",
                  operation.c_str(), static_cast<int>(requiredOwner), static_cast<int>(currentOwner));
    return false;
}

bool ServoControlManager::checkMotionInterference(BedType bedType, float targetPosition, float targetVelocity) {
    //  todo: delete
    return true;

    // 检查位置是否在有效范围内
    if (!isValidPosition(bedType, targetPosition)) {
        LOG_ERROR_MSG(domain_, "目标位置超出范围: %.2f", targetPosition);
        return false;
    }

    // 检查速度是否在有效范围内
    if (!isValidVelocity(bedType, targetVelocity)) {
        LOG_ERROR_MSG(domain_, "目标速度超出范围: %.2f", targetVelocity);
        return false;
    }

    // 检查是否有其他床正在运动（避免冲突）
    if (bedType == BedType::Both) {
        // 检查两张床的状态
        BedStatus primaryStatus = getBedStatus(BedType::Primary);
        BedStatus secondaryStatus = getBedStatus(BedType::Secondary);

        if (primaryStatus.status == BedMotionStatus::Moving ||
            secondaryStatus.status == BedMotionStatus::Moving) {
            LOG_ERROR_MSG(domain_, "有床正在运动，无法执行Both操作");
            return false;
        }
    }

    return true;
}

void ServoControlManager::setStopFlag(bool value) {
    stop_flag_.store(value);
    if (value) {
        LOG_INFO_MSG(domain_, "StopFlag已设置为true");
    }
}

bool ServoControlManager::getStopFlag() const {
    return stop_flag_.load();
}

TriggerInfo ServoControlManager::getCurrentTriggerInfo() {
    std::lock_guard<std::mutex> lock(trigger_mutex_);
    return current_trigger_info_;
}

bool ServoControlManager::setTriggerInfo(int32_t position, uint32_t timestamp) {
    static const int threshold_ns = 20 * (int)pow(10, 6);   // 20ms
    std::lock_guard<std::mutex> lock(trigger_mutex_);

    if (timestamp == current_trigger_info_.timestamp) {
        LOG_DEBUG_MSG(domain_, "Received trigger info is identical to last one, last=%u, now=%u", current_trigger_info_.timestamp, timestamp);
        return false;
    }

    int triggerTimeStampIntervalNs = static_cast<int>(timestamp - current_trigger_info_.timestamp);
    if (triggerTimeStampIntervalNs > threshold_ns) {
        current_trigger_info_.interval = (int) (triggerTimeStampIntervalNs * 1.0 / pow(10, 6));
    } else if (abs(triggerTimeStampIntervalNs) <= threshold_ns && current_trigger_info_.position == position) {
        LOG_DEBUG_MSG(domain_, "Trigger info is redundant!");
        current_trigger_info_.timestamp = timestamp;
        current_trigger_info_.isValid = false;
        return false;
    } else {
        current_trigger_info_.interval = (int) (triggerTimeStampIntervalNs + std::pow(2, 32) * 1.0 / 100000);
    }
    current_trigger_info_.position = position;
    current_trigger_info_.timestamp = timestamp;
    current_trigger_info_.triggerTimestamp = std::chrono::time_point_cast<std::chrono::microseconds>(std::chrono::system_clock::now()).time_since_epoch().count();
    current_trigger_info_.isValid = true;

    LOG_INFO_MSG(domain_, "Trigger info set: Position=%d, Timestamp=%u", position, timestamp);

    trigger_info_changed_.notify_all(); // 通知所有等待的线程
    return true;
}

bool ServoControlManager::waitForTriggerInfoChange(TriggerInfo &lastKnownInfo, int timeout_ms) {
    std::unique_lock<std::mutex> lock(trigger_mutex_);

    // 使用带超时和谓词的wait_for
    // 它的返回值就能告诉我们是真的有新数据(true)，还是仅仅超时了(false)
    bool has_new_info = trigger_info_changed_.wait_for(lock, std::chrono::milliseconds(timeout_ms), [&] {
        // 判断数据是否更新。
        return current_trigger_info_.timestamp != lastKnownInfo.timestamp;
    });

    if (has_new_info) {
        // 如果has_new_info为true，说明条件满足，真的有新数据
        lastKnownInfo = current_trigger_info_;
        return true; // 返回true，通知GrpcServer发送新数据
    }

    // 如果has_new_info为false，说明是超时了，数据没有变化
    return false; // 返回false，GrpcServer将不会发送数据，继续等待下一轮通知
}

// ==== TPDO回调函数实现 ====

void ServoControlManager::handleTPDO1Data(uint8_t nodeId, const uint8_t *data, size_t length) {
    LOG_DEBUG_MSG(domain_, "RPDO received message from %d", static_cast<int>(nodeId));
    updateMotorStatus(nodeId);
}

void ServoControlManager::handleTPDO2Data(uint8_t nodeId, const uint8_t *data, size_t length) {
    LOG_INFO_MSG(domain_, "RPDO2 received message from %d", static_cast<int>(nodeId));
    auto pdoData = canopen_master_->getPDOData(nodeId);
}

void ServoControlManager::handleTPDO3Data(uint8_t nodeId, const uint8_t *data, size_t length) {
    LOG_INFO_MSG(domain_, "RPDO3 received message from %d", static_cast<int>(nodeId));
    auto pdoData = canopen_master_->getPDOData(nodeId);
}

void ServoControlManager::handleTouchProbeData(uint8_t nodeId, const uint8_t *data, size_t length) {
    if (length < 8) {
        LOG_ERROR_MSG(domain_, "The length of input touch probe data is less than 8!");
        return;
    }

    uint8_t dataPos = 0;
    uint32_t timestamp = data[dataPos] | (data[dataPos + 1] << 8) |
                         (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
    dataPos += 4;
    int32_t position = data[dataPos] | (data[dataPos + 1] << 8) |
                       (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);


//    LOG_DEBUG_MSG(domain_, "Received trigger info from node[%d]: touchProbePos1PosValue - %d, touchProbeTime1PosValue - %u", static_cast<int>(nodeId), position, timestamp);

    if (!setTriggerInfo(position, timestamp))
        return;
}

// ==== 心跳检测功能实现 ====

bool ServoControlManager::updateHeartbeat(BedOwnerType owner) {
    // 权限检查
    if (!checkBedControlPermission(owner, "HeartBeatCheck")) {
        return false;
    }

    std::lock_guard<std::mutex> lock(heartbeat_mutex_);
    // 更新心跳时间戳
    auto now = std::chrono::steady_clock::now();
    client_heartbeats_[owner] = {
            owner,
            now,
            true,
            0  // 重置丢失计数
    };

    LOG_INFO_MSG(domain_, "心跳已更新，客户端: %d", static_cast<int>(owner));
    return true;
}

bool ServoControlManager::isHeartbeatValid(BedOwnerType owner) {
    std::lock_guard<std::mutex> lock(heartbeat_mutex_);

    auto it = client_heartbeats_.find(owner);
    if (it == client_heartbeats_.end() || !it->second.is_active) {
        return false;
    }

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - it->second.last_heartbeat).count();

    return elapsed < heartbeat_config_.timeout_ms;
}

bool ServoControlManager::startHeartbeatMonitor() {
    if (!heartbeat_config_.enable_heartbeat_monitor) {
        LOG_INFO_MSG(domain_, "心跳监控已禁用");
        return true;
    }

    if (is_heartbeat_monitor_running_.load()) {
        LOG_INFO_MSG(domain_, "心跳监控线程已在运行");
        return true;
    }

    is_heartbeat_monitor_running_ = true;
    heartbeat_monitor_thread_ = std::thread([this]() {
        heartbeatMonitorThreadFunction();
    });

    LOG_INFO_MSG(domain_, "心跳监控线程已启动");
    return true;
}

void ServoControlManager::stopHeartbeatMonitor() {
    if (is_heartbeat_monitor_running_.load()) {
        is_heartbeat_monitor_running_ = false;
        if (heartbeat_monitor_thread_.joinable()) {
            heartbeat_monitor_thread_.join();
        }
        LOG_INFO_MSG(domain_, "心跳监控线程已停止");
    }
}

void ServoControlManager::heartbeatMonitorThreadFunction() {
    LOG_INFO_MSG(domain_, "心跳监控线程开始运行");

    while (is_heartbeat_monitor_running_.load()) {
        try {
            BedOwnerType current_owner = bed_owner_.load();

            // 只监控有控制权的客户端
            if (current_owner != BedOwnerType::NONE) {
                if (!isHeartbeatValid(current_owner)) {
                    LOG_ERROR_MSG(domain_, "检测到心跳超时，客户端: %d", static_cast<int>(current_owner));
                    handleHeartbeatTimeout(current_owner);
                }
            }

            // 按配置的间隔检查
            std::this_thread::sleep_for(std::chrono::milliseconds(heartbeat_config_.check_interval_ms));

        } catch (const std::exception &e) {
            LOG_ERROR_MSG(domain_, "心跳监控线程异常: %s", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    LOG_INFO_MSG(domain_, "心跳监控线程已退出");
}

bool ServoControlManager::handleHeartbeatTimeout(BedOwnerType owner) {
    LOG_INFO_MSG(domain_, "处理心跳超时，客户端: %d", static_cast<int>(owner));

    try {
        // 1. 记录超时事件
        syslog(LOG_WARNING, "客户端心跳超时，类型: %d", static_cast<int>(owner));

        // 2. 执行安全回零操作
        bool homingSuccess = false;
        if (heartbeat_config_.enable_auto_homing) {
            homingSuccess = performSafeHomingOnTimeout(owner);
        }

        // 3. 释放控制权
        bool releaseSuccess = releaseBedOwner(owner);

        // 4. 清理心跳信息
        clearHeartbeatInfo(owner);

        LOG_INFO_MSG(domain_, "心跳超时处理完成 - 回零: %s, 权限释放: %s",
                     homingSuccess ? "成功" : "失败", releaseSuccess ? "成功" : "失败");

        return homingSuccess && releaseSuccess;

    } catch (const std::exception &e) {
        LOG_ERROR_MSG(domain_, "心跳超时处理异常: %s", e.what());
        syslog(LOG_ERR, "心跳超时处理异常: %s", e.what());
        return false;
    }
}

bool ServoControlManager::performSafeHomingOnTimeout(BedOwnerType owner) {
    LOG_INFO_MSG(domain_, "执行安全回零操作，原控制者: %d", static_cast<int>(owner));

    try {
        // 1. 紧急停止所有运动
        bool stopSuccess = true;
        stopSuccess &= emergencyStop(bedTypeToNodeId(BedType::Primary));
        stopSuccess &= emergencyStop(bedTypeToNodeId(BedType::Secondary));

        if (!stopSuccess) {
            LOG_ERROR_MSG(domain_, "紧急停止失败");
            return false;
        }

        // 2. 等待停止完成
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // 3. 执行回零操作
        bool homingSuccess = homingBed(BedType::Both);

        if (homingSuccess) {
            LOG_INFO_MSG(domain_, "心跳超时后安全回零成功");
        } else {
            LOG_ERROR_MSG(domain_, "心跳超时后安全回零失败");
        }

        return homingSuccess;

    } catch (const std::exception &e) {
        LOG_ERROR_MSG(domain_, "安全回零操作异常: %s", e.what());
        return false;
    }
}

void ServoControlManager::clearHeartbeatInfo(BedOwnerType owner) {
    std::lock_guard<std::mutex> lock(heartbeat_mutex_);

    auto it = client_heartbeats_.find(owner);
    if (it != client_heartbeats_.end()) {
        it->second.is_active = false;
        it->second.missed_count = 0;
        LOG_INFO_MSG(domain_, "已清理心跳信息，客户端: %d", static_cast<int>(owner));
    }
}

bool ServoControlManager::loadHeartbeatConfig(const ServoConfigProto::ServoConfigMessage &config) {
    if (!config.has_heartbeat_config()) {
        LOG_INFO_MSG(domain_, "配置中未找到心跳配置，使用默认值");
        return true;
    }

    const auto &hb_config = config.heartbeat_config();

    // 验证配置参数
    if (hb_config.timeout_ms() < 1000 || hb_config.timeout_ms() > 300000) {
        LOG_ERROR_MSG(domain_, "心跳超时时间无效: %dms", hb_config.timeout_ms());
        return false;
    }

    if (hb_config.check_interval_ms() < 100 || hb_config.check_interval_ms() > 10000) {
        LOG_ERROR_MSG(domain_, "心跳检查间隔无效: %dms", hb_config.check_interval_ms());
        return false;
    }

    // 应用配置
    heartbeat_config_.timeout_ms = hb_config.timeout_ms();
    heartbeat_config_.check_interval_ms = hb_config.check_interval_ms();
    heartbeat_config_.enable_auto_homing = hb_config.enable_auto_homing();
    heartbeat_config_.enable_heartbeat_monitor = hb_config.enable_heartbeat_monitor();
    heartbeat_config_.max_missed_heartbeats = hb_config.max_missed_heartbeats();

    LOG_INFO_MSG(domain_, "心跳配置已加载: 超时时间: %dms, 检查间隔: %dms, 自动回零: %s, 心跳监控: %s, 最大丢失次数: %d",
                 heartbeat_config_.timeout_ms, heartbeat_config_.check_interval_ms,
                 heartbeat_config_.enable_auto_homing ? "启用" : "禁用",
                 heartbeat_config_.enable_heartbeat_monitor ? "启用" : "禁用",
                 heartbeat_config_.max_missed_heartbeats);

    return true;
}

HeartbeatConfig ServoControlManager::getHeartbeatConfig() const {
    return heartbeat_config_;
}

// ==== 床位运动状态管理功能实现 ====

BedMotionStatus ServoControlManager::GetBedMotionStatus(BedType bedType) {
    std::lock_guard<std::mutex> lock(bed_status_mutex_);

    // 处理Both类型：返回两个床位中较"严重"的状态
    if (bedType == BedType::Both) {
        BedMotionStatus primaryStatus = bed_motion_status_[BedType::Primary];
        BedMotionStatus secondaryStatus = bed_motion_status_[BedType::Secondary];

        // 状态优先级：Error > Estop > Moving > Ready
        if (primaryStatus == BedMotionStatus::Error || secondaryStatus == BedMotionStatus::Error) {
            return BedMotionStatus::Error;
        }
        if (primaryStatus == BedMotionStatus::Estop || secondaryStatus == BedMotionStatus::Estop) {
            return BedMotionStatus::Estop;
        }
        if (primaryStatus == BedMotionStatus::Moving || secondaryStatus == BedMotionStatus::Moving) {
            return BedMotionStatus::Moving;
        }
        return BedMotionStatus::Ready;
    }

    // 单个床位状态查询
    auto it = bed_motion_status_.find(bedType);
    if (it != bed_motion_status_.end()) {
        return it->second;
    }

    LOG_ERROR_MSG(domain_, "无效的床位类型: %d", static_cast<int>(bedType));
    return BedMotionStatus::Error;
}

bool ServoControlManager::SetBedMotionStatus(BedType bedType, BedMotionStatus newStatus, const std::string& reason) {
    std::lock_guard<std::mutex> lock(bed_status_mutex_);

    // 不支持Both类型的状态设置
    if (bedType == BedType::Both) {
        LOG_ERROR_MSG(domain_, "不支持对Both类型床位设置状态");
        return false;
    }

    // 获取当前状态
    auto it = bed_motion_status_.find(bedType);
    if (it == bed_motion_status_.end()) {
        LOG_ERROR_MSG(domain_, "无效的床位类型: %d", static_cast<int>(bedType));
        return false;
    }

    BedMotionStatus currentStatus = it->second;

    // 如果状态没有变化，直接返回
    if (currentStatus == newStatus) {
        return true;
    }

    // 验证状态转换是否合理
    if (!IsValidStatusTransition(currentStatus, newStatus)) {
        LOG_ERROR_MSG(domain_, "无效的状态转换 - 床位: %s, 从 %s 到 %s, 原因: %s",
                      GetBedTypeName(bedType), GetStatusName(currentStatus),
                      GetStatusName(newStatus), reason.c_str());
        return false;
    }

    // 更新状态
    it->second = newStatus;

    // 记录状态变化日志
    LOG_INFO_MSG(domain_, "床位状态变化 - 床位: %s, 从 %s 变为 %s, 原因: %s",
                 GetBedTypeName(bedType), GetStatusName(currentStatus),
                 GetStatusName(newStatus), reason.c_str());

    return true;
}

bool ServoControlManager::IsValidStatusTransition(BedMotionStatus currentStatus, BedMotionStatus newStatus) {
    // 定义有效的状态转换规则
    switch (currentStatus) {
        case BedMotionStatus::Ready:
            // Ready可以转换到：Moving, Estop, Error
            return (newStatus == BedMotionStatus::Moving ||
                    newStatus == BedMotionStatus::Estop ||
                    newStatus == BedMotionStatus::Error);

        case BedMotionStatus::Moving:
            // Moving可以转换到：Ready, Estop, Error
            return (newStatus == BedMotionStatus::Ready ||
                    newStatus == BedMotionStatus::Estop ||
                    newStatus == BedMotionStatus::Error);

        case BedMotionStatus::Estop:
            // Estop可以转换到：Ready, Error
            return (newStatus == BedMotionStatus::Ready ||
                    newStatus == BedMotionStatus::Error);

        case BedMotionStatus::Error:
            // Error可以转换到：Ready（恢复）
            return (newStatus == BedMotionStatus::Ready);

        default:
            return false;
    }
}

const char* ServoControlManager::GetStatusName(BedMotionStatus status) {
    switch (status) {
        case BedMotionStatus::Ready:  return "Ready";
        case BedMotionStatus::Moving: return "Moving";
        case BedMotionStatus::Estop:  return "Estop";
        case BedMotionStatus::Error:  return "Error";
        default:                      return "Unknown";
    }
}

const char* ServoControlManager::GetBedTypeName(BedType bedType) {
    switch (bedType) {
        case BedType::Primary:   return "Primary";
        case BedType::Secondary: return "Secondary";
        case BedType::Both:      return "Both";
        default:                 return "Unknown";
    }
}

BedType ServoControlManager::nodeIdToBedType(uint8_t nodeId) {
    if (nodeId == primary_bed_node_id_) {
        return BedType::Primary;
    } else if (nodeId == secondary_bed_node_id_) {
        return BedType::Secondary;
    } else {
        LOG_ERROR_MSG(domain_, "无法识别的节点ID: %d", static_cast<int>(nodeId));
        return BedType::Primary; // 默认返回Primary，避免未定义行为
    }
}

