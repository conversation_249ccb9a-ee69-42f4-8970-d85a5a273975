#ifndef SERVO_CONTROL_MANAGER_HPP
#define SERVO_CONTROL_MANAGER_HPP

#include <memory>
#include <vector>
#include <map>
#include <string>
#include <mutex>
#include <functional>
#include <thread>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include "CANopenMaster.hpp"
#include "ServoConfigParameters.pb.h"
#include "motor_control.pb.h"

// 伺服电机类型
enum class ServoMotorType {
    GENERIC,       // 通用伺服电机
    COPLEY_FIRST,  // 一级床伺服,
    COPLEY_SECOND  // 二级床伺服
};

// 床控制所有者类型（与proto中定义对应）
enum class BedOwnerType {
    CT = 0,     // CT控制
    PET = 1,    // PET控制
    SPECT = 2,  // SPECT控制
    NONE = 3    // 无控制
};

// 心跳配置结构体
struct HeartbeatConfig {
    uint32_t timeout_ms;              // 心跳超时时间（毫秒）
    uint32_t check_interval_ms;       // 检查间隔（毫秒）
    bool enable_auto_homing;          // 是否启用自动回零
    bool enable_heartbeat_monitor;    // 是否启用心跳监控
    uint32_t max_missed_heartbeats;   // 最大允许丢失心跳次数
};

// 客户端心跳信息
struct ClientHeartbeatInfo {
    BedOwnerType owner_type;
    std::chrono::steady_clock::time_point last_heartbeat;
    bool is_active;
    uint32_t missed_count;            // 连续丢失心跳次数
};

// 伺服电机位置模式配置
struct PositionModeConfig {
    int32_t profileVelocity;       // 轮廓速度
    int32_t profileAcceleration;   // 轮廓加速度
    int32_t profileDeceleration;   // 轮廓减速度
    bool useLimitSwitches;         // 是否使用限位开关
    int32_t positiveLimit;         // 正向软限位
    int32_t negativeLimit;         // 负向软限位
    uint32_t positionTrackingWindow;
    uint32_t positionTrackingWindowTime;
};

// 伺服电机速度模式配置
struct VelocityModeConfig {
    int32_t profileAcceleration;   // 轮廓加速度
    int32_t profileDeceleration;   // 轮廓减速度
    int32_t maxVelocity;           // 最大速度
    int32_t minVelocity;           // 最小速度
};

// 伺服电机回零配置
struct HomingConfig {
    int8_t homingMethod;          // 回零方法
    int32_t homingSpeed_switch;    // 寻找开关的速度,fastSpeed
    int32_t homingSpeed_zero;      // 寻找零点的速度,slowSpeed
    int32_t homingAcceleration;    // 回零加速度
    int32_t homeOffset;            // 回零偏移量
};

//  输入IO引脚配置
struct InputPinConfig {
    uint8_t pinIndex;
    uint16_t configuration;
    uint16_t debounceValues;
};

//  探针操作配置
struct TouchProbeConfig {
    uint8_t touchProbeIndex;
    uint16_t function;
    int16_t ioSelect;
    uint16_t debounceValues;
};

// PDO映射条目配置
struct PDOMappingEntryConfig {
    uint32_t objectIndex;    // 对象字典索引（如0x6041）
    uint32_t subIndex;       // 子索引
    uint32_t dataLength;     // 数据长度（位数）
};

// TPDO配置
struct TPDOConfigEntry {
    uint32_t pdoNumber;                                    // TPDO编号（1, 2, 3...）
    uint32_t transmissionType;                             // 传输类型
    std::vector<PDOMappingEntryConfig> mappingEntries;     // PDO映射条目数组
};

// 伺服电机配置
struct ServoConfig {
    uint8_t nodeId;                // CANopen节点ID
    ServoMotorType type;           // 电机类型
    std::string name;              // 电机名称
    int32_t encoderResolution;     // 编码器分辨率(每转脉冲数)
    PositionModeConfig positionConfig;  // 位置模式配置
    VelocityModeConfig velocityConfig;  // 速度模式配置
    HomingConfig homingConfig;          // 回零配置
    std::vector<InputPinConfig> inputPinConfig;      // IO配置
    std::vector<TouchProbeConfig> touchProbeConfig;  // 探针配置
    std::vector<TPDOConfigEntry> tpdoConfig;         // TPDO配置
};

// 伺服电机状态
struct ServoMotorStatus {
    uint8_t nodeId;                // 节点ID
    DS402State state;              // 状态机状态
    DS402OperationMode mode;       // 当前操作模式
    int32_t actualPosition;        // 实际位置
    int32_t actualVelocity;        // 实际速度
    uint16_t statusWord;           // 状态字
    uint16_t ioStatus;             // IO状态
    bool isTargetReached;          // 是否到达目标
    bool hasFault;                 // 是否有故障
    uint32_t errorCode;            // 错误代码
};

// 运动信息结构体
struct MotionInfo {
    float position;                // 位置
    float velocity;                // 速度
};

// 动物床运动状态
enum class BedMotionStatus {
    Ready = 0,   // 就绪
    Moving = 1,  // 运动中
    Estop = 2,   // 紧急停止
    Error = 3    // 错误
};

// 动物床类型
enum class BedType {
    Primary = 0,    // 一级床
    Secondary = 1,  // 二级床
    Both = 2        // 两张床
};

// 动物床状态结构体
struct BedStatus {
    BedMotionStatus status;        // 运动状态
    MotionInfo motionInfo;         // 运动信息
    float positionMin;             // 位置最小值
    float positionMax;             // 位置最大值
    float velocityMin;             // 速度最小值
    float velocityMax;             // 速度最大值
    float accelerationMax;         // 加速度最大值
    float decelerationMax;         // 减速度最大值
};

// PostId信息结构体
struct PostIdInfo {
    uint32_t vid;                  // 厂商ID
    uint32_t did;                  // 设备ID
    uint32_t hwid;                 // 硬件ID
    uint32_t rid;                  // 区域ID
};

// Trigger信息管理
struct TriggerInfo {
    int32_t position;
    uint32_t timestamp;
    uint64_t triggerTimestamp;
    int32_t interval;
    bool isValid;
};

class ServoControlManager {
public:
    // 超时配置常量
    //  todo: 超时时间确认
    static constexpr int DEFAULT_POSITION_TIMEOUT_MS = 100000;  // 位置控制默认超时10秒
    static constexpr int DEFAULT_VELOCITY_TIMEOUT_MS = 10000;   // 速度控制默认超时8秒
    static constexpr int DEFAULT_HOMING_TIMEOUT_MS = 60000;    // 回零操作默认超时30秒
    static constexpr int DEFAULT_VELOCITY_TOLERANCE = 10000;     // 速度容差默认值
    static constexpr int DEFAULT_POSITION_TOLERANCE = 20;     // 速度容差默认值
    static constexpr int DEFAULT_OPERATION_INTERVAL_MS = 100;  // 操作间隔默认值
    static constexpr int GEAR_RATIO = 1000; //  电子齿比， 1mm -> 1000counts
    static constexpr int VELOCITY_FACTOR = 10; //  速度及加减速度转换系数

    // 构造函数和析构函数
    explicit ServoControlManager(CANopenMaster* master);

    ~ServoControlManager();
    
    // 添加伺服电机
    bool addServoMotor(const ServoConfig& config);
    
    // 按照标准流程初始化节点
    bool initializeNode(uint8_t nodeId);
    
    // 启动ServoManager，初始化所有节点
    virtual bool startManager(const std::string& configFile);
    
    // 获取伺服电机配置
    ServoConfig getServoConfig(uint8_t nodeId) const;
    
    // 控制电机开关
    bool enableMotor(uint8_t nodeId);
    bool disableMotor(uint8_t nodeId);

    // NMT
    bool enterNmtOperational(uint8_t nodeId);
    bool resetNmtState(uint8_t nodeId);
    
    // 设置操作模式
    bool setOperationMode(uint8_t nodeId, DS402OperationMode mode);
    
    // 位置模式
    virtual bool moveToPosition(uint8_t nodeId, int32_t position, int32_t velocity, bool absolute, bool immediate);

    // 速度模式
    virtual bool moveWithVelocity(uint8_t nodeId, int32_t velocity, int32_t position);
    
    // 回零操作
    virtual bool moveWithHoming(uint8_t nodeId);
    
    // 紧急停止
    virtual bool emergencyStop(uint8_t nodeId);
    
    // 清除故障
    bool clearFault(uint8_t nodeId);
    
    // 等待位置到达
    bool waitForTargetReached(uint8_t nodeId, int32_t targetPosition, int32_t tolerance, int timeout_ms);

    // 等待速度模式完成
    bool waitForTargetVelocity(uint8_t nodeId, int32_t targetVelocity, int32_t targetPosition, int timeout_ms = 5000, int32_t tolerance = 100);

    bool waitForVelocityReached(uint8_t nodeId, int32_t targetVelocity, int32_t tolerance, uint32_t timeout);

    // 等待回零完成
    bool waitForHomingComplete(uint8_t nodeId, int timeout_ms = 30000);


    // 配置探针操作(用于一级床接收Trigger信号)
    bool configTouchProbe(uint8_t nodeId);

    // 配置IO操作
    bool configInputPin(uint8_t nodeId);

    // 配置TPDO
    bool configTPDO(uint8_t nodeId);

    // 配置默认TPDO（向后兼容）
    bool configTPDODefault(uint8_t nodeId);

    // ==== 动物床控制相关功能 ====

    // 开始移动床
    virtual bool startMoveBed(BedType bedType, motor_control::MotionMode mode, float position, float velocity, BedOwnerType requiredOwner);

    // 停止移动床
    virtual bool stopMoveBed(BedType bedType, BedOwnerType requiredOwner);

    virtual bool homingBed(BedType bedType);

    // 获取床状态
    virtual BedStatus getBedStatus(BedType bedType);

    // ==== 床位运动状态管理功能 ====

    // 获取床位运动状态
    BedMotionStatus GetBedMotionStatus(BedType bedType);

    ServoMotorStatus getMotorStatus(uint8_t nodeId);

    std::map<uint8_t, ServoMotorStatus> getAllMotorStatus();

    // 获取床PostId
    virtual PostIdInfo getPostId(BedType bedType);

    // 获取/设置床控制权
    BedOwnerType getBedOwner();

    bool gainBedOwner(BedOwnerType owner);

    bool releaseBedOwner(BedOwnerType owner);

    // ==== 业务逻辑控制方法 ====

    // 权限检查
    bool checkBedControlPermission(BedOwnerType requiredOwner, const std::string& operation);

    // 运动干涉检查
    bool checkMotionInterference(BedType bedType, float targetPosition, float targetVelocity);

    // StopFlag管理
    void setStopFlag(bool value);

    bool getStopFlag() const;

    // ==== 心跳检测相关功能 ====

    // 更新客户端心跳
    virtual bool updateHeartbeat(BedOwnerType owner);

    // 检查心跳是否有效
    virtual bool isHeartbeatValid(BedOwnerType owner);

    // 启动心跳监控
    virtual bool startHeartbeatMonitor();

    // 停止心跳监控
    virtual void stopHeartbeatMonitor();

    // 加载心跳配置
    virtual bool loadHeartbeatConfig(const ServoConfigProto::ServoConfigMessage& config);

    // 获取心跳配置
    virtual HeartbeatConfig getHeartbeatConfig() const;

    // ==== trigger ====
    bool setTriggerInfo(int32_t position, uint32_t timestamp);

    TriggerInfo getCurrentTriggerInfo();

    virtual bool waitForTriggerInfoChange(TriggerInfo& lastKnownInfo, int timeout_ms = 5000);

protected:
    
    // 监测电机状态的功能
    bool updateMotorStatus(uint8_t nodeId);

    // 床类型到节点ID映射
    uint8_t bedTypeToNodeId(BedType bedType);
    
    // 单位转换辅助函数
    static int32_t positionToTicks(float position);
    static int32_t velocityToTicks(float velocity);
    static float ticksToPosition(int32_t ticks);
    static float ticksToVelocity(int32_t ticks);
    
    // 检查参数是否在有效范围内
    static bool isValidPosition(BedType bedType, float position);
    static bool isValidVelocity(BedType bedType, float velocity);

    // 床控制辅助方法
    bool validateSingleBedParameters(BedType bedType, float position, float velocity);
    bool executeBedOperationForBoth(std::function<bool(BedType)> operation, const std::string& operationName, bool allowPartialSuccess = false);
    bool executeBedOperationSequentially(const std::vector<BedType>& bedTypes, std::function<bool(BedType)> operation, const std::string& operationName, int intervalMs = 100);
    bool executeBedStopOperationForBoth(std::function<bool(BedType)> operation, const std::string& operationName);

    // 错误处理和清理方法
    bool handleOperationTimeout(uint8_t nodeId, const std::string& operation);
    bool performEmergencyCleanup(uint8_t nodeId, const std::string& operation);
    void logOperationError(uint8_t nodeId, const std::string& operation, const std::string& error);

    // 启动状态监控线程
    void startStatusMonitorThread();

    //  TPDO回调函数
    void handleTPDO1Data(uint8_t nodeId, const uint8_t* data, size_t length);
    void handleTPDO2Data(uint8_t nodeId, const uint8_t* data, size_t length);
    void handleTPDO3Data(uint8_t nodeId, const uint8_t* data, size_t length);
    void handleTouchProbeData(uint8_t nodeId, const uint8_t* data, size_t length);   //   TouchProbe

private:
    CANopenMaster* canopen_master_;           // CANopen主站对象
    std::map<uint8_t, ServoConfig> motors_;   // 电机配置映射表
    mutable std::mutex mutex_;                // 互斥锁
    std::atomic<BedOwnerType> bed_owner_;     // 当前床控制权归属
    std::thread statusMonitorThread_;         // 状态监控线程
    std::atomic<bool> isMonitorRunning_;      // 监控线程运行标志

    // 业务逻辑控制相关
    std::atomic<bool> stop_flag_;             // 停止标志
    mutable std::mutex trigger_mutex_;        // Trigger信息互斥锁
    TriggerInfo current_trigger_info_{};        // 当前Trigger信息
    std::condition_variable trigger_info_changed_; // Trigger信息变化条件变量

    std::map<uint8_t, ServoMotorStatus> motor_status_{};
    mutable std::mutex status_mutex_ {};

    // 床位节点ID存储（简单方案）
    uint8_t primary_bed_node_id_{0x06};    // 一级床节点ID，默认值0x06
    uint8_t secondary_bed_node_id_{0x0a};  // 二级床节点ID，默认值0x0a

    // 床位运动状态管理
    std::map<BedType, BedMotionStatus> bed_motion_status_;  // 床位运动状态存储
    mutable std::mutex bed_status_mutex_;                   // 床位状态互斥锁

    // 心跳检测相关成员变量
    HeartbeatConfig heartbeat_config_;
    std::map<BedOwnerType, ClientHeartbeatInfo> client_heartbeats_;
    std::thread heartbeat_monitor_thread_;
    std::atomic<bool> is_heartbeat_monitor_running_;
    mutable std::mutex heartbeat_mutex_;

    const std::string domain_{"ServoControlManager"};

private:
    // 心跳监控线程函数
    void heartbeatMonitorThreadFunction();

    // 处理心跳超时
    bool handleHeartbeatTimeout(BedOwnerType owner);

    // 执行安全回零操作
    bool performSafeHomingOnTimeout(BedOwnerType owner);

    // 清理心跳信息
    void clearHeartbeatInfo(BedOwnerType owner);

    // ==== 床位状态管理私有方法 ====

    // 设置床位运动状态（内部使用）
    bool SetBedMotionStatus(BedType bedType, BedMotionStatus newStatus, const std::string& reason);

    // 验证状态切换是否合理
    bool IsValidStatusTransition(BedMotionStatus currentStatus, BedMotionStatus newStatus);

    // 获取状态名称字符串（用于日志）
    const char* GetStatusName(BedMotionStatus status);

    // 获取床位类型名称字符串（用于日志）
    const char* GetBedTypeName(BedType bedType);

    // 根据节点ID获取床位类型
    BedType nodeIdToBedType(uint8_t nodeId);
};

#endif // SERVO_CONTROL_MANAGER_HPP 