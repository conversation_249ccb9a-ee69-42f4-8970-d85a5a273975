cmake_minimum_required(VERSION 3.10)

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CROSSCOMPILING true)

set (_gRPC_CPP_PLUGIN "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/bin/grpc_cpp_plugin")
set(_gRPC_PROTOBUF_PROTOC_EXECUTABLE "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/bin/protoc")

if(${CMAKE_CROSSCOMPILING})
    message("Ready to execute cross-compiling!")

    # 根据交叉编译链文件来设置编译链
    set(CMAKE_TOOLCHAIN_FILE  "${CMAKE_SOURCE_DIR}/toolchain.cmake")
    message("Toolchain file path: ${CMAKE_TOOLCHAIN_FILE}")

    # 设置arm包搜索路径
    set(GRPC_INSTALL_PATH_ARM "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_arm")
    set(absl_DIR "${GRPC_INSTALL_PATH_ARM}/lib/cmake/absl")
    set(Protobuf_DIR "${GRPC_INSTALL_PATH_ARM}/lib/cmake/protobuf")
    set(gRPC_DIR "${GRPC_INSTALL_PATH_ARM}/lib/cmake/grpc")

    project(CANopenMasterServerArm CXX C)

else()
    message("Ready to execute x86-compiling!")

    # 设置x86包搜索路径
    set(GRPC_INSTALL_PATH_X86 "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/")
    set(absl_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/absl")
    set(Protobuf_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/protobuf")
    set(gRPC_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/grpc")

    # 项目名称
    project(CANopenMasterServerX86 CXX C)
endif()


# 编译选项
add_compile_options(-Wextra -Wno-dev -g)
add_compile_options(-Wno-comment -Wno-type-limits -Wno-sign-compare -Wno-unused-parameter -Wno-write-strings -Wno-missing-field-initializers)

# 查找线程库
find_package(Threads REQUIRED)

# 包含头文件目录
set(CANOPENNODE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/CANopen/CANopenNode)
set(GRPC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/Grpc/)
set(CANOPENMASTER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/CANopen/CANopenMaster)
set(SERVOMOTOR_CONTROL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Business/ServoControl)
set(LOG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/Utils)
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CANOPENNODE_DIR}
    ${CANOPENNODE_DIR}/301
    ${CANOPENNODE_DIR}/303
    ${CANOPENNODE_DIR}/304
    ${CANOPENNODE_DIR}/305
    ${CANOPENNODE_DIR}/309
    ${CANOPENNODE_DIR}/extra
    ${CANOPENNODE_DIR}/storage
    ${CANOPENNODE_DIR}/linux
    ${CANOPENNODE_DIR}/od
    ${GRPC_DIR}
    ${CANOPENMASTER_DIR}
    ${SERVOMOTOR_CONTROL_DIR}
    ${LOG_DIR}
)

# 查找gRPC和Protobuf
set(protobuf_MODULE_COMPATIBLE TRUE)
find_package(Protobuf CONFIG REQUIRED)
message(STATUS "Using protobuf ${Protobuf_VERSION}")
set(_PROTOBUF_LIBPROTOBUF protobuf::libprotobuf)
set(_REFLECTION gRPC::grpc++_reflection)

find_package(gRPC CONFIG REQUIRED)
message(STATUS "Using gRPC ${gRPC_VERSION}")
set(_GRPC_GRPCPP gRPC::grpc++)

# 设置Proto文件
set(PROTO_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/Protobuf/ProtobufSource)
file(GLOB_RECURSE PROTO_SOURCE_FILES LIST_DIRECTORIES false "${PROTO_SOURCE_DIR}/*.proto")
message("Protobuf Sources: ")
foreach(item ${PROTO_SOURCE_FILES})
    message(${item})
endforeach()

# 生成Proto和gRPC文件
set(PROTO_GEN_DIR ${CMAKE_CURRENT_SOURCE_DIR}/Common/Protobuf/ProtobufGenerate)
file(MAKE_DIRECTORY ${PROTO_GEN_DIR})

# 使用protoc和grpc_cpp_plugin生成代码
set(PROTO_SRCS "")
set(PROTO_HDRS "")
set(GRPC_SRCS "")
set(GRPC_HDRS "")
foreach(proto ${PROTO_SOURCE_FILES})
    get_filename_component(proto_name ${proto} NAME_WE)
    get_filename_component(PROTO_SOURCE_PATH "${proto}" PATH) #   根据proto文件绝对路径获取当前文件夹路径
    
    # 生成protobuf文件
    set(proto_out_file "${PROTO_GEN_DIR}/${proto_name}.pb.cc")
    set(proto_out_header "${PROTO_GEN_DIR}/${proto_name}.pb.h")
    list(APPEND PROTO_SRCS ${proto_out_file})
    list(APPEND PROTO_HDRS ${proto_out_header})
    
    # 生成gRPC文件
    set(grpc_out_file "${PROTO_GEN_DIR}/${proto_name}.grpc.pb.cc")
    set(grpc_out_header "${PROTO_GEN_DIR}/${proto_name}.grpc.pb.h")
    list(APPEND GRPC_SRCS ${grpc_out_file})
    list(APPEND GRPC_HDRS ${grpc_out_header})

    add_custom_command(
            OUTPUT ${proto_out_file} ${proto_out_header} ${grpc_out_file} ${grpc_out_header}
            COMMAND ${_gRPC_PROTOBUF_PROTOC_EXECUTABLE}
            ARGS --grpc_out "${PROTO_GEN_DIR}"
            --cpp_out "${PROTO_GEN_DIR}"
            -I ${PROTO_SOURCE_PATH}
            --plugin=protoc-gen-grpc="${_gRPC_CPP_PLUGIN}"
            "${proto}"
            DEPENDS "${proto}"
            COMMENT "Running C++ protocol buffer compiler on ${proto}"
    )
endforeach()

# 添加生成的源文件目录到包含路径
include_directories(${PROTO_GEN_DIR})

# CANopenNode源文件
file(GLOB_RECURSE CANOPEN_NODE_SOURCES LIST_DIRECTORIES false "${CANOPENNODE_DIR}/*.c")     # "LIST_DIRECTORIES false" 只包含文件，不包含目录
list(FILTER CANOPEN_NODE_SOURCES EXCLUDE REGEX ".*/example/.*")
list(REMOVE_ITEM CANOPEN_NODE_SOURCES
        "${CANOPENNODE_DIR}/301/CO_Node_Guarding.c"
        "${CANOPENNODE_DIR}/linux/CO_main_basic.c"
        "${CANOPENNODE_DIR}/storage/CO_storageEeprom.c"
)
message("CANopenNode Sources: ")
foreach(item ${CANOPEN_NODE_SOURCES})
    message(${item})
endforeach()

# CPP源文件
file(GLOB_RECURSE ALL_CPP_SOURCES LIST_DIRECTORIES false "*.cpp")
list(FILTER ALL_CPP_SOURCES EXCLUDE REGEX ".*/cmake-build.*/.*")
list(FILTER ALL_CPP_SOURCES EXCLUDE REGEX ".*/build/.*")
list(FILTER ALL_CPP_SOURCES EXCLUDE REGEX ".*/Tests/.*")
message("Cpp Sources: ")
foreach(item ${ALL_CPP_SOURCES})
    message(${item})
endforeach()

# 主应用源文件
set(MASTER_SOURCES
    ${CANOPEN_NODE_SOURCES}
    ${ALL_CPP_SOURCES}
    ${PROTO_SRCS}
    ${PROTO_HDRS}
    ${GRPC_SRCS}
    ${GRPC_HDRS}
)

# 编译Canopennode静态库以供单元测试程序使用
#add_library(canopennode STATIC ${CANOPEN_NODE_SOURCES})

# 创建可执行文件
add_executable(${PROJECT_NAME} ${MASTER_SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Threads::Threads
    ${PROTOBUF_LIBRARIES}
    gRPC::grpc++
    gRPC::grpc++_reflection
)

