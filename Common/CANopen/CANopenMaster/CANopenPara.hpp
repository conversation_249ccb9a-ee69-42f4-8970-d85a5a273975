#ifndef CAMOPEN_PARA_HPP
#define CAMOPEN_PARA_HPP


// NMT命令常量
#define NMT_ENTER_OPERATIONAL      0x01
#define NMT_ENTER_STOPPED          0x02
#define NMT_ENTER_PRE_OPERATIONAL  0x80
#define NMT_RESET_NODE             0x81
#define NMT_RESET_COMMUNICATION    0x82

// PDO映射对象长度定义（单位：bit）
#define OD_MAP_LEN_1_BIT       0x0001
#define OD_MAP_LEN_8_BIT       0x0008
#define OD_MAP_LEN_16_BIT      0x0010
#define OD_MAP_LEN_32_BIT      0x0020
#define OD_MAP_LEN_64_BIT      0x0040

// DS402对象字典索引
#define OD_DEVICE_TYPE              0x1000
#define OD_HEARTBEAT_TIME           0x1017
#define OD_CONTROL_WORD             0x6040
#define OD_STATUS_WORD              0x6041
#define OD_OPERATION_MODE           0x6060
#define OD_OPERATION_MODE_DISP      0x6061
#define OD_TARGET_POSITION          0x607A
#define OD_TARGET_VELOCITY          0x60FF
#define OD_PROFILE_VELOCITY         0x6081
#define OD_TARGET_TORQUE            0x6071
#define OD_ACTUAL_POSITION          0x6064
#define OD_ACTUAL_VELOCITY          0x6069
#define OD_ACTUAL_TORQUE            0x6077
#define OD_PROFILE_ACCELERATION     0x6083
#define OD_PROFILE_DECELERATION     0x6084
#define OD_QUICK_STOP_DECELERATION  0x6085
#define OD_MOTION_PROFILE_TYPE      0x6086
#define OD_POSITION_TRACKING_WINDOW          0x6067
#define OD_POSITION_TRACKING_WINDOW_TIME              0x6068

#define OD_TOUCH_PROBE_POSITION     0x60BA
#define OD_TOUCH_PROBE_TIME         0x60D1
#define OD_TOUCH_PROBE_FUNCTION       0x60B8
#define OD_TOUCH_PROBE_SELECT         0x60D0
#define OD_TOUCH_PROBE_POS_1_POS_VALUE         0x60BA
#define OD_TOUCH_PROBE_POS_1_NEG_VALUE         0x60BB
#define OD_TOUCH_PROBE_POS_2_POS_VALUE         0x60BC
#define OD_TOUCH_PROBE_POS_2_NEG_VALUE         0x60B
#define OD_TOUCH_PROBE_TIME_1_POS_VALUE         0x60D1
#define OD_TOUCH_PROBE_TIME_1_NEG_VALUE         0x60D2
#define OD_TOUCH_PROBE_TIME_2_POS_VALUE         0x60D3
#define OD_TOUCH_PROBE_TIME_2_NEG_VALUE         0x60D4

#define OD_INPUT_PIN_STATUS         0x2190
#define OD_PIN_CONFIGURATION        0x2192
#define OD_INPUT_PIN_DEBOUNCE_VALUES           0x2195

#define OD_PHASING_MODE             0x21C0
#define OD_FAULT_MASK               0x2182  //  使能故障检测

#define OD_HOMING_METHOD           0x6098
#define OD_HOMING_SPEEDS           0x6099
#define OD_HOMING_ACCELERATION     0x609A
#define OD_HOMING_OFFSET           0x607C


// PDO通信参数索引
#define OD_RPDO1_PARAM         0x1400
#define OD_RPDO2_PARAM         0x1401
#define OD_RPDO3_PARAM         0x1402
#define OD_RPDO4_PARAM         0x1403
#define OD_TPDO1_PARAM         0x1800
#define OD_TPDO2_PARAM         0x1801
#define OD_TPDO3_PARAM         0x1802
#define OD_TPDO4_PARAM         0x1803

// PDO映射参数索引
#define OD_RPDO1_MAPPING       0x1600
#define OD_RPDO2_MAPPING       0x1601
#define OD_RPDO3_MAPPING       0x1602
#define OD_RPDO4_MAPPING       0x1603
#define OD_TPDO1_MAPPING       0x1A00
#define OD_TPDO2_MAPPING       0x1A01
#define OD_TPDO3_MAPPING       0x1A02
#define OD_TPDO4_MAPPING       0x1A03

// PDO通信参数子索引
#define OD_PDO_SUBIDX_COUNT    0x00
#define OD_PDO_SUBIDX_COB_ID   0x01
#define OD_PDO_SUBIDX_TRANSMISSION_TYPE 0x02
#define OD_PDO_SUBIDX_INHIBIT_TIME 0x03
#define OD_PDO_SUBIDX_EVENT_TIMER 0x05

// PDO映射对象长度定义（单位：bit）
#define OD_MAP_LEN_1_BIT       0x0001
#define OD_MAP_LEN_8_BIT       0x0008
#define OD_MAP_LEN_16_BIT      0x0010
#define OD_MAP_LEN_32_BIT      0x0020
#define OD_MAP_LEN_64_BIT      0x0040

// PDO传输类型定义
#define OD_PDO_TRANS_SYNC_ACYCLIC     0x00  // 同步, 非周期性
#define OD_PDO_TRANS_SYNC_1           0x01  // 同步, 每1个SYNC
#define OD_PDO_TRANS_SYNC_2           0x02  // 同步, 每2个SYNC
#define OD_PDO_TRANS_SYNC_CYCLIC_MIN  0x01  // 同步最小周期
#define OD_PDO_TRANS_SYNC_CYCLIC_MAX  0xF0  // 同步最大周期
#define OD_PDO_TRANS_EVENT_SPECIFIC   0xFE  // 制造商指定事件触发
#define OD_PDO_TRANS_EVENT_PROFILE    0xFF  // 设备配置文件事件触发

// DS402状态字掩码
#define DS402_SW_NOT_READY_TO_SWITCH_ON    0x0000
#define DS402_SW_SWITCH_ON_DISABLED       (1<<6)
#define DS402_SW_READY_TO_SWITCH_ON       ((1<<0) | (1<<6))
#define DS402_SW_SWITCHED_ON              ((1<<0) | (1<<1))
#define DS402_SW_OPERATION_ENABLED        ((1<<0) | (1<<1) | (1<<2) | (1<<4) | (1<<5))  // 0x0037
#define DS402_SW_QUICK_STOP_ACTIVE        ((1<<5))
#define DS402_SW_FAULT_REACTION_ACTIVE    ((1<<0) | (1<<1) | (1<<2) | (1<<3))
#define DS402_SW_FAULT                    ((1<<3))
#define DS402_SW_TARGET_REACHED           ((1<<10))
#define DS402_SW_HOMING_ATTAINED          ((1<<12))
#define DS402_SW_HOMING_ERROR             ((1<<13))
#define DS402_SW_POSITION_FOLLOWING_ERROR       ((1<<13))
#define DS402_SW_VELOCITY_MAXIMUM_SLIPPAGER_ERROR ((1<<13))

// DS402控制字命令掩码
#define DS402_CW_SHUTDOWN                 ((1<<1) | (1<<2))
#define DS402_CW_SWITCH_ON                ((1<<0) | (1<<1) | (1<<2))
#define DS402_CW_DISABLE_VOLTAGE          (0)
#define DS402_CW_QUICK_STOP               ((1<<1))
#define DS402_CW_DISABLE_OPERATION        ((1<<0) | (1<<1) | (1<<2))
#define DS402_CW_ENABLE_OPERATION         ((1<<0) | (1<<1) | (1<<2) | (1<<3))
#define DS402_CW_FAULT_RESET              ((1<<7))


// DS402状态机状态定义
enum class DS402State {
    NOT_READY_TO_SWITCH_ON = 0,
    SWITCH_ON_DISABLED,
    READY_TO_SWITCH_ON,
    SWITCHED_ON,
    OPERATION_ENABLED,
    QUICK_STOP_ACTIVE,
    FAULT_REACTION_ACTIVE,
    FAULT
};

// DS402控制字命令
enum class DS402Command {
    SHUTDOWN,
    SWITCH_ON,
    SWITCH_ON_AND_ENABLE_OPERATION,
    DISABLE_VOLTAGE,
    QUICK_STOP,
    DISABLE_OPERATION,
    ENABLE_OPERATION,
    FAULT_RESET
};

// 操作模式
enum class DS402OperationMode {
    PROFILE_POSITION = 1,
    VELOCITY = 2,
    PROFILE_VELOCITY = 3,
    PROFILE_TORQUE = 4,
    HOMING = 6,
    INTERPOLATED_POSITION = 7,
    CYCLIC_SYNC_POSITION = 8,
    CYCLIC_SYNC_VELOCITY = 9,
    CYCLIC_SYNC_TORQUE = 10
};

// PDO类型
enum class PDOType {
    RPDO,  // 接收PDO
    TPDO   // 发送PDO
};

// PDO数据结构
struct PDOData {
    uint16_t statusWord;
    int32_t actualPosition;
    int32_t actualVelocity;
    int16_t actualTorque;
    uint16_t controlWord;
    int32_t targetPosition;
    int32_t targetVelocity;
    int16_t targetTorque;
    int8_t operationMode;
    uint16_t inputPinStatus;
    uint32_t touchProbeTime;
    int32_t touchProbePosition;
    uint32_t profileAcceleration;
    uint32_t profileDeceleration;
    uint32_t profileVelocity;
    uint32_t quickStopDeceleration;
    uint16_t motionProfileType;
    uint32_t positionTrackingWindow;
    uint16_t positionTrackingWindowTime;
    uint16_t touchProbeFunction;
    uint16_t phasingMode;
    uint32_t faultMask;
    int8_t homingMethod;
    uint32_t homingSpeeds[2];  // 索引1=速度切换点，索引2=归零速度
    uint32_t homingAcceleration;
    int32_t homingOffset;
    int32_t touchProbePos1PosValue;
    int32_t touchProbePos1NegValue;
    int32_t touchProbePos2PosValue;
    int32_t touchProbePos2NegValue;
    uint32_t touchProbeTime1PosValue;
    uint32_t touchProbeTime1NegValue;
    uint32_t touchProbeTime2PosValue;
    uint32_t touchProbeTime2NegValue;
};

// PDO映射条目结构
struct PDOMappingEntry {
    uint16_t index;       // 对象字典索引
    uint8_t subIndex;     // 对象字典子索引
    uint8_t length;       // 长度（bit）
};

// PDO配置结构
struct PDOConfig {
    std::vector<PDOMappingEntry> mappingEntries;  // 映射条目
    uint8_t transmissionType;                    // 传输类型
};

enum InputPinConfigType
{
	INCFG_NONE                  = 0x0000, ///< No function assigned to the input
	INCFG_RESET_R               = 0x0002, ///< Reset the amplifier on the Rising edge of the input
	INCFG_RESET_F               = 0x0003, ///< Reset the amplifier on the Falling edge of the input
	INCFG_POSLIM_H              = 0x0004, ///< Positive limit switch, active High.
	INCFG_POSLIM_L              = 0x0005, ///< Positive limit switch, active Low.
	INCFG_NEGLIM_H              = 0x0006, ///< Negative limit switch, active High.
	INCFG_NEGLIM_L              = 0x0007, ///< Negative limit switch, active Low.
	INCFG_MOTOR_TEMP_H          = 0x0008, ///< Motor temp sensor active high
	INCFG_MOTOR_TEMP_L          = 0x0009, ///< Motor temp sensor active low
	INCFG_CLR_FAULTS_H          = 0x000A, ///< Clear faults on edge, disable while high
	INCFG_CLR_FAULTS_L          = 0x000B, ///< Clear faults on edge, disable while low
	INCFG_RESET_DISABLE_R       = 0x000C, ///< Reset on rising edge, disable while high
	INCFG_RESET_DISABLE_F       = 0x000D, ///< Reset on falling edge, disable while low
	INCFG_HOME_H                = 0x000E, ///< Home switch, active high
	INCFG_HOME_L                = 0x000F, ///< Home switch, active low
	INCFG_DISABLE_H             = 0x0010, ///< Amplifier disable active high
	INCFG_DISABLE_L             = 0x0011, ///< Amplifier disable active low
	INCFG_PWM_SYNC_H            = 0x0013, ///< Sync input on falling edge, valid only on high speed inputs
	INCFG_MOTION_ABORT_H        = 0x0014, ///< Abort motion active high
	INCFG_MOTION_ABORT_L        = 0x0015, ///< Abort motion active low
	INCFG_SCALE_ADC_H           = 0x0016, ///< Scale analog reference input by a factor of 8 when high
	INCFG_SCALE_ADC_L           = 0x0017, ///< Scale analog reference input by a factor of 8 when low
	INCFG_HIGHSPEED_CAPTURE_R   = 0x0018, ///< High speed position capture on rising edge
	INCFG_HIGHSPEED_CAPTURE_F   = 0x0019, ///< High speed position capture on falling edge
	INCFG_COUNT_EDGES_R         = 0x001A, ///< Count rising edges of input, store the results to an indexer register
	INCFG_COUNT_EDGES_F         = 0x001B, ///< Count falling edges of input, store the results to an indexer register
	INCFG_ABORT_WINDOW_R        = 0x0024, ///< Abort move on rising edge if not within N counts of destination position.
	INCFG_ABORT_WINDOW_F        = 0x0025  ///< Abort move on falling edge if not within N counts of destination position.
};

enum HomingMethod
{
	HOMING_NHARDSTOP_ONDX	 		= -4,
	HOMING_PHARDSTOP_ONDX	 	    = -3,

	HOMING_NLIM_ONDX	 			= 1,
	HOMING_PLIM_ONDX	 			= 2,
	HOMING_PHOME_ONDX    			= 3,
	HOMING_PHOME_INDX    			= 4,
	HOMING_NHOME_ONDX    			= 5,
	HOMING_NHOME_INDX    			= 6,
	HOMING_LHOME_ONDX_POS			= 7,
	HOMING_LHOME_INDX_POS			= 8,
	HOMING_UHOME_INDX_POS			= 9,
	HOMING_UHOME_ONDX_POS			= 10,
	HOMING_UHOME_ONDX_NEG			= 11,
	HOMING_UHOME_INDX_NEG			= 12,
	HOMING_LHOME_INDX_NEG			= 13,
	HOMING_LHOME_ONDX_NEG			= 14,
	HOMING_NLIM          			= 17,
	HOMING_PLIM          			= 18,
	HOMING_PHOME         			= 19,
	HOMING_NHOME         			= 21,
	KINCO_HOMING_NHOME         	    = 22,
	HOMING_LHOME_POS     			= 23,
	HOMING_UHOME_POS     			= 25,
	HOMING_UHOME_NEG     			= 27,
	HOMING_LHOME_NEG     			= 29,
	HOMING_NDX_NEG       			= 33,
	HOMING_NDX_POS       			= 34,
	HOMING_NONE          			= 35,
};

enum HomingSpeedType
{
	FAST_SPEED = 1,
	SLOW_SPEED = 2,
};



#endif