/*
 * CANopen main program file for CANopenNode on Linux.
 *
 * @file        CO_main_basic.c
 * <AUTHOR>
 * @copyright   2020 <PERSON><PERSON> Paternoster
 *
 * This file is part of <https://github.com/CANopenNode/CANopenNode>, a CANopen Stack.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is
 * distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sched.h>
#include <signal.h>
#include <errno.h>
#include <stdarg.h>
#include <syslog.h>
#include <time.h>
#include <sys/epoll.h>
#include <net/if.h>
#include <linux/reboot.h>
#include <sys/reboot.h>

#include "CANopen.h"
#include "OD.h"
#include "CO_error.h"
#include "CO_epoll_interface.h"
#include "CO_storageLinux.h"

/* Include optional external application functions */
#ifdef CO_USE_APPLICATION
#include "CO_application.h"
#endif

/* Add trace functionality for recording variables over time */
#if (CO_CONFIG_TRACE) & CO_CONFIG_TRACE_ENABLE
#include "CO_time_trace.h"
#endif

/* Interval of mainline and real-time thread in microseconds */
#ifndef MAIN_THREAD_INTERVAL_US
#define MAIN_THREAD_INTERVAL_US 100000
#endif
#ifndef TMR_THREAD_INTERVAL_US
#define TMR_THREAD_INTERVAL_US 1000000
#endif

/* default values for CO_CANopenInit() */
#ifndef NMT_CONTROL
#define NMT_CONTROL                                                                                                    \
    CO_NMT_STARTUP_TO_OPERATIONAL                                                                                      \
    | CO_NMT_ERR_ON_ERR_REG | CO_ERR_REG_GENERIC_ERR | CO_ERR_REG_COMMUNICATION
#endif
#ifndef FIRST_HB_TIME
#define FIRST_HB_TIME 500
#endif
#ifndef SDO_SRV_TIMEOUT_TIME
#define SDO_SRV_TIMEOUT_TIME 1000
#endif
#ifndef SDO_CLI_TIMEOUT_TIME
#define SDO_CLI_TIMEOUT_TIME 500
#endif
#ifndef SDO_CLI_BLOCK
#define SDO_CLI_BLOCK false
#endif
#ifndef OD_STATUS_BITS
#define OD_STATUS_BITS NULL
#endif
/* CANopen gateway enable switch for CO_epoll_processMain() */
#ifndef GATEWAY_ENABLE
#define GATEWAY_ENABLE false
#endif
/* Interval for time stamp message in milliseconds */
#ifndef TIME_STAMP_INTERVAL_MS
#define TIME_STAMP_INTERVAL_MS 10000
#endif

/* Definitions for application specific data storage objects */
#ifndef CO_STORAGE_APPLICATION
#define CO_STORAGE_APPLICATION
#endif
/* Interval for automatic data storage in microseconds */
#ifndef CO_STORAGE_AUTO_INTERVAL
#define CO_STORAGE_AUTO_INTERVAL 60000000
#endif

/* CANopen object */
CO_t *CO = NULL;

/* Active node-id, copied from pendingNodeId in the communication reset */
static uint8_t CO_activeNodeId = CO_LSS_NODE_ID_ASSIGNMENT;

/* Data block for mainline data, which can be stored to non-volatile memory */
typedef struct {
    /* Pending CAN bit rate, can be set by argument or LSS slave. */
    uint16_t pendingBitRate;
    /* Pending CANopen NodeId, can be set by argument or LSS slave. */
    uint8_t pendingNodeId;
} mainlineStorage_t;

mainlineStorage_t mlStorage = {0};

#if (CO_CONFIG_TRACE) & CO_CONFIG_TRACE_ENABLE
static CO_time_t CO_time; /* Object for current time */
#endif

/* Helper functions ***********************************************************/
#ifndef CO_SINGLE_THREAD
/* Realtime thread */
CO_epoll_t epRT;

static void *rt_thread(void *arg);

#endif

/* Signal handler */
volatile sig_atomic_t CO_endProgram = 0;

static void
sigHandler(int sig) {
    (void) sig;
    CO_endProgram = 1;
}

/* Message logging function */
void
log_printf(int priority, const char *format, ...) {
    va_list ap;

    va_start(ap, format);
    vsyslog(priority, format, ap);
    va_end(ap);

#if (CO_CONFIG_GTW) & CO_CONFIG_GTW_ASCII_LOG
    if (CO != NULL) {
        char buf[200];
        time_t timer;
        struct tm *tm_info;
        size_t len;

        timer = time(NULL);
        tm_info = localtime(&timer);
        len = strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S: ", tm_info);

        va_start(ap, format);
        vsnprintf(buf + len, sizeof(buf) - len - 2, format, ap);
        va_end(ap);
        strcat(buf, "\r\n");
        CO_GTWA_log_print(CO->gtwa, buf);
    }
#endif
}

#if (CO_CONFIG_EM) & CO_CONFIG_EM_CONSUMER

/* callback for emergency messages */
static void
EmergencyRxCallback(const uint16_t ident, const uint16_t errorCode, const uint8_t errorRegister, const uint8_t errorBit,
                    const uint32_t infoCode) {
    int16_t nodeIdRx = ident ? (ident & 0x7F) : CO_activeNodeId;

    log_printf(LOG_NOTICE, DBG_EMERGENCY_RX, nodeIdRx, errorCode, errorRegister, errorBit, infoCode);
}

#endif

#if ((CO_CONFIG_NMT) & CO_CONFIG_NMT_CALLBACK_CHANGE) || ((CO_CONFIG_HB_CONS) & CO_CONFIG_HB_CONS_CALLBACK_CHANGE)

/* return string description of NMT state. */
static char *
NmtState2Str(CO_NMT_internalState_t state) {
    switch (state) {
        case CO_NMT_INITIALIZING:
            return "initializing";
        case CO_NMT_PRE_OPERATIONAL:
            return "pre-operational";
        case CO_NMT_OPERATIONAL:
            return "operational";
        case CO_NMT_STOPPED:
            return "stopped";
        default:
            return "unknown";
    }
}

#endif

#if (CO_CONFIG_NMT) & CO_CONFIG_NMT_CALLBACK_CHANGE

/* callback for NMT change messages */
static void
NmtChangedCallback(CO_NMT_internalState_t state) {
    log_printf(LOG_NOTICE, DBG_NMT_CHANGE, NmtState2Str(state), state);
}

#endif

#if (CO_CONFIG_HB_CONS) & CO_CONFIG_HB_CONS_CALLBACK_CHANGE

/* callback for monitoring Heartbeat remote NMT state change */
static void
HeartbeatNmtChangedCallback(uint8_t nodeId, uint8_t idx, CO_NMT_internalState_t state, void *object) {
    (void) object;
    log_printf(LOG_NOTICE, DBG_HB_CONS_NMT_CHANGE, nodeId, idx, NmtState2Str(state), state);
}

#endif

/* callback for storing node id and bitrate */
static bool_t
LSScfgStoreCallback(void *object, uint8_t id, uint16_t bitRate) {
    mainlineStorage_t *mainlineStorage = object;
    mainlineStorage->pendingNodeId = id;
    mainlineStorage->pendingBitRate = bitRate;
    return true;
}

/* Print usage */
static void
printUsage(char *progName) {
    printf("Usage: %s <CAN device name> [options]\n", progName);
    printf("\n"
           "Options:\n"
           "  -i <Node ID>        CANopen Node-id (1..127) or 0xFF (LSS unconfigured).\n");
#ifndef CO_SINGLE_THREAD
    printf("  -p <RT priority>    Real-time priority of RT thread (1 .. 99). If not set or\n"
           "                      set to -1, then normal scheduler is used for RT thread.\n");
#endif
    printf("  -r                  Enable reboot on CANopen NMT reset_node command. \n");
#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE
    printf("  -s <storage path>   Path and filename prefix for data storage files.\n"
           "                      By default files are stored in current dictionary.\n");
#endif
#if (CO_CONFIG_GTW) & CO_CONFIG_GTW_ASCII
    printf("  -c <interface>      Enable command interface for master functionality.\n"
           "                      One of three types of interfaces can be specified as:\n"
           "                   1. \"stdio\" - Standard IO of a program (terminal).\n"
           "                   2. \"local-<file path>\" - Local socket interface on file\n"
           "                      path, for example \"local-/tmp/CO_command_socket\".\n"
           "                   3. \"tcp-<port>\" - Tcp socket interface on specified \n"
           "                      port, for example \"tcp-60000\".\n"
           "                      Note that this option may affect security of the CAN.\n"
           "  -T <timeout_time>   If -c is specified as local or tcp socket, then this\n"
           "                      parameter specifies socket timeout time in milliseconds.\n"
           "                      Default is 0 - no timeout on established connection.\n");
#endif
    printf("\n"
           "See also: https://github.com/CANopenNode/CANopenNode\n"
           "\n");
}


/*****************************************************SDO Interface***************************************************/
CO_SDO_abortCode_t
read_SDO(CO_SDOclient_t *SDO_C, uint8_t nodeId, uint16_t index, uint8_t subIndex, uint8_t *buf, size_t bufSize, size_t *readSize) {
    CO_SDO_return_t SDO_ret;

    // setup client (this can be skipped, if remote device don't change)
    SDO_ret = CO_SDOclient_setup(SDO_C, CO_CAN_ID_SDO_CLI + nodeId, CO_CAN_ID_SDO_SRV + nodeId, nodeId);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // initiate upload
    SDO_ret = CO_SDOclientUploadInitiate(SDO_C, index, subIndex, 1000, false);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // upload data
    do {
        uint32_t timeDifference_us = 10000;
        CO_SDO_abortCode_t abortCode = CO_SDO_AB_NONE;

        SDO_ret = CO_SDOclientUpload(SDO_C, timeDifference_us, false, &abortCode, NULL, NULL, NULL);
        if (SDO_ret < 0) {
            return abortCode;
        }

        usleep(timeDifference_us);
    } while (SDO_ret > 0);

    // copy data to the user buffer (for long data function must be called several times inside the loop)
    *readSize = CO_SDOclientUploadBufRead(SDO_C, buf, bufSize);

    return CO_SDO_AB_NONE;
}

CO_SDO_abortCode_t
write_SDO(CO_SDOclient_t *SDO_C, uint8_t nodeId, uint16_t index, uint8_t subIndex, uint8_t *data, size_t dataSize) {
    CO_SDO_return_t SDO_ret;
    bool_t bufferPartial = false;

    // setup client (this can be skipped, if remote device is the same)
    SDO_ret = CO_SDOclient_setup(SDO_C, CO_CAN_ID_SDO_CLI + nodeId, CO_CAN_ID_SDO_SRV + nodeId, nodeId);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return -1;
    }

    // initiate download
    SDO_ret = CO_SDOclientDownloadInitiate(SDO_C, index, subIndex, dataSize, 1000, false);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return -1;
    }

    // fill data
    size_t nWritten = CO_SDOclientDownloadBufWrite(SDO_C, data, dataSize);
    if (nWritten < dataSize) {
        bufferPartial = true;
        // If SDO Fifo buffer is too small, data can be refilled in the loop.
    }

    // download data
    do {
        uint32_t timeDifference_us = 10000;
        CO_SDO_abortCode_t abortCode = CO_SDO_AB_NONE;

        SDO_ret = CO_SDOclientDownload(SDO_C, timeDifference_us, false, bufferPartial, &abortCode, NULL, NULL);
        if (SDO_ret < 0) {
            return abortCode;
        }

        usleep(timeDifference_us);
    } while (SDO_ret > 0);

    return CO_SDO_AB_NONE;
}

void rpdoCallback(void* object) {
    log_printf(LOG_NOTICE, "Success in receiving rpdo message!");
}

void setLogger(char *programName) {
    /* configure system log */
    setlogmask(LOG_UPTO(LOG_DEBUG));                  /* LOG_DEBUG - log all messages */
    openlog(programName, LOG_PID | LOG_PERROR, LOG_USER); /* print also to standard error */
}

void registerSignalHandler() {
    /* Catch signals SIGINT and SIGTERM */
    if (signal(SIGINT, sigHandler) == SIG_ERR) {
        log_printf(LOG_CRIT, DBG_ERRNO, "signal(SIGINT, sigHandler)");
        exit(EXIT_FAILURE);
    }
    if (signal(SIGTERM, sigHandler) == SIG_ERR) {
        log_printf(LOG_CRIT, DBG_ERRNO, "signal(SIGTERM, sigHandler)");
        exit(EXIT_FAILURE);
    }
}


/*******************************************************************************
 * Mainline thread
 ******************************************************************************/
int main(int argc, char *argv[]) {
    int programExit = EXIT_SUCCESS;
#ifndef CO_SINGLE_THREAD
    pthread_t rt_thread_id;
    int rtPriority = -1;
#endif

    //  Set logger
    setLogger(argv[0]);


    //  Declare and check can device
    CO_CANptrSocketCan_t CANptr = {0};
    char *CANdevice = "vcan0";
    CANptr.can_ifindex = if_nametoindex(CANdevice);
    if (CANptr.can_ifindex == 0) {
        log_printf(LOG_CRIT, DBG_NO_CAN_DEVICE, CANdevice);
        exit(EXIT_FAILURE);
    }
    //  Declare NodeId
    mlStorage.pendingNodeId = 0x03;
    log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, mlStorage.pendingNodeId, "starting");

    //  Register signal handler
    registerSignalHandler();

    /* Allocate memory for CANopen objects */
    uint32_t heapMemoryUsed = 0;
    CO_config_t *config_ptr = NULL;

    CO = CO_new(config_ptr, &heapMemoryUsed);
    if (CO == NULL) {
        log_printf(LOG_CRIT, DBG_GENERAL, "CO_new(), heapMemoryUsed=", heapMemoryUsed);
        exit(EXIT_FAILURE);
    }

    // Create epoll functions
    CO_epoll_t epMain;
    CO_ReturnError_t err;
    err = CO_epoll_create(&epMain, MAIN_THREAD_INTERVAL_US);
    if (err != CO_ERROR_NO) {
        log_printf(LOG_CRIT, DBG_GENERAL, "CO_epoll_create(main), err=", err);
        exit(EXIT_FAILURE);
    }

    err = CO_epoll_create(&epRT, TMR_THREAD_INTERVAL_US);
    if (err != CO_ERROR_NO) {
        log_printf(LOG_CRIT, DBG_GENERAL, "CO_epoll_create(RT), err=", err);
        exit(EXIT_FAILURE);
    }
    CANptr.epoll_fd = epRT.epoll_fd;

    /* get current time for CO_TIME_set(), since January 1, 1984, UTC. */
    struct timespec ts;
    if (clock_gettime(CLOCK_REALTIME, &ts) == -1) {
        log_printf(LOG_CRIT, DBG_GENERAL, "clock_gettime(main)", 0);
        exit(EXIT_FAILURE);
    }
    uint16_t time_days = (uint16_t) (ts.tv_sec / (24 * 60 * 60));
    time_days -= 5113; /* difference between Unix epoch and CANopen Epoch */
    uint32_t time_ms = (uint32_t) (ts.tv_sec % (24 * 60 * 60)) * 1000;
    time_ms += ts.tv_nsec / 1000000;

    bool_t firstRun = true;
    CO_NMT_reset_cmd_t reset = CO_RESET_NOT;
    while (reset != CO_RESET_APP && reset != CO_RESET_QUIT && CO_endProgram == 0) {
        /* CANopen communication reset - initialize CANopen objects *******************/
        uint32_t errInfo;

        /* Wait rt_thread. */
        if (!firstRun) {
            CO_LOCK_OD(CO->CANmodule);
            CO->CANmodule->CANnormal = false;
            CO_UNLOCK_OD(CO->CANmodule);
        }

        /* Enter CAN configuration. */
        CO_CANsetConfigurationMode((void *) &CANptr);
        CO_CANmodule_disable(CO->CANmodule);

        /* Initialize CAN driver*/
        err = CO_CANinit(CO, (void *) &CANptr, 0 /* bit rate not used */);
        if (err != CO_ERROR_NO) {
            log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANinit()", err);
            programExit = EXIT_FAILURE;
            CO_endProgram = 1;
            continue;
        }

        CO_LSS_address_t lssAddress = {.identity = {.vendorID = OD_PERSIST_COMM.x1018_identity.vendor_ID,
                .productCode = OD_PERSIST_COMM.x1018_identity.productCode,
                .revisionNumber = OD_PERSIST_COMM.x1018_identity.revisionNumber,
                .serialNumber = OD_PERSIST_COMM.x1018_identity.serialNumber}};
        err = CO_LSSinit(CO, &lssAddress, &mlStorage.pendingNodeId, &mlStorage.pendingBitRate);
        if (err != CO_ERROR_NO) {
            log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_LSSinit()", err);
            programExit = EXIT_FAILURE;
            CO_endProgram = 1;
            continue;
        }

        CO_activeNodeId = mlStorage.pendingNodeId;
        errInfo = 0;

        //  Initialize CANopenNode except PDO objects.
        err = CO_CANopenInit(CO,                   /* CANopen object */
                             NULL,                 /* alternate NMT */
                             NULL,                 /* alternate em */
                             OD,                   /* Object dictionary */
                             OD_STATUS_BITS,       /* Optional OD_statusBits */
                             NMT_CONTROL,          /* CO_NMT_control_t */
                             FIRST_HB_TIME,        /* firstHBTime_ms */
                             SDO_SRV_TIMEOUT_TIME, /* SDOserverTimeoutTime_ms */
                             SDO_CLI_TIMEOUT_TIME, /* SDOclientTimeoutTime_ms */
                             SDO_CLI_BLOCK,        /* SDOclientBlockTransfer */
                             CO_activeNodeId, &errInfo);
        if (err != CO_ERROR_NO && err != CO_ERROR_NODE_ID_UNCONFIGURED_LSS) {
            if (err == CO_ERROR_OD_PARAMETERS) {
                log_printf(LOG_CRIT, DBG_OD_ENTRY, errInfo);
            } else {
                log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANopenInit()", err);
            }
            programExit = EXIT_FAILURE;
            CO_endProgram = 1;
            continue;
        }

        /* initialize part of threadMain and callbacks */
        CO_epoll_initCANopenMain(&epMain, CO);

        CO_LSSslave_initCfgStoreCall(CO->LSSslave, &mlStorage, LSScfgStoreCallback);
        if (!CO->nodeIdUnconfigured) {
            if (errInfo != 0) {
                CO_errorReport(CO->em, CO_EM_INCONSISTENT_OBJECT_DICT, CO_EMC_DATA_SET, errInfo);
            }
#if (CO_CONFIG_EM) & CO_CONFIG_EM_CONSUMER
            CO_EM_initCallbackRx(CO->em, EmergencyRxCallback);
#endif

#if (CO_CONFIG_NMT) & CO_CONFIG_NMT_CALLBACK_CHANGE
            CO_NMT_initCallbackChanged(CO->NMT, NmtChangedCallback);
#endif

#if (CO_CONFIG_HB_CONS) & CO_CONFIG_HB_CONS_CALLBACK_CHANGE
            CO_HBconsumer_initCallbackNmtChanged(CO->HBcons, 0, NULL, HeartbeatNmtChangedCallback);
#endif

            // CO_RPDO_initCallbackPre(CO->RPDO, NULL, rpdoCallback);



#if (CO_CONFIG_TRACE) & CO_CONFIG_TRACE_ENABLE
            /* Initialize time */
            CO_time_init(&CO_time, CO->SDO[0], &OD_time.epochTimeBaseMs, &OD_time.epochTimeOffsetMs, 0x2130);
#endif
            log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, CO_activeNodeId, "communication reset");
        } else {
            log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, CO_activeNodeId, "node-id not initialized");
        }

        /* First time only initialization. */
        if (firstRun) {
            firstRun = false;
            CO_TIME_set(CO->TIME, time_ms, time_days, TIME_STAMP_INTERVAL_MS);

            /* Create rt_thread and set priority */
            if (pthread_create(&rt_thread_id, NULL, rt_thread, NULL) != 0) {
                log_printf(LOG_CRIT, DBG_ERRNO, "pthread_create(rt_thread)");
                programExit = EXIT_FAILURE;
                CO_endProgram = 1;
                continue;
            }
            if (rtPriority > 0) {
                struct sched_param param;

                param.sched_priority = rtPriority;
                if (pthread_setschedparam(rt_thread_id, SCHED_FIFO, &param) != 0) {
                    log_printf(LOG_CRIT, DBG_ERRNO, "pthread_setschedparam()");
                    programExit = EXIT_FAILURE;
                    CO_endProgram = 1;
                    continue;
                }
            }

        } /* if(firstRun) */


        //  Initialize PDO objects.
        errInfo = 0;
        err = CO_CANopenInitPDO(CO,     /* CANopen object */
                                CO->em, /* emergency object */
                                OD,     /* Object dictionary */
                                CO_activeNodeId, &errInfo);
        if (err != CO_ERROR_NO && err != CO_ERROR_NODE_ID_UNCONFIGURED_LSS) {
            if (err == CO_ERROR_OD_PARAMETERS) {
                log_printf(LOG_CRIT, DBG_OD_ENTRY, errInfo);
            } else {
                log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANopenInitPDO()", err);
            }
            programExit = EXIT_FAILURE;
            CO_endProgram = 1;
            continue;
        }
        CO_RPDO_initCallbackPre(CO->RPDO, NULL, rpdoCallback);

        /* start CAN */
        CO_CANsetNormalMode(CO->CANmodule);

        log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, CO_activeNodeId, "running ...");
        reset = CO_RESET_NOT;
        while (reset == CO_RESET_NOT && CO_endProgram == 0) {
            /* loop for normal program execution ******************************************/
            CO_epoll_wait(&epMain);

            CO_epoll_processMain(&epMain, CO, GATEWAY_ENABLE, &reset);

            CO_epoll_processLast(&epMain);
        }
    } /* while(reset != CO_RESET_APP */

    /* program exit ***************************************************************/
    /* join threads */
    CO_endProgram = 1;
    if (pthread_join(rt_thread_id, NULL) != 0) {
        log_printf(LOG_CRIT, DBG_ERRNO, "pthread_join()");
        exit(EXIT_FAILURE);
    }


    /* delete objects from memory */
    CO_epoll_close(&epRT);
    CO_epoll_close(&epMain);
    CO_CANsetConfigurationMode((void *) &CANptr);
    CO_delete(CO);
    log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, CO_activeNodeId, "finished");

    /* Flush all buffers (and reboot) */
    if (reset == CO_RESET_APP) {
        sync();
        if (reboot(LINUX_REBOOT_CMD_RESTART) != 0) {
            log_printf(LOG_CRIT, DBG_ERRNO, "reboot()");
            exit(EXIT_FAILURE);
        }
    }
    exit(programExit);
}

#ifndef CO_SINGLE_THREAD

/*******************************************************************************
 * Realtime thread for CAN receive and threadTmr
 ******************************************************************************/
static void *rt_thread(void *arg) {
    (void) arg;

    bool isStarted = true;
    /* Endless loop */
    while (CO_endProgram == 0) {

        /**
         * Wait for an epoll event
         *
         * This function blocks until event registered on epoll: timerfd, eventfd, or application specified event. Function also
         * calculates timeDifference_us since last call and prepares timerNext_us.
         */
        CO_epoll_wait(&epRT);

        /**
         * Process CAN receive and realtime functions
         *
         * This function checks epoll for CAN receive event and processes CANopen realtime functions: @ref CO_process_SYNC(),
         * @ref CO_process_RPDO() and @ref CO_process_TPDO().  It is non-blocking and should execute cyclically. It should be
         * between @ref CO_epoll_wait() and @ref CO_epoll_processLast() functions.
         */
        CO_epoll_processRT(&epRT, CO, true);

        /**
         * Closing function for an epoll event
         *
         * This function must be called after @ref CO_epoll_wait(). Between them should be application specified processing
         * functions, which can check for own events and do own processing. Application may also lower timerNext_us variable. If
         * lowered, then interval timer will be reconfigured and @ref CO_epoll_wait() will be triggered earlier.
         *
         * @param ep This object
         */
        CO_epoll_processLast(&epRT);


        /* ****************************************Test*******************************/
        //  1. NMT
        // CO_NMT_sendCommand(CO->NMT, CO_NMT_ENTER_OPERATIONAL, 0x02);

        //  2. SDO
        // uint8_t rxBuf[8] = {};
        // uint8_t txBuf[4] = {0x01, 0x00, 0x00, 0x00};
        // size_t readSize = 0;
        // read_SDO(CO->SDOclient, 0x02, 0x1017, 0x00, rxBuf, sizeof(rxBuf), &readSize);
        // write_SDO(CO->SDOclient, 0x02, 0x1017, 0x00, txBuf, sizeof(txBuf));

        //  3. SYNC
        // CO_SYNCsend(CO->SYNC);

        //  4. 心跳报文(HeartBeat)通过配置对象字典0x1017对象来发送
        // uint16_t heartBeatTime = 0;
        // ODR_t odGetStatus = OD_get_value(OD_ENTRY_H1017_producerHeartbeatTime, 0x0, &heartBeatTime, sizeof(uint16_t), false);
        // if (odGetStatus != ODR_OK)
        //     printf("Failed to get indicated value \n");
        // else
        //     printf("Now heartbeat time is %u \n", heartBeatTime);
        //
        //
        // heartBeatTime = 1;
        // if (isStarted) {
        //     ODR_t odSetStatus = OD_set_u16(OD_ENTRY_H1017_producerHeartbeatTime, 0x0, heartBeatTime, false);
        //     if (odSetStatus != ODR_OK)
        //         printf("Failed to set indicated value \n");
        //     else
        //         printf("Now heartbeat time is %u \n", heartBeatTime);
        //
        //     isStarted = false;
        // }

        //  5. PDO, TPDO/RPDO可通过配置对象字典来发送
        //  作为主站的话，只需要配置从站的TPDO（通过SDO）及自己的RPDO


    }

    return NULL;
}

#endif
