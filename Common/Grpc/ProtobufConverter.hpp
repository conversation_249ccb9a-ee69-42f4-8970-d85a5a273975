#ifndef PROTOBUF_CONVERTER_HPP
#define PROTOBUF_CONVERTER_HPP

#include "motor_control.pb.h"
#include "ServoControlManager.hpp"
#include <map>
#include <string>

/**
 * @brief Protobuf 参数转换工具类
 * 
 * 负责在 gRPC protobuf 类型和业务层类型之间进行转换
 * 统一管理所有类型转换逻辑，避免在各个 gRPC 方法中重复代码
 */
class ProtobufConverter {
public:
    /**
     * @brief 将 gRPC HostType 转换为业务层 BedOwnerType
     * @param hostType gRPC 协议中的主机类型
     * @return 业务层的床控制权拥有者类型
     */
    static BedOwnerType convertHostType(motor_control::HostType hostType);

    /**
     * @brief 将 gRPC BedType 转换为业务层 BedType
     * @param bedType gRPC 协议中的床类型
     * @return 业务层的床类型
     */
    static BedType convertBedType(motor_control::BedType bedType);

    /**
     * @brief 将 gRPC MotionMode 转换为业务层运动模式枚举
     * @param motionMode gRPC 协议中的运动模式
     * @return 业务层的运动模式
     */
    static motor_control::MotionMode convertMotionMode(motor_control::MotionMode motionMode);

    /**
     * @brief 将业务层 BedOwnerType 转换为 gRPC HostType
     * @param ownerType 业务层的床控制权拥有者类型
     * @return gRPC 协议中的主机类型
     */
    static motor_control::HostType convertToHostType(BedOwnerType ownerType);

    /**
     * @brief 将业务层 BedType 转换为 gRPC BedType
     * @param bedType 业务层的床类型
     * @return gRPC 协议中的床类型
     */
    static motor_control::BedType convertToBedType(BedType bedType);

    /**
     * @brief 将业务层 BedStatus 转换为 gRPC SystemStatusInfoStatus
     * @param primaryStatus 一级床状态
     * @param secondaryStatus 二级床状态
     * @param currentOwner 当前控制权拥有者
     * @param response gRPC 响应对象
     */
    static void fillSystemStatusInfo(const BedStatus& primaryStatus, 
                                   const BedStatus& secondaryStatus,
                                   BedOwnerType currentOwner,
                                   motor_control::SystemStatusInfoStatus* response);

    /**
     * @brief 将业务层 PostIdInfo 转换为 gRPC PostIdInfo
     * @param postIdInfo 业务层的 PostId 信息
     * @param response gRPC 响应对象
     */
    static void fillPostIdInfo(const PostIdInfo& postIdInfo, 
                             motor_control::GetPostIdStatus* response);

private:
    // 禁止实例化
    ProtobufConverter() = delete;
    ~ProtobufConverter() = delete;
    ProtobufConverter(const ProtobufConverter&) = delete;
    ProtobufConverter& operator=(const ProtobufConverter&) = delete;
};

#endif // PROTOBUF_CONVERTER_HPP
