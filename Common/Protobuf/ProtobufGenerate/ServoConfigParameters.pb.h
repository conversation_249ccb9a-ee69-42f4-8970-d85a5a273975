// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ServoConfigParameters.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ServoConfigParameters_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ServoConfigParameters_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ServoConfigParameters_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ServoConfigParameters_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ServoConfigParameters_2eproto;
namespace ServoConfigProto {
class HeartbeatConfig;
struct HeartbeatConfigDefaultTypeInternal;
extern HeartbeatConfigDefaultTypeInternal _HeartbeatConfig_default_instance_;
class HomingConfig;
struct HomingConfigDefaultTypeInternal;
extern HomingConfigDefaultTypeInternal _HomingConfig_default_instance_;
class InputPinConfig;
struct InputPinConfigDefaultTypeInternal;
extern InputPinConfigDefaultTypeInternal _InputPinConfig_default_instance_;
class PDOMappingEntry;
struct PDOMappingEntryDefaultTypeInternal;
extern PDOMappingEntryDefaultTypeInternal _PDOMappingEntry_default_instance_;
class PositionModeConfig;
struct PositionModeConfigDefaultTypeInternal;
extern PositionModeConfigDefaultTypeInternal _PositionModeConfig_default_instance_;
class ServoConfigMessage;
struct ServoConfigMessageDefaultTypeInternal;
extern ServoConfigMessageDefaultTypeInternal _ServoConfigMessage_default_instance_;
class ServoMotorConfig;
struct ServoMotorConfigDefaultTypeInternal;
extern ServoMotorConfigDefaultTypeInternal _ServoMotorConfig_default_instance_;
class TPDOConfig;
struct TPDOConfigDefaultTypeInternal;
extern TPDOConfigDefaultTypeInternal _TPDOConfig_default_instance_;
class TouchProbeConfig;
struct TouchProbeConfigDefaultTypeInternal;
extern TouchProbeConfigDefaultTypeInternal _TouchProbeConfig_default_instance_;
class VelocityModeConfig;
struct VelocityModeConfigDefaultTypeInternal;
extern VelocityModeConfigDefaultTypeInternal _VelocityModeConfig_default_instance_;
}  // namespace ServoConfigProto
PROTOBUF_NAMESPACE_OPEN
template<> ::ServoConfigProto::HeartbeatConfig* Arena::CreateMaybeMessage<::ServoConfigProto::HeartbeatConfig>(Arena*);
template<> ::ServoConfigProto::HomingConfig* Arena::CreateMaybeMessage<::ServoConfigProto::HomingConfig>(Arena*);
template<> ::ServoConfigProto::InputPinConfig* Arena::CreateMaybeMessage<::ServoConfigProto::InputPinConfig>(Arena*);
template<> ::ServoConfigProto::PDOMappingEntry* Arena::CreateMaybeMessage<::ServoConfigProto::PDOMappingEntry>(Arena*);
template<> ::ServoConfigProto::PositionModeConfig* Arena::CreateMaybeMessage<::ServoConfigProto::PositionModeConfig>(Arena*);
template<> ::ServoConfigProto::ServoConfigMessage* Arena::CreateMaybeMessage<::ServoConfigProto::ServoConfigMessage>(Arena*);
template<> ::ServoConfigProto::ServoMotorConfig* Arena::CreateMaybeMessage<::ServoConfigProto::ServoMotorConfig>(Arena*);
template<> ::ServoConfigProto::TPDOConfig* Arena::CreateMaybeMessage<::ServoConfigProto::TPDOConfig>(Arena*);
template<> ::ServoConfigProto::TouchProbeConfig* Arena::CreateMaybeMessage<::ServoConfigProto::TouchProbeConfig>(Arena*);
template<> ::ServoConfigProto::VelocityModeConfig* Arena::CreateMaybeMessage<::ServoConfigProto::VelocityModeConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace ServoConfigProto {

enum ServoMotorType : int {
  GENERIC = 0,
  COPLEY_FIRST = 1,
  COPLEY_SECOND = 2,
  ServoMotorType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ServoMotorType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ServoMotorType_IsValid(int value);
constexpr ServoMotorType ServoMotorType_MIN = GENERIC;
constexpr ServoMotorType ServoMotorType_MAX = COPLEY_SECOND;
constexpr int ServoMotorType_ARRAYSIZE = ServoMotorType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ServoMotorType_descriptor();
template<typename T>
inline const std::string& ServoMotorType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ServoMotorType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ServoMotorType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ServoMotorType_descriptor(), enum_t_value);
}
inline bool ServoMotorType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ServoMotorType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ServoMotorType>(
    ServoMotorType_descriptor(), name, value);
}
// ===================================================================

class PositionModeConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.PositionModeConfig) */ {
 public:
  inline PositionModeConfig() : PositionModeConfig(nullptr) {}
  ~PositionModeConfig() override;
  explicit constexpr PositionModeConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PositionModeConfig(const PositionModeConfig& from);
  PositionModeConfig(PositionModeConfig&& from) noexcept
    : PositionModeConfig() {
    *this = ::std::move(from);
  }

  inline PositionModeConfig& operator=(const PositionModeConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline PositionModeConfig& operator=(PositionModeConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PositionModeConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const PositionModeConfig* internal_default_instance() {
    return reinterpret_cast<const PositionModeConfig*>(
               &_PositionModeConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PositionModeConfig& a, PositionModeConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(PositionModeConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PositionModeConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PositionModeConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PositionModeConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PositionModeConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PositionModeConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PositionModeConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.PositionModeConfig";
  }
  protected:
  explicit PositionModeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileVelocityFieldNumber = 1,
    kProfileAccelerationFieldNumber = 2,
    kProfileDecelerationFieldNumber = 3,
    kUseLimitSwitchesFieldNumber = 4,
    kPositiveLimitFieldNumber = 5,
    kNegativeLimitFieldNumber = 6,
    kPositionTrackingWindowFieldNumber = 7,
    kPositionTrackingWindowTimeFieldNumber = 8,
  };
  // int32 profile_velocity = 1;
  void clear_profile_velocity();
  int32_t profile_velocity() const;
  void set_profile_velocity(int32_t value);
  private:
  int32_t _internal_profile_velocity() const;
  void _internal_set_profile_velocity(int32_t value);
  public:

  // int32 profile_acceleration = 2;
  void clear_profile_acceleration();
  int32_t profile_acceleration() const;
  void set_profile_acceleration(int32_t value);
  private:
  int32_t _internal_profile_acceleration() const;
  void _internal_set_profile_acceleration(int32_t value);
  public:

  // int32 profile_deceleration = 3;
  void clear_profile_deceleration();
  int32_t profile_deceleration() const;
  void set_profile_deceleration(int32_t value);
  private:
  int32_t _internal_profile_deceleration() const;
  void _internal_set_profile_deceleration(int32_t value);
  public:

  // bool use_limit_switches = 4;
  void clear_use_limit_switches();
  bool use_limit_switches() const;
  void set_use_limit_switches(bool value);
  private:
  bool _internal_use_limit_switches() const;
  void _internal_set_use_limit_switches(bool value);
  public:

  // int32 positive_limit = 5;
  void clear_positive_limit();
  int32_t positive_limit() const;
  void set_positive_limit(int32_t value);
  private:
  int32_t _internal_positive_limit() const;
  void _internal_set_positive_limit(int32_t value);
  public:

  // int32 negative_limit = 6;
  void clear_negative_limit();
  int32_t negative_limit() const;
  void set_negative_limit(int32_t value);
  private:
  int32_t _internal_negative_limit() const;
  void _internal_set_negative_limit(int32_t value);
  public:

  // int32 position_tracking_window = 7;
  void clear_position_tracking_window();
  int32_t position_tracking_window() const;
  void set_position_tracking_window(int32_t value);
  private:
  int32_t _internal_position_tracking_window() const;
  void _internal_set_position_tracking_window(int32_t value);
  public:

  // int32 position_tracking_window_time = 8;
  void clear_position_tracking_window_time();
  int32_t position_tracking_window_time() const;
  void set_position_tracking_window_time(int32_t value);
  private:
  int32_t _internal_position_tracking_window_time() const;
  void _internal_set_position_tracking_window_time(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.PositionModeConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t profile_velocity_;
  int32_t profile_acceleration_;
  int32_t profile_deceleration_;
  bool use_limit_switches_;
  int32_t positive_limit_;
  int32_t negative_limit_;
  int32_t position_tracking_window_;
  int32_t position_tracking_window_time_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class VelocityModeConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.VelocityModeConfig) */ {
 public:
  inline VelocityModeConfig() : VelocityModeConfig(nullptr) {}
  ~VelocityModeConfig() override;
  explicit constexpr VelocityModeConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VelocityModeConfig(const VelocityModeConfig& from);
  VelocityModeConfig(VelocityModeConfig&& from) noexcept
    : VelocityModeConfig() {
    *this = ::std::move(from);
  }

  inline VelocityModeConfig& operator=(const VelocityModeConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline VelocityModeConfig& operator=(VelocityModeConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VelocityModeConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const VelocityModeConfig* internal_default_instance() {
    return reinterpret_cast<const VelocityModeConfig*>(
               &_VelocityModeConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(VelocityModeConfig& a, VelocityModeConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(VelocityModeConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VelocityModeConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VelocityModeConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VelocityModeConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VelocityModeConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VelocityModeConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VelocityModeConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.VelocityModeConfig";
  }
  protected:
  explicit VelocityModeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileAccelerationFieldNumber = 1,
    kProfileDecelerationFieldNumber = 2,
    kMaxVelocityFieldNumber = 3,
    kMinVelocityFieldNumber = 4,
  };
  // int32 profile_acceleration = 1;
  void clear_profile_acceleration();
  int32_t profile_acceleration() const;
  void set_profile_acceleration(int32_t value);
  private:
  int32_t _internal_profile_acceleration() const;
  void _internal_set_profile_acceleration(int32_t value);
  public:

  // int32 profile_deceleration = 2;
  void clear_profile_deceleration();
  int32_t profile_deceleration() const;
  void set_profile_deceleration(int32_t value);
  private:
  int32_t _internal_profile_deceleration() const;
  void _internal_set_profile_deceleration(int32_t value);
  public:

  // int32 max_velocity = 3;
  void clear_max_velocity();
  int32_t max_velocity() const;
  void set_max_velocity(int32_t value);
  private:
  int32_t _internal_max_velocity() const;
  void _internal_set_max_velocity(int32_t value);
  public:

  // int32 min_velocity = 4;
  void clear_min_velocity();
  int32_t min_velocity() const;
  void set_min_velocity(int32_t value);
  private:
  int32_t _internal_min_velocity() const;
  void _internal_set_min_velocity(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.VelocityModeConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t profile_acceleration_;
  int32_t profile_deceleration_;
  int32_t max_velocity_;
  int32_t min_velocity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class HomingConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.HomingConfig) */ {
 public:
  inline HomingConfig() : HomingConfig(nullptr) {}
  ~HomingConfig() override;
  explicit constexpr HomingConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HomingConfig(const HomingConfig& from);
  HomingConfig(HomingConfig&& from) noexcept
    : HomingConfig() {
    *this = ::std::move(from);
  }

  inline HomingConfig& operator=(const HomingConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline HomingConfig& operator=(HomingConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HomingConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const HomingConfig* internal_default_instance() {
    return reinterpret_cast<const HomingConfig*>(
               &_HomingConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(HomingConfig& a, HomingConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(HomingConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HomingConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HomingConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HomingConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HomingConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HomingConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HomingConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.HomingConfig";
  }
  protected:
  explicit HomingConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMethodFieldNumber = 1,
    kSpeedSwitchFieldNumber = 2,
    kSpeedZeroFieldNumber = 3,
    kAccelerationFieldNumber = 4,
    kOffsetFieldNumber = 5,
  };
  // int32 method = 1;
  void clear_method();
  int32_t method() const;
  void set_method(int32_t value);
  private:
  int32_t _internal_method() const;
  void _internal_set_method(int32_t value);
  public:

  // int32 speed_switch = 2;
  void clear_speed_switch();
  int32_t speed_switch() const;
  void set_speed_switch(int32_t value);
  private:
  int32_t _internal_speed_switch() const;
  void _internal_set_speed_switch(int32_t value);
  public:

  // int32 speed_zero = 3;
  void clear_speed_zero();
  int32_t speed_zero() const;
  void set_speed_zero(int32_t value);
  private:
  int32_t _internal_speed_zero() const;
  void _internal_set_speed_zero(int32_t value);
  public:

  // int32 acceleration = 4;
  void clear_acceleration();
  int32_t acceleration() const;
  void set_acceleration(int32_t value);
  private:
  int32_t _internal_acceleration() const;
  void _internal_set_acceleration(int32_t value);
  public:

  // int32 offset = 5;
  void clear_offset();
  int32_t offset() const;
  void set_offset(int32_t value);
  private:
  int32_t _internal_offset() const;
  void _internal_set_offset(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.HomingConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t method_;
  int32_t speed_switch_;
  int32_t speed_zero_;
  int32_t acceleration_;
  int32_t offset_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class InputPinConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.InputPinConfig) */ {
 public:
  inline InputPinConfig() : InputPinConfig(nullptr) {}
  ~InputPinConfig() override;
  explicit constexpr InputPinConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InputPinConfig(const InputPinConfig& from);
  InputPinConfig(InputPinConfig&& from) noexcept
    : InputPinConfig() {
    *this = ::std::move(from);
  }

  inline InputPinConfig& operator=(const InputPinConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline InputPinConfig& operator=(InputPinConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InputPinConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const InputPinConfig* internal_default_instance() {
    return reinterpret_cast<const InputPinConfig*>(
               &_InputPinConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(InputPinConfig& a, InputPinConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(InputPinConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InputPinConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InputPinConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InputPinConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InputPinConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const InputPinConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InputPinConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.InputPinConfig";
  }
  protected:
  explicit InputPinConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPinIndexFieldNumber = 1,
    kConfigurationFieldNumber = 2,
    kDebounceValuesFieldNumber = 3,
  };
  // int32 pin_index = 1;
  void clear_pin_index();
  int32_t pin_index() const;
  void set_pin_index(int32_t value);
  private:
  int32_t _internal_pin_index() const;
  void _internal_set_pin_index(int32_t value);
  public:

  // int32 configuration = 2;
  void clear_configuration();
  int32_t configuration() const;
  void set_configuration(int32_t value);
  private:
  int32_t _internal_configuration() const;
  void _internal_set_configuration(int32_t value);
  public:

  // uint32 debounce_values = 3;
  void clear_debounce_values();
  uint32_t debounce_values() const;
  void set_debounce_values(uint32_t value);
  private:
  uint32_t _internal_debounce_values() const;
  void _internal_set_debounce_values(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.InputPinConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t pin_index_;
  int32_t configuration_;
  uint32_t debounce_values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class TouchProbeConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.TouchProbeConfig) */ {
 public:
  inline TouchProbeConfig() : TouchProbeConfig(nullptr) {}
  ~TouchProbeConfig() override;
  explicit constexpr TouchProbeConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TouchProbeConfig(const TouchProbeConfig& from);
  TouchProbeConfig(TouchProbeConfig&& from) noexcept
    : TouchProbeConfig() {
    *this = ::std::move(from);
  }

  inline TouchProbeConfig& operator=(const TouchProbeConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline TouchProbeConfig& operator=(TouchProbeConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TouchProbeConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const TouchProbeConfig* internal_default_instance() {
    return reinterpret_cast<const TouchProbeConfig*>(
               &_TouchProbeConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TouchProbeConfig& a, TouchProbeConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(TouchProbeConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TouchProbeConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TouchProbeConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TouchProbeConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TouchProbeConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TouchProbeConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TouchProbeConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.TouchProbeConfig";
  }
  protected:
  explicit TouchProbeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProbeIndexFieldNumber = 1,
    kFunctionFieldNumber = 2,
    kIoSelectFieldNumber = 3,
    kDebounceValuesFieldNumber = 4,
  };
  // int32 probe_index = 1;
  void clear_probe_index();
  int32_t probe_index() const;
  void set_probe_index(int32_t value);
  private:
  int32_t _internal_probe_index() const;
  void _internal_set_probe_index(int32_t value);
  public:

  // int32 function = 2;
  void clear_function();
  int32_t function() const;
  void set_function(int32_t value);
  private:
  int32_t _internal_function() const;
  void _internal_set_function(int32_t value);
  public:

  // int32 io_select = 3;
  void clear_io_select();
  int32_t io_select() const;
  void set_io_select(int32_t value);
  private:
  int32_t _internal_io_select() const;
  void _internal_set_io_select(int32_t value);
  public:

  // uint32 debounce_values = 4;
  void clear_debounce_values();
  uint32_t debounce_values() const;
  void set_debounce_values(uint32_t value);
  private:
  uint32_t _internal_debounce_values() const;
  void _internal_set_debounce_values(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.TouchProbeConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t probe_index_;
  int32_t function_;
  int32_t io_select_;
  uint32_t debounce_values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class PDOMappingEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.PDOMappingEntry) */ {
 public:
  inline PDOMappingEntry() : PDOMappingEntry(nullptr) {}
  ~PDOMappingEntry() override;
  explicit constexpr PDOMappingEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PDOMappingEntry(const PDOMappingEntry& from);
  PDOMappingEntry(PDOMappingEntry&& from) noexcept
    : PDOMappingEntry() {
    *this = ::std::move(from);
  }

  inline PDOMappingEntry& operator=(const PDOMappingEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline PDOMappingEntry& operator=(PDOMappingEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PDOMappingEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const PDOMappingEntry* internal_default_instance() {
    return reinterpret_cast<const PDOMappingEntry*>(
               &_PDOMappingEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PDOMappingEntry& a, PDOMappingEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(PDOMappingEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PDOMappingEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PDOMappingEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PDOMappingEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PDOMappingEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PDOMappingEntry& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PDOMappingEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.PDOMappingEntry";
  }
  protected:
  explicit PDOMappingEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kObjectIndexFieldNumber = 1,
    kSubIndexFieldNumber = 2,
    kDataLengthFieldNumber = 3,
  };
  // uint32 object_index = 1;
  void clear_object_index();
  uint32_t object_index() const;
  void set_object_index(uint32_t value);
  private:
  uint32_t _internal_object_index() const;
  void _internal_set_object_index(uint32_t value);
  public:

  // uint32 sub_index = 2;
  void clear_sub_index();
  uint32_t sub_index() const;
  void set_sub_index(uint32_t value);
  private:
  uint32_t _internal_sub_index() const;
  void _internal_set_sub_index(uint32_t value);
  public:

  // uint32 data_length = 3;
  void clear_data_length();
  uint32_t data_length() const;
  void set_data_length(uint32_t value);
  private:
  uint32_t _internal_data_length() const;
  void _internal_set_data_length(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.PDOMappingEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t object_index_;
  uint32_t sub_index_;
  uint32_t data_length_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class TPDOConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.TPDOConfig) */ {
 public:
  inline TPDOConfig() : TPDOConfig(nullptr) {}
  ~TPDOConfig() override;
  explicit constexpr TPDOConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPDOConfig(const TPDOConfig& from);
  TPDOConfig(TPDOConfig&& from) noexcept
    : TPDOConfig() {
    *this = ::std::move(from);
  }

  inline TPDOConfig& operator=(const TPDOConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPDOConfig& operator=(TPDOConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPDOConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPDOConfig* internal_default_instance() {
    return reinterpret_cast<const TPDOConfig*>(
               &_TPDOConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TPDOConfig& a, TPDOConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(TPDOConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPDOConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPDOConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPDOConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPDOConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TPDOConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPDOConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.TPDOConfig";
  }
  protected:
  explicit TPDOConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMappingEntriesFieldNumber = 3,
    kPdoNumberFieldNumber = 1,
    kTransmissionTypeFieldNumber = 2,
  };
  // repeated .ServoConfigProto.PDOMappingEntry mapping_entries = 3;
  int mapping_entries_size() const;
  private:
  int _internal_mapping_entries_size() const;
  public:
  void clear_mapping_entries();
  ::ServoConfigProto::PDOMappingEntry* mutable_mapping_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::PDOMappingEntry >*
      mutable_mapping_entries();
  private:
  const ::ServoConfigProto::PDOMappingEntry& _internal_mapping_entries(int index) const;
  ::ServoConfigProto::PDOMappingEntry* _internal_add_mapping_entries();
  public:
  const ::ServoConfigProto::PDOMappingEntry& mapping_entries(int index) const;
  ::ServoConfigProto::PDOMappingEntry* add_mapping_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::PDOMappingEntry >&
      mapping_entries() const;

  // uint32 pdo_number = 1;
  void clear_pdo_number();
  uint32_t pdo_number() const;
  void set_pdo_number(uint32_t value);
  private:
  uint32_t _internal_pdo_number() const;
  void _internal_set_pdo_number(uint32_t value);
  public:

  // uint32 transmission_type = 2;
  void clear_transmission_type();
  uint32_t transmission_type() const;
  void set_transmission_type(uint32_t value);
  private:
  uint32_t _internal_transmission_type() const;
  void _internal_set_transmission_type(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.TPDOConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::PDOMappingEntry > mapping_entries_;
  uint32_t pdo_number_;
  uint32_t transmission_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class ServoMotorConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.ServoMotorConfig) */ {
 public:
  inline ServoMotorConfig() : ServoMotorConfig(nullptr) {}
  ~ServoMotorConfig() override;
  explicit constexpr ServoMotorConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ServoMotorConfig(const ServoMotorConfig& from);
  ServoMotorConfig(ServoMotorConfig&& from) noexcept
    : ServoMotorConfig() {
    *this = ::std::move(from);
  }

  inline ServoMotorConfig& operator=(const ServoMotorConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServoMotorConfig& operator=(ServoMotorConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ServoMotorConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const ServoMotorConfig* internal_default_instance() {
    return reinterpret_cast<const ServoMotorConfig*>(
               &_ServoMotorConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ServoMotorConfig& a, ServoMotorConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(ServoMotorConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServoMotorConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ServoMotorConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ServoMotorConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ServoMotorConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ServoMotorConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServoMotorConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.ServoMotorConfig";
  }
  protected:
  explicit ServoMotorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputPinsFieldNumber = 8,
    kTouchProbesFieldNumber = 9,
    kTpdoConfigFieldNumber = 10,
    kNameFieldNumber = 2,
    kTypeFieldNumber = 3,
    kPositionModeFieldNumber = 5,
    kVelocityModeFieldNumber = 6,
    kHomingFieldNumber = 7,
    kNodeIdFieldNumber = 1,
    kEncoderResolutionFieldNumber = 4,
  };
  // repeated .ServoConfigProto.InputPinConfig input_pins = 8;
  int input_pins_size() const;
  private:
  int _internal_input_pins_size() const;
  public:
  void clear_input_pins();
  ::ServoConfigProto::InputPinConfig* mutable_input_pins(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::InputPinConfig >*
      mutable_input_pins();
  private:
  const ::ServoConfigProto::InputPinConfig& _internal_input_pins(int index) const;
  ::ServoConfigProto::InputPinConfig* _internal_add_input_pins();
  public:
  const ::ServoConfigProto::InputPinConfig& input_pins(int index) const;
  ::ServoConfigProto::InputPinConfig* add_input_pins();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::InputPinConfig >&
      input_pins() const;

  // repeated .ServoConfigProto.TouchProbeConfig touch_probes = 9;
  int touch_probes_size() const;
  private:
  int _internal_touch_probes_size() const;
  public:
  void clear_touch_probes();
  ::ServoConfigProto::TouchProbeConfig* mutable_touch_probes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TouchProbeConfig >*
      mutable_touch_probes();
  private:
  const ::ServoConfigProto::TouchProbeConfig& _internal_touch_probes(int index) const;
  ::ServoConfigProto::TouchProbeConfig* _internal_add_touch_probes();
  public:
  const ::ServoConfigProto::TouchProbeConfig& touch_probes(int index) const;
  ::ServoConfigProto::TouchProbeConfig* add_touch_probes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TouchProbeConfig >&
      touch_probes() const;

  // repeated .ServoConfigProto.TPDOConfig tpdo_config = 10;
  int tpdo_config_size() const;
  private:
  int _internal_tpdo_config_size() const;
  public:
  void clear_tpdo_config();
  ::ServoConfigProto::TPDOConfig* mutable_tpdo_config(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TPDOConfig >*
      mutable_tpdo_config();
  private:
  const ::ServoConfigProto::TPDOConfig& _internal_tpdo_config(int index) const;
  ::ServoConfigProto::TPDOConfig* _internal_add_tpdo_config();
  public:
  const ::ServoConfigProto::TPDOConfig& tpdo_config(int index) const;
  ::ServoConfigProto::TPDOConfig* add_tpdo_config();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TPDOConfig >&
      tpdo_config() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string type = 3;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // .ServoConfigProto.PositionModeConfig position_mode = 5;
  bool has_position_mode() const;
  private:
  bool _internal_has_position_mode() const;
  public:
  void clear_position_mode();
  const ::ServoConfigProto::PositionModeConfig& position_mode() const;
  PROTOBUF_NODISCARD ::ServoConfigProto::PositionModeConfig* release_position_mode();
  ::ServoConfigProto::PositionModeConfig* mutable_position_mode();
  void set_allocated_position_mode(::ServoConfigProto::PositionModeConfig* position_mode);
  private:
  const ::ServoConfigProto::PositionModeConfig& _internal_position_mode() const;
  ::ServoConfigProto::PositionModeConfig* _internal_mutable_position_mode();
  public:
  void unsafe_arena_set_allocated_position_mode(
      ::ServoConfigProto::PositionModeConfig* position_mode);
  ::ServoConfigProto::PositionModeConfig* unsafe_arena_release_position_mode();

  // .ServoConfigProto.VelocityModeConfig velocity_mode = 6;
  bool has_velocity_mode() const;
  private:
  bool _internal_has_velocity_mode() const;
  public:
  void clear_velocity_mode();
  const ::ServoConfigProto::VelocityModeConfig& velocity_mode() const;
  PROTOBUF_NODISCARD ::ServoConfigProto::VelocityModeConfig* release_velocity_mode();
  ::ServoConfigProto::VelocityModeConfig* mutable_velocity_mode();
  void set_allocated_velocity_mode(::ServoConfigProto::VelocityModeConfig* velocity_mode);
  private:
  const ::ServoConfigProto::VelocityModeConfig& _internal_velocity_mode() const;
  ::ServoConfigProto::VelocityModeConfig* _internal_mutable_velocity_mode();
  public:
  void unsafe_arena_set_allocated_velocity_mode(
      ::ServoConfigProto::VelocityModeConfig* velocity_mode);
  ::ServoConfigProto::VelocityModeConfig* unsafe_arena_release_velocity_mode();

  // .ServoConfigProto.HomingConfig homing = 7;
  bool has_homing() const;
  private:
  bool _internal_has_homing() const;
  public:
  void clear_homing();
  const ::ServoConfigProto::HomingConfig& homing() const;
  PROTOBUF_NODISCARD ::ServoConfigProto::HomingConfig* release_homing();
  ::ServoConfigProto::HomingConfig* mutable_homing();
  void set_allocated_homing(::ServoConfigProto::HomingConfig* homing);
  private:
  const ::ServoConfigProto::HomingConfig& _internal_homing() const;
  ::ServoConfigProto::HomingConfig* _internal_mutable_homing();
  public:
  void unsafe_arena_set_allocated_homing(
      ::ServoConfigProto::HomingConfig* homing);
  ::ServoConfigProto::HomingConfig* unsafe_arena_release_homing();

  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // int32 encoder_resolution = 4;
  void clear_encoder_resolution();
  int32_t encoder_resolution() const;
  void set_encoder_resolution(int32_t value);
  private:
  int32_t _internal_encoder_resolution() const;
  void _internal_set_encoder_resolution(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.ServoMotorConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::InputPinConfig > input_pins_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TouchProbeConfig > touch_probes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TPDOConfig > tpdo_config_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::ServoConfigProto::PositionModeConfig* position_mode_;
  ::ServoConfigProto::VelocityModeConfig* velocity_mode_;
  ::ServoConfigProto::HomingConfig* homing_;
  uint32_t node_id_;
  int32_t encoder_resolution_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class HeartbeatConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.HeartbeatConfig) */ {
 public:
  inline HeartbeatConfig() : HeartbeatConfig(nullptr) {}
  ~HeartbeatConfig() override;
  explicit constexpr HeartbeatConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HeartbeatConfig(const HeartbeatConfig& from);
  HeartbeatConfig(HeartbeatConfig&& from) noexcept
    : HeartbeatConfig() {
    *this = ::std::move(from);
  }

  inline HeartbeatConfig& operator=(const HeartbeatConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeartbeatConfig& operator=(HeartbeatConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HeartbeatConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const HeartbeatConfig* internal_default_instance() {
    return reinterpret_cast<const HeartbeatConfig*>(
               &_HeartbeatConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(HeartbeatConfig& a, HeartbeatConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(HeartbeatConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeartbeatConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HeartbeatConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HeartbeatConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HeartbeatConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HeartbeatConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeartbeatConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.HeartbeatConfig";
  }
  protected:
  explicit HeartbeatConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimeoutMsFieldNumber = 1,
    kCheckIntervalMsFieldNumber = 2,
    kEnableAutoHomingFieldNumber = 3,
    kEnableHeartbeatMonitorFieldNumber = 4,
    kMaxMissedHeartbeatsFieldNumber = 5,
  };
  // uint32 timeout_ms = 1;
  void clear_timeout_ms();
  uint32_t timeout_ms() const;
  void set_timeout_ms(uint32_t value);
  private:
  uint32_t _internal_timeout_ms() const;
  void _internal_set_timeout_ms(uint32_t value);
  public:

  // uint32 check_interval_ms = 2;
  void clear_check_interval_ms();
  uint32_t check_interval_ms() const;
  void set_check_interval_ms(uint32_t value);
  private:
  uint32_t _internal_check_interval_ms() const;
  void _internal_set_check_interval_ms(uint32_t value);
  public:

  // bool enable_auto_homing = 3;
  void clear_enable_auto_homing();
  bool enable_auto_homing() const;
  void set_enable_auto_homing(bool value);
  private:
  bool _internal_enable_auto_homing() const;
  void _internal_set_enable_auto_homing(bool value);
  public:

  // bool enable_heartbeat_monitor = 4;
  void clear_enable_heartbeat_monitor();
  bool enable_heartbeat_monitor() const;
  void set_enable_heartbeat_monitor(bool value);
  private:
  bool _internal_enable_heartbeat_monitor() const;
  void _internal_set_enable_heartbeat_monitor(bool value);
  public:

  // uint32 max_missed_heartbeats = 5;
  void clear_max_missed_heartbeats();
  uint32_t max_missed_heartbeats() const;
  void set_max_missed_heartbeats(uint32_t value);
  private:
  uint32_t _internal_max_missed_heartbeats() const;
  void _internal_set_max_missed_heartbeats(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ServoConfigProto.HeartbeatConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t timeout_ms_;
  uint32_t check_interval_ms_;
  bool enable_auto_homing_;
  bool enable_heartbeat_monitor_;
  uint32_t max_missed_heartbeats_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// -------------------------------------------------------------------

class ServoConfigMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ServoConfigProto.ServoConfigMessage) */ {
 public:
  inline ServoConfigMessage() : ServoConfigMessage(nullptr) {}
  ~ServoConfigMessage() override;
  explicit constexpr ServoConfigMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ServoConfigMessage(const ServoConfigMessage& from);
  ServoConfigMessage(ServoConfigMessage&& from) noexcept
    : ServoConfigMessage() {
    *this = ::std::move(from);
  }

  inline ServoConfigMessage& operator=(const ServoConfigMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServoConfigMessage& operator=(ServoConfigMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ServoConfigMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const ServoConfigMessage* internal_default_instance() {
    return reinterpret_cast<const ServoConfigMessage*>(
               &_ServoConfigMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ServoConfigMessage& a, ServoConfigMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(ServoConfigMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServoConfigMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ServoConfigMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ServoConfigMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ServoConfigMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ServoConfigMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServoConfigMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ServoConfigProto.ServoConfigMessage";
  }
  protected:
  explicit ServoConfigMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServoMotorsFieldNumber = 1,
    kHeartbeatConfigFieldNumber = 2,
  };
  // repeated .ServoConfigProto.ServoMotorConfig servo_motors = 1;
  int servo_motors_size() const;
  private:
  int _internal_servo_motors_size() const;
  public:
  void clear_servo_motors();
  ::ServoConfigProto::ServoMotorConfig* mutable_servo_motors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::ServoMotorConfig >*
      mutable_servo_motors();
  private:
  const ::ServoConfigProto::ServoMotorConfig& _internal_servo_motors(int index) const;
  ::ServoConfigProto::ServoMotorConfig* _internal_add_servo_motors();
  public:
  const ::ServoConfigProto::ServoMotorConfig& servo_motors(int index) const;
  ::ServoConfigProto::ServoMotorConfig* add_servo_motors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::ServoMotorConfig >&
      servo_motors() const;

  // .ServoConfigProto.HeartbeatConfig heartbeat_config = 2;
  bool has_heartbeat_config() const;
  private:
  bool _internal_has_heartbeat_config() const;
  public:
  void clear_heartbeat_config();
  const ::ServoConfigProto::HeartbeatConfig& heartbeat_config() const;
  PROTOBUF_NODISCARD ::ServoConfigProto::HeartbeatConfig* release_heartbeat_config();
  ::ServoConfigProto::HeartbeatConfig* mutable_heartbeat_config();
  void set_allocated_heartbeat_config(::ServoConfigProto::HeartbeatConfig* heartbeat_config);
  private:
  const ::ServoConfigProto::HeartbeatConfig& _internal_heartbeat_config() const;
  ::ServoConfigProto::HeartbeatConfig* _internal_mutable_heartbeat_config();
  public:
  void unsafe_arena_set_allocated_heartbeat_config(
      ::ServoConfigProto::HeartbeatConfig* heartbeat_config);
  ::ServoConfigProto::HeartbeatConfig* unsafe_arena_release_heartbeat_config();

  // @@protoc_insertion_point(class_scope:ServoConfigProto.ServoConfigMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::ServoMotorConfig > servo_motors_;
  ::ServoConfigProto::HeartbeatConfig* heartbeat_config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ServoConfigParameters_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PositionModeConfig

// int32 profile_velocity = 1;
inline void PositionModeConfig::clear_profile_velocity() {
  profile_velocity_ = 0;
}
inline int32_t PositionModeConfig::_internal_profile_velocity() const {
  return profile_velocity_;
}
inline int32_t PositionModeConfig::profile_velocity() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.profile_velocity)
  return _internal_profile_velocity();
}
inline void PositionModeConfig::_internal_set_profile_velocity(int32_t value) {
  
  profile_velocity_ = value;
}
inline void PositionModeConfig::set_profile_velocity(int32_t value) {
  _internal_set_profile_velocity(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.profile_velocity)
}

// int32 profile_acceleration = 2;
inline void PositionModeConfig::clear_profile_acceleration() {
  profile_acceleration_ = 0;
}
inline int32_t PositionModeConfig::_internal_profile_acceleration() const {
  return profile_acceleration_;
}
inline int32_t PositionModeConfig::profile_acceleration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.profile_acceleration)
  return _internal_profile_acceleration();
}
inline void PositionModeConfig::_internal_set_profile_acceleration(int32_t value) {
  
  profile_acceleration_ = value;
}
inline void PositionModeConfig::set_profile_acceleration(int32_t value) {
  _internal_set_profile_acceleration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.profile_acceleration)
}

// int32 profile_deceleration = 3;
inline void PositionModeConfig::clear_profile_deceleration() {
  profile_deceleration_ = 0;
}
inline int32_t PositionModeConfig::_internal_profile_deceleration() const {
  return profile_deceleration_;
}
inline int32_t PositionModeConfig::profile_deceleration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.profile_deceleration)
  return _internal_profile_deceleration();
}
inline void PositionModeConfig::_internal_set_profile_deceleration(int32_t value) {
  
  profile_deceleration_ = value;
}
inline void PositionModeConfig::set_profile_deceleration(int32_t value) {
  _internal_set_profile_deceleration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.profile_deceleration)
}

// bool use_limit_switches = 4;
inline void PositionModeConfig::clear_use_limit_switches() {
  use_limit_switches_ = false;
}
inline bool PositionModeConfig::_internal_use_limit_switches() const {
  return use_limit_switches_;
}
inline bool PositionModeConfig::use_limit_switches() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.use_limit_switches)
  return _internal_use_limit_switches();
}
inline void PositionModeConfig::_internal_set_use_limit_switches(bool value) {
  
  use_limit_switches_ = value;
}
inline void PositionModeConfig::set_use_limit_switches(bool value) {
  _internal_set_use_limit_switches(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.use_limit_switches)
}

// int32 positive_limit = 5;
inline void PositionModeConfig::clear_positive_limit() {
  positive_limit_ = 0;
}
inline int32_t PositionModeConfig::_internal_positive_limit() const {
  return positive_limit_;
}
inline int32_t PositionModeConfig::positive_limit() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.positive_limit)
  return _internal_positive_limit();
}
inline void PositionModeConfig::_internal_set_positive_limit(int32_t value) {
  
  positive_limit_ = value;
}
inline void PositionModeConfig::set_positive_limit(int32_t value) {
  _internal_set_positive_limit(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.positive_limit)
}

// int32 negative_limit = 6;
inline void PositionModeConfig::clear_negative_limit() {
  negative_limit_ = 0;
}
inline int32_t PositionModeConfig::_internal_negative_limit() const {
  return negative_limit_;
}
inline int32_t PositionModeConfig::negative_limit() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.negative_limit)
  return _internal_negative_limit();
}
inline void PositionModeConfig::_internal_set_negative_limit(int32_t value) {
  
  negative_limit_ = value;
}
inline void PositionModeConfig::set_negative_limit(int32_t value) {
  _internal_set_negative_limit(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.negative_limit)
}

// int32 position_tracking_window = 7;
inline void PositionModeConfig::clear_position_tracking_window() {
  position_tracking_window_ = 0;
}
inline int32_t PositionModeConfig::_internal_position_tracking_window() const {
  return position_tracking_window_;
}
inline int32_t PositionModeConfig::position_tracking_window() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.position_tracking_window)
  return _internal_position_tracking_window();
}
inline void PositionModeConfig::_internal_set_position_tracking_window(int32_t value) {
  
  position_tracking_window_ = value;
}
inline void PositionModeConfig::set_position_tracking_window(int32_t value) {
  _internal_set_position_tracking_window(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.position_tracking_window)
}

// int32 position_tracking_window_time = 8;
inline void PositionModeConfig::clear_position_tracking_window_time() {
  position_tracking_window_time_ = 0;
}
inline int32_t PositionModeConfig::_internal_position_tracking_window_time() const {
  return position_tracking_window_time_;
}
inline int32_t PositionModeConfig::position_tracking_window_time() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PositionModeConfig.position_tracking_window_time)
  return _internal_position_tracking_window_time();
}
inline void PositionModeConfig::_internal_set_position_tracking_window_time(int32_t value) {
  
  position_tracking_window_time_ = value;
}
inline void PositionModeConfig::set_position_tracking_window_time(int32_t value) {
  _internal_set_position_tracking_window_time(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PositionModeConfig.position_tracking_window_time)
}

// -------------------------------------------------------------------

// VelocityModeConfig

// int32 profile_acceleration = 1;
inline void VelocityModeConfig::clear_profile_acceleration() {
  profile_acceleration_ = 0;
}
inline int32_t VelocityModeConfig::_internal_profile_acceleration() const {
  return profile_acceleration_;
}
inline int32_t VelocityModeConfig::profile_acceleration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.VelocityModeConfig.profile_acceleration)
  return _internal_profile_acceleration();
}
inline void VelocityModeConfig::_internal_set_profile_acceleration(int32_t value) {
  
  profile_acceleration_ = value;
}
inline void VelocityModeConfig::set_profile_acceleration(int32_t value) {
  _internal_set_profile_acceleration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.VelocityModeConfig.profile_acceleration)
}

// int32 profile_deceleration = 2;
inline void VelocityModeConfig::clear_profile_deceleration() {
  profile_deceleration_ = 0;
}
inline int32_t VelocityModeConfig::_internal_profile_deceleration() const {
  return profile_deceleration_;
}
inline int32_t VelocityModeConfig::profile_deceleration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.VelocityModeConfig.profile_deceleration)
  return _internal_profile_deceleration();
}
inline void VelocityModeConfig::_internal_set_profile_deceleration(int32_t value) {
  
  profile_deceleration_ = value;
}
inline void VelocityModeConfig::set_profile_deceleration(int32_t value) {
  _internal_set_profile_deceleration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.VelocityModeConfig.profile_deceleration)
}

// int32 max_velocity = 3;
inline void VelocityModeConfig::clear_max_velocity() {
  max_velocity_ = 0;
}
inline int32_t VelocityModeConfig::_internal_max_velocity() const {
  return max_velocity_;
}
inline int32_t VelocityModeConfig::max_velocity() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.VelocityModeConfig.max_velocity)
  return _internal_max_velocity();
}
inline void VelocityModeConfig::_internal_set_max_velocity(int32_t value) {
  
  max_velocity_ = value;
}
inline void VelocityModeConfig::set_max_velocity(int32_t value) {
  _internal_set_max_velocity(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.VelocityModeConfig.max_velocity)
}

// int32 min_velocity = 4;
inline void VelocityModeConfig::clear_min_velocity() {
  min_velocity_ = 0;
}
inline int32_t VelocityModeConfig::_internal_min_velocity() const {
  return min_velocity_;
}
inline int32_t VelocityModeConfig::min_velocity() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.VelocityModeConfig.min_velocity)
  return _internal_min_velocity();
}
inline void VelocityModeConfig::_internal_set_min_velocity(int32_t value) {
  
  min_velocity_ = value;
}
inline void VelocityModeConfig::set_min_velocity(int32_t value) {
  _internal_set_min_velocity(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.VelocityModeConfig.min_velocity)
}

// -------------------------------------------------------------------

// HomingConfig

// int32 method = 1;
inline void HomingConfig::clear_method() {
  method_ = 0;
}
inline int32_t HomingConfig::_internal_method() const {
  return method_;
}
inline int32_t HomingConfig::method() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HomingConfig.method)
  return _internal_method();
}
inline void HomingConfig::_internal_set_method(int32_t value) {
  
  method_ = value;
}
inline void HomingConfig::set_method(int32_t value) {
  _internal_set_method(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HomingConfig.method)
}

// int32 speed_switch = 2;
inline void HomingConfig::clear_speed_switch() {
  speed_switch_ = 0;
}
inline int32_t HomingConfig::_internal_speed_switch() const {
  return speed_switch_;
}
inline int32_t HomingConfig::speed_switch() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HomingConfig.speed_switch)
  return _internal_speed_switch();
}
inline void HomingConfig::_internal_set_speed_switch(int32_t value) {
  
  speed_switch_ = value;
}
inline void HomingConfig::set_speed_switch(int32_t value) {
  _internal_set_speed_switch(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HomingConfig.speed_switch)
}

// int32 speed_zero = 3;
inline void HomingConfig::clear_speed_zero() {
  speed_zero_ = 0;
}
inline int32_t HomingConfig::_internal_speed_zero() const {
  return speed_zero_;
}
inline int32_t HomingConfig::speed_zero() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HomingConfig.speed_zero)
  return _internal_speed_zero();
}
inline void HomingConfig::_internal_set_speed_zero(int32_t value) {
  
  speed_zero_ = value;
}
inline void HomingConfig::set_speed_zero(int32_t value) {
  _internal_set_speed_zero(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HomingConfig.speed_zero)
}

// int32 acceleration = 4;
inline void HomingConfig::clear_acceleration() {
  acceleration_ = 0;
}
inline int32_t HomingConfig::_internal_acceleration() const {
  return acceleration_;
}
inline int32_t HomingConfig::acceleration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HomingConfig.acceleration)
  return _internal_acceleration();
}
inline void HomingConfig::_internal_set_acceleration(int32_t value) {
  
  acceleration_ = value;
}
inline void HomingConfig::set_acceleration(int32_t value) {
  _internal_set_acceleration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HomingConfig.acceleration)
}

// int32 offset = 5;
inline void HomingConfig::clear_offset() {
  offset_ = 0;
}
inline int32_t HomingConfig::_internal_offset() const {
  return offset_;
}
inline int32_t HomingConfig::offset() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HomingConfig.offset)
  return _internal_offset();
}
inline void HomingConfig::_internal_set_offset(int32_t value) {
  
  offset_ = value;
}
inline void HomingConfig::set_offset(int32_t value) {
  _internal_set_offset(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HomingConfig.offset)
}

// -------------------------------------------------------------------

// InputPinConfig

// int32 pin_index = 1;
inline void InputPinConfig::clear_pin_index() {
  pin_index_ = 0;
}
inline int32_t InputPinConfig::_internal_pin_index() const {
  return pin_index_;
}
inline int32_t InputPinConfig::pin_index() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.InputPinConfig.pin_index)
  return _internal_pin_index();
}
inline void InputPinConfig::_internal_set_pin_index(int32_t value) {
  
  pin_index_ = value;
}
inline void InputPinConfig::set_pin_index(int32_t value) {
  _internal_set_pin_index(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.InputPinConfig.pin_index)
}

// int32 configuration = 2;
inline void InputPinConfig::clear_configuration() {
  configuration_ = 0;
}
inline int32_t InputPinConfig::_internal_configuration() const {
  return configuration_;
}
inline int32_t InputPinConfig::configuration() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.InputPinConfig.configuration)
  return _internal_configuration();
}
inline void InputPinConfig::_internal_set_configuration(int32_t value) {
  
  configuration_ = value;
}
inline void InputPinConfig::set_configuration(int32_t value) {
  _internal_set_configuration(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.InputPinConfig.configuration)
}

// uint32 debounce_values = 3;
inline void InputPinConfig::clear_debounce_values() {
  debounce_values_ = 0u;
}
inline uint32_t InputPinConfig::_internal_debounce_values() const {
  return debounce_values_;
}
inline uint32_t InputPinConfig::debounce_values() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.InputPinConfig.debounce_values)
  return _internal_debounce_values();
}
inline void InputPinConfig::_internal_set_debounce_values(uint32_t value) {
  
  debounce_values_ = value;
}
inline void InputPinConfig::set_debounce_values(uint32_t value) {
  _internal_set_debounce_values(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.InputPinConfig.debounce_values)
}

// -------------------------------------------------------------------

// TouchProbeConfig

// int32 probe_index = 1;
inline void TouchProbeConfig::clear_probe_index() {
  probe_index_ = 0;
}
inline int32_t TouchProbeConfig::_internal_probe_index() const {
  return probe_index_;
}
inline int32_t TouchProbeConfig::probe_index() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TouchProbeConfig.probe_index)
  return _internal_probe_index();
}
inline void TouchProbeConfig::_internal_set_probe_index(int32_t value) {
  
  probe_index_ = value;
}
inline void TouchProbeConfig::set_probe_index(int32_t value) {
  _internal_set_probe_index(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TouchProbeConfig.probe_index)
}

// int32 function = 2;
inline void TouchProbeConfig::clear_function() {
  function_ = 0;
}
inline int32_t TouchProbeConfig::_internal_function() const {
  return function_;
}
inline int32_t TouchProbeConfig::function() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TouchProbeConfig.function)
  return _internal_function();
}
inline void TouchProbeConfig::_internal_set_function(int32_t value) {
  
  function_ = value;
}
inline void TouchProbeConfig::set_function(int32_t value) {
  _internal_set_function(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TouchProbeConfig.function)
}

// int32 io_select = 3;
inline void TouchProbeConfig::clear_io_select() {
  io_select_ = 0;
}
inline int32_t TouchProbeConfig::_internal_io_select() const {
  return io_select_;
}
inline int32_t TouchProbeConfig::io_select() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TouchProbeConfig.io_select)
  return _internal_io_select();
}
inline void TouchProbeConfig::_internal_set_io_select(int32_t value) {
  
  io_select_ = value;
}
inline void TouchProbeConfig::set_io_select(int32_t value) {
  _internal_set_io_select(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TouchProbeConfig.io_select)
}

// uint32 debounce_values = 4;
inline void TouchProbeConfig::clear_debounce_values() {
  debounce_values_ = 0u;
}
inline uint32_t TouchProbeConfig::_internal_debounce_values() const {
  return debounce_values_;
}
inline uint32_t TouchProbeConfig::debounce_values() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TouchProbeConfig.debounce_values)
  return _internal_debounce_values();
}
inline void TouchProbeConfig::_internal_set_debounce_values(uint32_t value) {
  
  debounce_values_ = value;
}
inline void TouchProbeConfig::set_debounce_values(uint32_t value) {
  _internal_set_debounce_values(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TouchProbeConfig.debounce_values)
}

// -------------------------------------------------------------------

// PDOMappingEntry

// uint32 object_index = 1;
inline void PDOMappingEntry::clear_object_index() {
  object_index_ = 0u;
}
inline uint32_t PDOMappingEntry::_internal_object_index() const {
  return object_index_;
}
inline uint32_t PDOMappingEntry::object_index() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PDOMappingEntry.object_index)
  return _internal_object_index();
}
inline void PDOMappingEntry::_internal_set_object_index(uint32_t value) {
  
  object_index_ = value;
}
inline void PDOMappingEntry::set_object_index(uint32_t value) {
  _internal_set_object_index(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PDOMappingEntry.object_index)
}

// uint32 sub_index = 2;
inline void PDOMappingEntry::clear_sub_index() {
  sub_index_ = 0u;
}
inline uint32_t PDOMappingEntry::_internal_sub_index() const {
  return sub_index_;
}
inline uint32_t PDOMappingEntry::sub_index() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PDOMappingEntry.sub_index)
  return _internal_sub_index();
}
inline void PDOMappingEntry::_internal_set_sub_index(uint32_t value) {
  
  sub_index_ = value;
}
inline void PDOMappingEntry::set_sub_index(uint32_t value) {
  _internal_set_sub_index(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PDOMappingEntry.sub_index)
}

// uint32 data_length = 3;
inline void PDOMappingEntry::clear_data_length() {
  data_length_ = 0u;
}
inline uint32_t PDOMappingEntry::_internal_data_length() const {
  return data_length_;
}
inline uint32_t PDOMappingEntry::data_length() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.PDOMappingEntry.data_length)
  return _internal_data_length();
}
inline void PDOMappingEntry::_internal_set_data_length(uint32_t value) {
  
  data_length_ = value;
}
inline void PDOMappingEntry::set_data_length(uint32_t value) {
  _internal_set_data_length(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.PDOMappingEntry.data_length)
}

// -------------------------------------------------------------------

// TPDOConfig

// uint32 pdo_number = 1;
inline void TPDOConfig::clear_pdo_number() {
  pdo_number_ = 0u;
}
inline uint32_t TPDOConfig::_internal_pdo_number() const {
  return pdo_number_;
}
inline uint32_t TPDOConfig::pdo_number() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TPDOConfig.pdo_number)
  return _internal_pdo_number();
}
inline void TPDOConfig::_internal_set_pdo_number(uint32_t value) {
  
  pdo_number_ = value;
}
inline void TPDOConfig::set_pdo_number(uint32_t value) {
  _internal_set_pdo_number(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TPDOConfig.pdo_number)
}

// uint32 transmission_type = 2;
inline void TPDOConfig::clear_transmission_type() {
  transmission_type_ = 0u;
}
inline uint32_t TPDOConfig::_internal_transmission_type() const {
  return transmission_type_;
}
inline uint32_t TPDOConfig::transmission_type() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TPDOConfig.transmission_type)
  return _internal_transmission_type();
}
inline void TPDOConfig::_internal_set_transmission_type(uint32_t value) {
  
  transmission_type_ = value;
}
inline void TPDOConfig::set_transmission_type(uint32_t value) {
  _internal_set_transmission_type(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.TPDOConfig.transmission_type)
}

// repeated .ServoConfigProto.PDOMappingEntry mapping_entries = 3;
inline int TPDOConfig::_internal_mapping_entries_size() const {
  return mapping_entries_.size();
}
inline int TPDOConfig::mapping_entries_size() const {
  return _internal_mapping_entries_size();
}
inline void TPDOConfig::clear_mapping_entries() {
  mapping_entries_.Clear();
}
inline ::ServoConfigProto::PDOMappingEntry* TPDOConfig::mutable_mapping_entries(int index) {
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.TPDOConfig.mapping_entries)
  return mapping_entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::PDOMappingEntry >*
TPDOConfig::mutable_mapping_entries() {
  // @@protoc_insertion_point(field_mutable_list:ServoConfigProto.TPDOConfig.mapping_entries)
  return &mapping_entries_;
}
inline const ::ServoConfigProto::PDOMappingEntry& TPDOConfig::_internal_mapping_entries(int index) const {
  return mapping_entries_.Get(index);
}
inline const ::ServoConfigProto::PDOMappingEntry& TPDOConfig::mapping_entries(int index) const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.TPDOConfig.mapping_entries)
  return _internal_mapping_entries(index);
}
inline ::ServoConfigProto::PDOMappingEntry* TPDOConfig::_internal_add_mapping_entries() {
  return mapping_entries_.Add();
}
inline ::ServoConfigProto::PDOMappingEntry* TPDOConfig::add_mapping_entries() {
  ::ServoConfigProto::PDOMappingEntry* _add = _internal_add_mapping_entries();
  // @@protoc_insertion_point(field_add:ServoConfigProto.TPDOConfig.mapping_entries)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::PDOMappingEntry >&
TPDOConfig::mapping_entries() const {
  // @@protoc_insertion_point(field_list:ServoConfigProto.TPDOConfig.mapping_entries)
  return mapping_entries_;
}

// -------------------------------------------------------------------

// ServoMotorConfig

// uint32 node_id = 1;
inline void ServoMotorConfig::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t ServoMotorConfig::_internal_node_id() const {
  return node_id_;
}
inline uint32_t ServoMotorConfig::node_id() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.node_id)
  return _internal_node_id();
}
inline void ServoMotorConfig::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void ServoMotorConfig::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.ServoMotorConfig.node_id)
}

// string name = 2;
inline void ServoMotorConfig::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ServoMotorConfig::name() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ServoMotorConfig::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ServoConfigProto.ServoMotorConfig.name)
}
inline std::string* ServoMotorConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.name)
  return _s;
}
inline const std::string& ServoMotorConfig::_internal_name() const {
  return name_.Get();
}
inline void ServoMotorConfig::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ServoMotorConfig::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ServoMotorConfig::release_name() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoMotorConfig.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ServoMotorConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoMotorConfig.name)
}

// string type = 3;
inline void ServoMotorConfig::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& ServoMotorConfig::type() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ServoMotorConfig::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ServoConfigProto.ServoMotorConfig.type)
}
inline std::string* ServoMotorConfig::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.type)
  return _s;
}
inline const std::string& ServoMotorConfig::_internal_type() const {
  return type_.Get();
}
inline void ServoMotorConfig::_internal_set_type(const std::string& value) {
  
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ServoMotorConfig::_internal_mutable_type() {
  
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ServoMotorConfig::release_type() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoMotorConfig.type)
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ServoMotorConfig::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoMotorConfig.type)
}

// int32 encoder_resolution = 4;
inline void ServoMotorConfig::clear_encoder_resolution() {
  encoder_resolution_ = 0;
}
inline int32_t ServoMotorConfig::_internal_encoder_resolution() const {
  return encoder_resolution_;
}
inline int32_t ServoMotorConfig::encoder_resolution() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.encoder_resolution)
  return _internal_encoder_resolution();
}
inline void ServoMotorConfig::_internal_set_encoder_resolution(int32_t value) {
  
  encoder_resolution_ = value;
}
inline void ServoMotorConfig::set_encoder_resolution(int32_t value) {
  _internal_set_encoder_resolution(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.ServoMotorConfig.encoder_resolution)
}

// .ServoConfigProto.PositionModeConfig position_mode = 5;
inline bool ServoMotorConfig::_internal_has_position_mode() const {
  return this != internal_default_instance() && position_mode_ != nullptr;
}
inline bool ServoMotorConfig::has_position_mode() const {
  return _internal_has_position_mode();
}
inline void ServoMotorConfig::clear_position_mode() {
  if (GetArenaForAllocation() == nullptr && position_mode_ != nullptr) {
    delete position_mode_;
  }
  position_mode_ = nullptr;
}
inline const ::ServoConfigProto::PositionModeConfig& ServoMotorConfig::_internal_position_mode() const {
  const ::ServoConfigProto::PositionModeConfig* p = position_mode_;
  return p != nullptr ? *p : reinterpret_cast<const ::ServoConfigProto::PositionModeConfig&>(
      ::ServoConfigProto::_PositionModeConfig_default_instance_);
}
inline const ::ServoConfigProto::PositionModeConfig& ServoMotorConfig::position_mode() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.position_mode)
  return _internal_position_mode();
}
inline void ServoMotorConfig::unsafe_arena_set_allocated_position_mode(
    ::ServoConfigProto::PositionModeConfig* position_mode) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position_mode_);
  }
  position_mode_ = position_mode;
  if (position_mode) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ServoConfigProto.ServoMotorConfig.position_mode)
}
inline ::ServoConfigProto::PositionModeConfig* ServoMotorConfig::release_position_mode() {
  
  ::ServoConfigProto::PositionModeConfig* temp = position_mode_;
  position_mode_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ServoConfigProto::PositionModeConfig* ServoMotorConfig::unsafe_arena_release_position_mode() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoMotorConfig.position_mode)
  
  ::ServoConfigProto::PositionModeConfig* temp = position_mode_;
  position_mode_ = nullptr;
  return temp;
}
inline ::ServoConfigProto::PositionModeConfig* ServoMotorConfig::_internal_mutable_position_mode() {
  
  if (position_mode_ == nullptr) {
    auto* p = CreateMaybeMessage<::ServoConfigProto::PositionModeConfig>(GetArenaForAllocation());
    position_mode_ = p;
  }
  return position_mode_;
}
inline ::ServoConfigProto::PositionModeConfig* ServoMotorConfig::mutable_position_mode() {
  ::ServoConfigProto::PositionModeConfig* _msg = _internal_mutable_position_mode();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.position_mode)
  return _msg;
}
inline void ServoMotorConfig::set_allocated_position_mode(::ServoConfigProto::PositionModeConfig* position_mode) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete position_mode_;
  }
  if (position_mode) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::ServoConfigProto::PositionModeConfig>::GetOwningArena(position_mode);
    if (message_arena != submessage_arena) {
      position_mode = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position_mode, submessage_arena);
    }
    
  } else {
    
  }
  position_mode_ = position_mode;
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoMotorConfig.position_mode)
}

// .ServoConfigProto.VelocityModeConfig velocity_mode = 6;
inline bool ServoMotorConfig::_internal_has_velocity_mode() const {
  return this != internal_default_instance() && velocity_mode_ != nullptr;
}
inline bool ServoMotorConfig::has_velocity_mode() const {
  return _internal_has_velocity_mode();
}
inline void ServoMotorConfig::clear_velocity_mode() {
  if (GetArenaForAllocation() == nullptr && velocity_mode_ != nullptr) {
    delete velocity_mode_;
  }
  velocity_mode_ = nullptr;
}
inline const ::ServoConfigProto::VelocityModeConfig& ServoMotorConfig::_internal_velocity_mode() const {
  const ::ServoConfigProto::VelocityModeConfig* p = velocity_mode_;
  return p != nullptr ? *p : reinterpret_cast<const ::ServoConfigProto::VelocityModeConfig&>(
      ::ServoConfigProto::_VelocityModeConfig_default_instance_);
}
inline const ::ServoConfigProto::VelocityModeConfig& ServoMotorConfig::velocity_mode() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.velocity_mode)
  return _internal_velocity_mode();
}
inline void ServoMotorConfig::unsafe_arena_set_allocated_velocity_mode(
    ::ServoConfigProto::VelocityModeConfig* velocity_mode) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(velocity_mode_);
  }
  velocity_mode_ = velocity_mode;
  if (velocity_mode) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ServoConfigProto.ServoMotorConfig.velocity_mode)
}
inline ::ServoConfigProto::VelocityModeConfig* ServoMotorConfig::release_velocity_mode() {
  
  ::ServoConfigProto::VelocityModeConfig* temp = velocity_mode_;
  velocity_mode_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ServoConfigProto::VelocityModeConfig* ServoMotorConfig::unsafe_arena_release_velocity_mode() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoMotorConfig.velocity_mode)
  
  ::ServoConfigProto::VelocityModeConfig* temp = velocity_mode_;
  velocity_mode_ = nullptr;
  return temp;
}
inline ::ServoConfigProto::VelocityModeConfig* ServoMotorConfig::_internal_mutable_velocity_mode() {
  
  if (velocity_mode_ == nullptr) {
    auto* p = CreateMaybeMessage<::ServoConfigProto::VelocityModeConfig>(GetArenaForAllocation());
    velocity_mode_ = p;
  }
  return velocity_mode_;
}
inline ::ServoConfigProto::VelocityModeConfig* ServoMotorConfig::mutable_velocity_mode() {
  ::ServoConfigProto::VelocityModeConfig* _msg = _internal_mutable_velocity_mode();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.velocity_mode)
  return _msg;
}
inline void ServoMotorConfig::set_allocated_velocity_mode(::ServoConfigProto::VelocityModeConfig* velocity_mode) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete velocity_mode_;
  }
  if (velocity_mode) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::ServoConfigProto::VelocityModeConfig>::GetOwningArena(velocity_mode);
    if (message_arena != submessage_arena) {
      velocity_mode = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, velocity_mode, submessage_arena);
    }
    
  } else {
    
  }
  velocity_mode_ = velocity_mode;
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoMotorConfig.velocity_mode)
}

// .ServoConfigProto.HomingConfig homing = 7;
inline bool ServoMotorConfig::_internal_has_homing() const {
  return this != internal_default_instance() && homing_ != nullptr;
}
inline bool ServoMotorConfig::has_homing() const {
  return _internal_has_homing();
}
inline void ServoMotorConfig::clear_homing() {
  if (GetArenaForAllocation() == nullptr && homing_ != nullptr) {
    delete homing_;
  }
  homing_ = nullptr;
}
inline const ::ServoConfigProto::HomingConfig& ServoMotorConfig::_internal_homing() const {
  const ::ServoConfigProto::HomingConfig* p = homing_;
  return p != nullptr ? *p : reinterpret_cast<const ::ServoConfigProto::HomingConfig&>(
      ::ServoConfigProto::_HomingConfig_default_instance_);
}
inline const ::ServoConfigProto::HomingConfig& ServoMotorConfig::homing() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.homing)
  return _internal_homing();
}
inline void ServoMotorConfig::unsafe_arena_set_allocated_homing(
    ::ServoConfigProto::HomingConfig* homing) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(homing_);
  }
  homing_ = homing;
  if (homing) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ServoConfigProto.ServoMotorConfig.homing)
}
inline ::ServoConfigProto::HomingConfig* ServoMotorConfig::release_homing() {
  
  ::ServoConfigProto::HomingConfig* temp = homing_;
  homing_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ServoConfigProto::HomingConfig* ServoMotorConfig::unsafe_arena_release_homing() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoMotorConfig.homing)
  
  ::ServoConfigProto::HomingConfig* temp = homing_;
  homing_ = nullptr;
  return temp;
}
inline ::ServoConfigProto::HomingConfig* ServoMotorConfig::_internal_mutable_homing() {
  
  if (homing_ == nullptr) {
    auto* p = CreateMaybeMessage<::ServoConfigProto::HomingConfig>(GetArenaForAllocation());
    homing_ = p;
  }
  return homing_;
}
inline ::ServoConfigProto::HomingConfig* ServoMotorConfig::mutable_homing() {
  ::ServoConfigProto::HomingConfig* _msg = _internal_mutable_homing();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.homing)
  return _msg;
}
inline void ServoMotorConfig::set_allocated_homing(::ServoConfigProto::HomingConfig* homing) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete homing_;
  }
  if (homing) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::ServoConfigProto::HomingConfig>::GetOwningArena(homing);
    if (message_arena != submessage_arena) {
      homing = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, homing, submessage_arena);
    }
    
  } else {
    
  }
  homing_ = homing;
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoMotorConfig.homing)
}

// repeated .ServoConfigProto.InputPinConfig input_pins = 8;
inline int ServoMotorConfig::_internal_input_pins_size() const {
  return input_pins_.size();
}
inline int ServoMotorConfig::input_pins_size() const {
  return _internal_input_pins_size();
}
inline void ServoMotorConfig::clear_input_pins() {
  input_pins_.Clear();
}
inline ::ServoConfigProto::InputPinConfig* ServoMotorConfig::mutable_input_pins(int index) {
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.input_pins)
  return input_pins_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::InputPinConfig >*
ServoMotorConfig::mutable_input_pins() {
  // @@protoc_insertion_point(field_mutable_list:ServoConfigProto.ServoMotorConfig.input_pins)
  return &input_pins_;
}
inline const ::ServoConfigProto::InputPinConfig& ServoMotorConfig::_internal_input_pins(int index) const {
  return input_pins_.Get(index);
}
inline const ::ServoConfigProto::InputPinConfig& ServoMotorConfig::input_pins(int index) const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.input_pins)
  return _internal_input_pins(index);
}
inline ::ServoConfigProto::InputPinConfig* ServoMotorConfig::_internal_add_input_pins() {
  return input_pins_.Add();
}
inline ::ServoConfigProto::InputPinConfig* ServoMotorConfig::add_input_pins() {
  ::ServoConfigProto::InputPinConfig* _add = _internal_add_input_pins();
  // @@protoc_insertion_point(field_add:ServoConfigProto.ServoMotorConfig.input_pins)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::InputPinConfig >&
ServoMotorConfig::input_pins() const {
  // @@protoc_insertion_point(field_list:ServoConfigProto.ServoMotorConfig.input_pins)
  return input_pins_;
}

// repeated .ServoConfigProto.TouchProbeConfig touch_probes = 9;
inline int ServoMotorConfig::_internal_touch_probes_size() const {
  return touch_probes_.size();
}
inline int ServoMotorConfig::touch_probes_size() const {
  return _internal_touch_probes_size();
}
inline void ServoMotorConfig::clear_touch_probes() {
  touch_probes_.Clear();
}
inline ::ServoConfigProto::TouchProbeConfig* ServoMotorConfig::mutable_touch_probes(int index) {
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.touch_probes)
  return touch_probes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TouchProbeConfig >*
ServoMotorConfig::mutable_touch_probes() {
  // @@protoc_insertion_point(field_mutable_list:ServoConfigProto.ServoMotorConfig.touch_probes)
  return &touch_probes_;
}
inline const ::ServoConfigProto::TouchProbeConfig& ServoMotorConfig::_internal_touch_probes(int index) const {
  return touch_probes_.Get(index);
}
inline const ::ServoConfigProto::TouchProbeConfig& ServoMotorConfig::touch_probes(int index) const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.touch_probes)
  return _internal_touch_probes(index);
}
inline ::ServoConfigProto::TouchProbeConfig* ServoMotorConfig::_internal_add_touch_probes() {
  return touch_probes_.Add();
}
inline ::ServoConfigProto::TouchProbeConfig* ServoMotorConfig::add_touch_probes() {
  ::ServoConfigProto::TouchProbeConfig* _add = _internal_add_touch_probes();
  // @@protoc_insertion_point(field_add:ServoConfigProto.ServoMotorConfig.touch_probes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TouchProbeConfig >&
ServoMotorConfig::touch_probes() const {
  // @@protoc_insertion_point(field_list:ServoConfigProto.ServoMotorConfig.touch_probes)
  return touch_probes_;
}

// repeated .ServoConfigProto.TPDOConfig tpdo_config = 10;
inline int ServoMotorConfig::_internal_tpdo_config_size() const {
  return tpdo_config_.size();
}
inline int ServoMotorConfig::tpdo_config_size() const {
  return _internal_tpdo_config_size();
}
inline void ServoMotorConfig::clear_tpdo_config() {
  tpdo_config_.Clear();
}
inline ::ServoConfigProto::TPDOConfig* ServoMotorConfig::mutable_tpdo_config(int index) {
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoMotorConfig.tpdo_config)
  return tpdo_config_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TPDOConfig >*
ServoMotorConfig::mutable_tpdo_config() {
  // @@protoc_insertion_point(field_mutable_list:ServoConfigProto.ServoMotorConfig.tpdo_config)
  return &tpdo_config_;
}
inline const ::ServoConfigProto::TPDOConfig& ServoMotorConfig::_internal_tpdo_config(int index) const {
  return tpdo_config_.Get(index);
}
inline const ::ServoConfigProto::TPDOConfig& ServoMotorConfig::tpdo_config(int index) const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoMotorConfig.tpdo_config)
  return _internal_tpdo_config(index);
}
inline ::ServoConfigProto::TPDOConfig* ServoMotorConfig::_internal_add_tpdo_config() {
  return tpdo_config_.Add();
}
inline ::ServoConfigProto::TPDOConfig* ServoMotorConfig::add_tpdo_config() {
  ::ServoConfigProto::TPDOConfig* _add = _internal_add_tpdo_config();
  // @@protoc_insertion_point(field_add:ServoConfigProto.ServoMotorConfig.tpdo_config)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::TPDOConfig >&
ServoMotorConfig::tpdo_config() const {
  // @@protoc_insertion_point(field_list:ServoConfigProto.ServoMotorConfig.tpdo_config)
  return tpdo_config_;
}

// -------------------------------------------------------------------

// HeartbeatConfig

// uint32 timeout_ms = 1;
inline void HeartbeatConfig::clear_timeout_ms() {
  timeout_ms_ = 0u;
}
inline uint32_t HeartbeatConfig::_internal_timeout_ms() const {
  return timeout_ms_;
}
inline uint32_t HeartbeatConfig::timeout_ms() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HeartbeatConfig.timeout_ms)
  return _internal_timeout_ms();
}
inline void HeartbeatConfig::_internal_set_timeout_ms(uint32_t value) {
  
  timeout_ms_ = value;
}
inline void HeartbeatConfig::set_timeout_ms(uint32_t value) {
  _internal_set_timeout_ms(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HeartbeatConfig.timeout_ms)
}

// uint32 check_interval_ms = 2;
inline void HeartbeatConfig::clear_check_interval_ms() {
  check_interval_ms_ = 0u;
}
inline uint32_t HeartbeatConfig::_internal_check_interval_ms() const {
  return check_interval_ms_;
}
inline uint32_t HeartbeatConfig::check_interval_ms() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HeartbeatConfig.check_interval_ms)
  return _internal_check_interval_ms();
}
inline void HeartbeatConfig::_internal_set_check_interval_ms(uint32_t value) {
  
  check_interval_ms_ = value;
}
inline void HeartbeatConfig::set_check_interval_ms(uint32_t value) {
  _internal_set_check_interval_ms(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HeartbeatConfig.check_interval_ms)
}

// bool enable_auto_homing = 3;
inline void HeartbeatConfig::clear_enable_auto_homing() {
  enable_auto_homing_ = false;
}
inline bool HeartbeatConfig::_internal_enable_auto_homing() const {
  return enable_auto_homing_;
}
inline bool HeartbeatConfig::enable_auto_homing() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HeartbeatConfig.enable_auto_homing)
  return _internal_enable_auto_homing();
}
inline void HeartbeatConfig::_internal_set_enable_auto_homing(bool value) {
  
  enable_auto_homing_ = value;
}
inline void HeartbeatConfig::set_enable_auto_homing(bool value) {
  _internal_set_enable_auto_homing(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HeartbeatConfig.enable_auto_homing)
}

// bool enable_heartbeat_monitor = 4;
inline void HeartbeatConfig::clear_enable_heartbeat_monitor() {
  enable_heartbeat_monitor_ = false;
}
inline bool HeartbeatConfig::_internal_enable_heartbeat_monitor() const {
  return enable_heartbeat_monitor_;
}
inline bool HeartbeatConfig::enable_heartbeat_monitor() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HeartbeatConfig.enable_heartbeat_monitor)
  return _internal_enable_heartbeat_monitor();
}
inline void HeartbeatConfig::_internal_set_enable_heartbeat_monitor(bool value) {
  
  enable_heartbeat_monitor_ = value;
}
inline void HeartbeatConfig::set_enable_heartbeat_monitor(bool value) {
  _internal_set_enable_heartbeat_monitor(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HeartbeatConfig.enable_heartbeat_monitor)
}

// uint32 max_missed_heartbeats = 5;
inline void HeartbeatConfig::clear_max_missed_heartbeats() {
  max_missed_heartbeats_ = 0u;
}
inline uint32_t HeartbeatConfig::_internal_max_missed_heartbeats() const {
  return max_missed_heartbeats_;
}
inline uint32_t HeartbeatConfig::max_missed_heartbeats() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.HeartbeatConfig.max_missed_heartbeats)
  return _internal_max_missed_heartbeats();
}
inline void HeartbeatConfig::_internal_set_max_missed_heartbeats(uint32_t value) {
  
  max_missed_heartbeats_ = value;
}
inline void HeartbeatConfig::set_max_missed_heartbeats(uint32_t value) {
  _internal_set_max_missed_heartbeats(value);
  // @@protoc_insertion_point(field_set:ServoConfigProto.HeartbeatConfig.max_missed_heartbeats)
}

// -------------------------------------------------------------------

// ServoConfigMessage

// repeated .ServoConfigProto.ServoMotorConfig servo_motors = 1;
inline int ServoConfigMessage::_internal_servo_motors_size() const {
  return servo_motors_.size();
}
inline int ServoConfigMessage::servo_motors_size() const {
  return _internal_servo_motors_size();
}
inline void ServoConfigMessage::clear_servo_motors() {
  servo_motors_.Clear();
}
inline ::ServoConfigProto::ServoMotorConfig* ServoConfigMessage::mutable_servo_motors(int index) {
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoConfigMessage.servo_motors)
  return servo_motors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::ServoMotorConfig >*
ServoConfigMessage::mutable_servo_motors() {
  // @@protoc_insertion_point(field_mutable_list:ServoConfigProto.ServoConfigMessage.servo_motors)
  return &servo_motors_;
}
inline const ::ServoConfigProto::ServoMotorConfig& ServoConfigMessage::_internal_servo_motors(int index) const {
  return servo_motors_.Get(index);
}
inline const ::ServoConfigProto::ServoMotorConfig& ServoConfigMessage::servo_motors(int index) const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoConfigMessage.servo_motors)
  return _internal_servo_motors(index);
}
inline ::ServoConfigProto::ServoMotorConfig* ServoConfigMessage::_internal_add_servo_motors() {
  return servo_motors_.Add();
}
inline ::ServoConfigProto::ServoMotorConfig* ServoConfigMessage::add_servo_motors() {
  ::ServoConfigProto::ServoMotorConfig* _add = _internal_add_servo_motors();
  // @@protoc_insertion_point(field_add:ServoConfigProto.ServoConfigMessage.servo_motors)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::ServoConfigProto::ServoMotorConfig >&
ServoConfigMessage::servo_motors() const {
  // @@protoc_insertion_point(field_list:ServoConfigProto.ServoConfigMessage.servo_motors)
  return servo_motors_;
}

// .ServoConfigProto.HeartbeatConfig heartbeat_config = 2;
inline bool ServoConfigMessage::_internal_has_heartbeat_config() const {
  return this != internal_default_instance() && heartbeat_config_ != nullptr;
}
inline bool ServoConfigMessage::has_heartbeat_config() const {
  return _internal_has_heartbeat_config();
}
inline void ServoConfigMessage::clear_heartbeat_config() {
  if (GetArenaForAllocation() == nullptr && heartbeat_config_ != nullptr) {
    delete heartbeat_config_;
  }
  heartbeat_config_ = nullptr;
}
inline const ::ServoConfigProto::HeartbeatConfig& ServoConfigMessage::_internal_heartbeat_config() const {
  const ::ServoConfigProto::HeartbeatConfig* p = heartbeat_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::ServoConfigProto::HeartbeatConfig&>(
      ::ServoConfigProto::_HeartbeatConfig_default_instance_);
}
inline const ::ServoConfigProto::HeartbeatConfig& ServoConfigMessage::heartbeat_config() const {
  // @@protoc_insertion_point(field_get:ServoConfigProto.ServoConfigMessage.heartbeat_config)
  return _internal_heartbeat_config();
}
inline void ServoConfigMessage::unsafe_arena_set_allocated_heartbeat_config(
    ::ServoConfigProto::HeartbeatConfig* heartbeat_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(heartbeat_config_);
  }
  heartbeat_config_ = heartbeat_config;
  if (heartbeat_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ServoConfigProto.ServoConfigMessage.heartbeat_config)
}
inline ::ServoConfigProto::HeartbeatConfig* ServoConfigMessage::release_heartbeat_config() {
  
  ::ServoConfigProto::HeartbeatConfig* temp = heartbeat_config_;
  heartbeat_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ServoConfigProto::HeartbeatConfig* ServoConfigMessage::unsafe_arena_release_heartbeat_config() {
  // @@protoc_insertion_point(field_release:ServoConfigProto.ServoConfigMessage.heartbeat_config)
  
  ::ServoConfigProto::HeartbeatConfig* temp = heartbeat_config_;
  heartbeat_config_ = nullptr;
  return temp;
}
inline ::ServoConfigProto::HeartbeatConfig* ServoConfigMessage::_internal_mutable_heartbeat_config() {
  
  if (heartbeat_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::ServoConfigProto::HeartbeatConfig>(GetArenaForAllocation());
    heartbeat_config_ = p;
  }
  return heartbeat_config_;
}
inline ::ServoConfigProto::HeartbeatConfig* ServoConfigMessage::mutable_heartbeat_config() {
  ::ServoConfigProto::HeartbeatConfig* _msg = _internal_mutable_heartbeat_config();
  // @@protoc_insertion_point(field_mutable:ServoConfigProto.ServoConfigMessage.heartbeat_config)
  return _msg;
}
inline void ServoConfigMessage::set_allocated_heartbeat_config(::ServoConfigProto::HeartbeatConfig* heartbeat_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete heartbeat_config_;
  }
  if (heartbeat_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::ServoConfigProto::HeartbeatConfig>::GetOwningArena(heartbeat_config);
    if (message_arena != submessage_arena) {
      heartbeat_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, heartbeat_config, submessage_arena);
    }
    
  } else {
    
  }
  heartbeat_config_ = heartbeat_config;
  // @@protoc_insertion_point(field_set_allocated:ServoConfigProto.ServoConfigMessage.heartbeat_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace ServoConfigProto

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::ServoConfigProto::ServoMotorType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ServoConfigProto::ServoMotorType>() {
  return ::ServoConfigProto::ServoMotorType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ServoConfigParameters_2eproto
