// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: motor_control.proto

#include "motor_control.pb.h"
#include "motor_control.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace motor_control {

static const char* BedMasterAppService_method_names[] = {
  "/motor_control.BedMasterAppService/StartMove",
  "/motor_control.BedMasterAppService/StopMove",
  "/motor_control.BedMasterAppService/GetPostId",
  "/motor_control.BedMasterAppService/GainControl",
  "/motor_control.BedMasterAppService/ReleaseControl",
  "/motor_control.BedMasterAppService/HeartBeatCheck",
  "/motor_control.BedMasterAppService/GetSystemStatusInfo",
  "/motor_control.BedMasterAppService/GetTriggerInfo",
};

std::unique_ptr< BedMasterAppService::Stub> BedMasterAppService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< BedMasterAppService::Stub> stub(new BedMasterAppService::Stub(channel, options));
  return stub;
}

BedMasterAppService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_StartMove_(BedMasterAppService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopMove_(BedMasterAppService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPostId_(BedMasterAppService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GainControl_(BedMasterAppService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReleaseControl_(BedMasterAppService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_HeartBeatCheck_(BedMasterAppService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSystemStatusInfo_(BedMasterAppService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTriggerInfo_(BedMasterAppService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  {}

::grpc::Status BedMasterAppService::Stub::StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::motor_control::StartMoveStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartMove_, context, request, response);
}

void BedMasterAppService::Stub::async::StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartMove_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartMove_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>* BedMasterAppService::Stub::PrepareAsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::StartMoveStatus, ::motor_control::StartMoveDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartMove_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>* BedMasterAppService::Stub::AsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartMoveRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopMove_, context, request, response);
}

void BedMasterAppService::Stub::async::StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopMove_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopMove_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::PrepareAsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::CommonStatus, ::motor_control::CommonDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopMove_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::AsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopMoveRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::motor_control::GetPostIdStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPostId_, context, request, response);
}

void BedMasterAppService::Stub::async::GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPostId_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPostId_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>* BedMasterAppService::Stub::PrepareAsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::GetPostIdStatus, ::motor_control::GetPostIdDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPostId_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>* BedMasterAppService::Stub::AsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPostIdRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GainControl_, context, request, response);
}

void BedMasterAppService::Stub::async::GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GainControl_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GainControl_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::PrepareAsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::CommonStatus, ::motor_control::CommonDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GainControl_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::AsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGainControlRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReleaseControl_, context, request, response);
}

void BedMasterAppService::Stub::async::ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReleaseControl_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReleaseControl_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::PrepareAsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::CommonStatus, ::motor_control::CommonDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReleaseControl_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::AsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReleaseControlRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_HeartBeatCheck_, context, request, response);
}

void BedMasterAppService::Stub::async::HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_HeartBeatCheck_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_HeartBeatCheck_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::PrepareAsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::CommonStatus, ::motor_control::CommonDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_HeartBeatCheck_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* BedMasterAppService::Stub::AsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncHeartBeatCheckRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BedMasterAppService::Stub::GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::SystemStatusInfoStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSystemStatusInfo_, context, request, response);
}

void BedMasterAppService::Stub::async::GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSystemStatusInfo_, context, request, response, std::move(f));
}

void BedMasterAppService::Stub::async::GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSystemStatusInfo_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>* BedMasterAppService::Stub::PrepareAsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::motor_control::SystemStatusInfoStatus, ::motor_control::CommonDescription, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSystemStatusInfo_, context, request);
}

::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>* BedMasterAppService::Stub::AsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSystemStatusInfoRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* BedMasterAppService::Stub::GetTriggerInfoRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>::Create(channel_.get(), rpcmethod_GetTriggerInfo_, context);
}

void BedMasterAppService::Stub::async::GetTriggerInfo(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::motor_control::GetTriggerInfoDescription,::motor_control::TriggerInfoStatus>* reactor) {
  ::grpc::internal::ClientCallbackReaderWriterFactory< ::motor_control::GetTriggerInfoDescription,::motor_control::TriggerInfoStatus>::Create(stub_->channel_.get(), stub_->rpcmethod_GetTriggerInfo_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* BedMasterAppService::Stub::AsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>::Create(channel_.get(), cq, rpcmethod_GetTriggerInfo_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* BedMasterAppService::Stub::PrepareAsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>::Create(channel_.get(), cq, rpcmethod_GetTriggerInfo_, context, false, nullptr);
}

BedMasterAppService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::StartMoveDescription* req,
             ::motor_control::StartMoveStatus* resp) {
               return service->StartMove(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::CommonDescription* req,
             ::motor_control::CommonStatus* resp) {
               return service->StopMove(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::GetPostIdDescription* req,
             ::motor_control::GetPostIdStatus* resp) {
               return service->GetPostId(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::CommonDescription* req,
             ::motor_control::CommonStatus* resp) {
               return service->GainControl(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::CommonDescription* req,
             ::motor_control::CommonStatus* resp) {
               return service->ReleaseControl(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::CommonDescription, ::motor_control::CommonStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::CommonDescription* req,
             ::motor_control::CommonStatus* resp) {
               return service->HeartBeatCheck(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BedMasterAppService::Service, ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::motor_control::CommonDescription* req,
             ::motor_control::SystemStatusInfoStatus* resp) {
               return service->GetSystemStatusInfo(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BedMasterAppService_method_names[7],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< BedMasterAppService::Service, ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>(
          [](BedMasterAppService::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReaderWriter<::motor_control::TriggerInfoStatus,
             ::motor_control::GetTriggerInfoDescription>* stream) {
               return service->GetTriggerInfo(ctx, stream);
             }, this)));
}

BedMasterAppService::Service::~Service() {
}

::grpc::Status BedMasterAppService::Service::StartMove(::grpc::ServerContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::StopMove(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::GetPostId(::grpc::ServerContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::GainControl(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::ReleaseControl(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::HeartBeatCheck(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::GetSystemStatusInfo(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BedMasterAppService::Service::GetTriggerInfo(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace motor_control

