// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: motor_control.proto

#include "motor_control.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace motor_control {
constexpr StartMoveDescription::StartMoveDescription(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , targetmotioninfo_(nullptr)
  , mode_(0)

  , bedtype_(0)
{}
struct StartMoveDescriptionDefaultTypeInternal {
  constexpr StartMoveDescriptionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartMoveDescriptionDefaultTypeInternal() {}
  union {
    StartMoveDescription _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartMoveDescriptionDefaultTypeInternal _StartMoveDescription_default_instance_;
constexpr StartMoveStatus::StartMoveStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , currentmotioninfo_(nullptr)
  , errorcode_(uint64_t{0u}){}
struct StartMoveStatusDefaultTypeInternal {
  constexpr StartMoveStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartMoveStatusDefaultTypeInternal() {}
  union {
    StartMoveStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartMoveStatusDefaultTypeInternal _StartMoveStatus_default_instance_;
constexpr CommonDescription::CommonDescription(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CommonDescriptionDefaultTypeInternal {
  constexpr CommonDescriptionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CommonDescriptionDefaultTypeInternal() {}
  union {
    CommonDescription _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CommonDescriptionDefaultTypeInternal _CommonDescription_default_instance_;
constexpr CommonStatus::CommonStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , errorcode_(uint64_t{0u}){}
struct CommonStatusDefaultTypeInternal {
  constexpr CommonStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CommonStatusDefaultTypeInternal() {}
  union {
    CommonStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CommonStatusDefaultTypeInternal _CommonStatus_default_instance_;
constexpr GetPostIdDescription::GetPostIdDescription(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bedtype_(0)
{}
struct GetPostIdDescriptionDefaultTypeInternal {
  constexpr GetPostIdDescriptionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPostIdDescriptionDefaultTypeInternal() {}
  union {
    GetPostIdDescription _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPostIdDescriptionDefaultTypeInternal _GetPostIdDescription_default_instance_;
constexpr GetPostIdStatus::GetPostIdStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , postid_(nullptr)
  , errorcode_(uint64_t{0u}){}
struct GetPostIdStatusDefaultTypeInternal {
  constexpr GetPostIdStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPostIdStatusDefaultTypeInternal() {}
  union {
    GetPostIdStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPostIdStatusDefaultTypeInternal _GetPostIdStatus_default_instance_;
constexpr SystemStatusInfoStatus::SystemStatusInfoStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : softwareversion_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , firstbedstatus_(nullptr)
  , secondarybedstatus_(nullptr)
  , ownership_(0)
{}
struct SystemStatusInfoStatusDefaultTypeInternal {
  constexpr SystemStatusInfoStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SystemStatusInfoStatusDefaultTypeInternal() {}
  union {
    SystemStatusInfoStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SystemStatusInfoStatusDefaultTypeInternal _SystemStatusInfoStatus_default_instance_;
constexpr GetTriggerInfoDescription::GetTriggerInfoDescription(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetTriggerInfoDescriptionDefaultTypeInternal {
  constexpr GetTriggerInfoDescriptionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetTriggerInfoDescriptionDefaultTypeInternal() {}
  union {
    GetTriggerInfoDescription _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetTriggerInfoDescriptionDefaultTypeInternal _GetTriggerInfoDescription_default_instance_;
constexpr TriggerInfoStatus::TriggerInfoStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : contextuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , errorcode_(uint64_t{0u})
  , triggertimestamp_(uint64_t{0u})
  , triggerposition_(0)
  , exposuretime_(0u){}
struct TriggerInfoStatusDefaultTypeInternal {
  constexpr TriggerInfoStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TriggerInfoStatusDefaultTypeInternal() {}
  union {
    TriggerInfoStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TriggerInfoStatusDefaultTypeInternal _TriggerInfoStatus_default_instance_;
constexpr MotionInfo::MotionInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : postion_(0)
  , velocity_(0){}
struct MotionInfoDefaultTypeInternal {
  constexpr MotionInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotionInfoDefaultTypeInternal() {}
  union {
    MotionInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotionInfoDefaultTypeInternal _MotionInfo_default_instance_;
constexpr MotionCapability::MotionCapability(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : positionmin_(0)
  , positionmax_(0)
  , velocitymin_(0)
  , velocitymax_(0)
  , accelerationmax_(0)
  , decelerationmax_(0){}
struct MotionCapabilityDefaultTypeInternal {
  constexpr MotionCapabilityDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotionCapabilityDefaultTypeInternal() {}
  union {
    MotionCapability _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotionCapabilityDefaultTypeInternal _MotionCapability_default_instance_;
constexpr BedStatus::BedStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : motioninfo_(nullptr)
  , motioncapability_(nullptr)
  , motionstatus_(0)
{}
struct BedStatusDefaultTypeInternal {
  constexpr BedStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BedStatusDefaultTypeInternal() {}
  union {
    BedStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BedStatusDefaultTypeInternal _BedStatus_default_instance_;
constexpr PostIdInfo::PostIdInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vid_(0u)
  , did_(0u)
  , hwid_(0u)
  , rid_(0u){}
struct PostIdInfoDefaultTypeInternal {
  constexpr PostIdInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PostIdInfoDefaultTypeInternal() {}
  union {
    PostIdInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PostIdInfoDefaultTypeInternal _PostIdInfo_default_instance_;
constexpr MotorControlRequest::MotorControlRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , enable_(false){}
struct MotorControlRequestDefaultTypeInternal {
  constexpr MotorControlRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotorControlRequestDefaultTypeInternal() {}
  union {
    MotorControlRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotorControlRequestDefaultTypeInternal _MotorControlRequest_default_instance_;
constexpr MotorControlResponse::MotorControlResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , state_(0)
{}
struct MotorControlResponseDefaultTypeInternal {
  constexpr MotorControlResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotorControlResponseDefaultTypeInternal() {}
  union {
    MotorControlResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotorControlResponseDefaultTypeInternal _MotorControlResponse_default_instance_;
constexpr MotorStatusRequest::MotorStatusRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u){}
struct MotorStatusRequestDefaultTypeInternal {
  constexpr MotorStatusRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotorStatusRequestDefaultTypeInternal() {}
  union {
    MotorStatusRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotorStatusRequestDefaultTypeInternal _MotorStatusRequest_default_instance_;
constexpr MotorStatusResponse::MotorStatusResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , state_(0)

  , actual_position_(0)
  , actual_velocity_(0)
  , actual_torque_(0)
  , success_(false)
  , is_target_reached_(false)
  , has_fault_(false)
  , current_mode_(0)

  , status_word_(0u){}
struct MotorStatusResponseDefaultTypeInternal {
  constexpr MotorStatusResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MotorStatusResponseDefaultTypeInternal() {}
  union {
    MotorStatusResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MotorStatusResponseDefaultTypeInternal _MotorStatusResponse_default_instance_;
constexpr OperationModeRequest::OperationModeRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , mode_(0)
{}
struct OperationModeRequestDefaultTypeInternal {
  constexpr OperationModeRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OperationModeRequestDefaultTypeInternal() {}
  union {
    OperationModeRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OperationModeRequestDefaultTypeInternal _OperationModeRequest_default_instance_;
constexpr OperationModeResponse::OperationModeResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , current_mode_(0)
{}
struct OperationModeResponseDefaultTypeInternal {
  constexpr OperationModeResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OperationModeResponseDefaultTypeInternal() {}
  union {
    OperationModeResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OperationModeResponseDefaultTypeInternal _OperationModeResponse_default_instance_;
constexpr PositionControlRequest::PositionControlRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , target_position_(0)
  , profile_velocity_(0)
  , profile_acceleration_(0)
  , profile_deceleration_(0)
  , absolute_(false)
  , immediate_(false){}
struct PositionControlRequestDefaultTypeInternal {
  constexpr PositionControlRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PositionControlRequestDefaultTypeInternal() {}
  union {
    PositionControlRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PositionControlRequestDefaultTypeInternal _PositionControlRequest_default_instance_;
constexpr PositionControlResponse::PositionControlResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , actual_position_(0){}
struct PositionControlResponseDefaultTypeInternal {
  constexpr PositionControlResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PositionControlResponseDefaultTypeInternal() {}
  union {
    PositionControlResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PositionControlResponseDefaultTypeInternal _PositionControlResponse_default_instance_;
constexpr VelocityControlRequest::VelocityControlRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , target_velocity_(0)
  , profile_acceleration_(0)
  , profile_deceleration_(0){}
struct VelocityControlRequestDefaultTypeInternal {
  constexpr VelocityControlRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VelocityControlRequestDefaultTypeInternal() {}
  union {
    VelocityControlRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VelocityControlRequestDefaultTypeInternal _VelocityControlRequest_default_instance_;
constexpr VelocityControlResponse::VelocityControlResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , actual_velocity_(0){}
struct VelocityControlResponseDefaultTypeInternal {
  constexpr VelocityControlResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VelocityControlResponseDefaultTypeInternal() {}
  union {
    VelocityControlResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VelocityControlResponseDefaultTypeInternal _VelocityControlResponse_default_instance_;
constexpr TorqueControlRequest::TorqueControlRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , target_torque_(0)
  , torque_slope_(0){}
struct TorqueControlRequestDefaultTypeInternal {
  constexpr TorqueControlRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TorqueControlRequestDefaultTypeInternal() {}
  union {
    TorqueControlRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TorqueControlRequestDefaultTypeInternal _TorqueControlRequest_default_instance_;
constexpr TorqueControlResponse::TorqueControlResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , actual_torque_(0){}
struct TorqueControlResponseDefaultTypeInternal {
  constexpr TorqueControlResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TorqueControlResponseDefaultTypeInternal() {}
  union {
    TorqueControlResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TorqueControlResponseDefaultTypeInternal _TorqueControlResponse_default_instance_;
constexpr HomingRequest::HomingRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u)
  , homing_method_(0)
  , homing_speed_switch_(0)
  , homing_speed_zero_(0)
  , homing_acceleration_(0){}
struct HomingRequestDefaultTypeInternal {
  constexpr HomingRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HomingRequestDefaultTypeInternal() {}
  union {
    HomingRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HomingRequestDefaultTypeInternal _HomingRequest_default_instance_;
constexpr HomingResponse::HomingResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , homing_completed_(false){}
struct HomingResponseDefaultTypeInternal {
  constexpr HomingResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HomingResponseDefaultTypeInternal() {}
  union {
    HomingResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HomingResponseDefaultTypeInternal _HomingResponse_default_instance_;
constexpr EmergencyStopRequest::EmergencyStopRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u){}
struct EmergencyStopRequestDefaultTypeInternal {
  constexpr EmergencyStopRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmergencyStopRequestDefaultTypeInternal() {}
  union {
    EmergencyStopRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmergencyStopRequestDefaultTypeInternal _EmergencyStopRequest_default_instance_;
constexpr EmergencyStopResponse::EmergencyStopResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false){}
struct EmergencyStopResponseDefaultTypeInternal {
  constexpr EmergencyStopResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmergencyStopResponseDefaultTypeInternal() {}
  union {
    EmergencyStopResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmergencyStopResponseDefaultTypeInternal _EmergencyStopResponse_default_instance_;
constexpr ClearFaultRequest::ClearFaultRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_id_(0u){}
struct ClearFaultRequestDefaultTypeInternal {
  constexpr ClearFaultRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ClearFaultRequestDefaultTypeInternal() {}
  union {
    ClearFaultRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ClearFaultRequestDefaultTypeInternal _ClearFaultRequest_default_instance_;
constexpr ClearFaultResponse::ClearFaultResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false)
  , fault_cleared_(false){}
struct ClearFaultResponseDefaultTypeInternal {
  constexpr ClearFaultResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ClearFaultResponseDefaultTypeInternal() {}
  union {
    ClearFaultResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ClearFaultResponseDefaultTypeInternal _ClearFaultResponse_default_instance_;
constexpr GetAllNodesRequest::GetAllNodesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetAllNodesRequestDefaultTypeInternal {
  constexpr GetAllNodesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetAllNodesRequestDefaultTypeInternal() {}
  union {
    GetAllNodesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetAllNodesRequestDefaultTypeInternal _GetAllNodesRequest_default_instance_;
constexpr NodeInfo::NodeInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vendor_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , product_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , revision_number_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , node_id_(0u)
  , is_connected_(false)
  , state_(0)
{}
struct NodeInfoDefaultTypeInternal {
  constexpr NodeInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~NodeInfoDefaultTypeInternal() {}
  union {
    NodeInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT NodeInfoDefaultTypeInternal _NodeInfo_default_instance_;
constexpr GetAllNodesResponse::GetAllNodesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : nodes_()
  , message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , success_(false){}
struct GetAllNodesResponseDefaultTypeInternal {
  constexpr GetAllNodesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetAllNodesResponseDefaultTypeInternal() {}
  union {
    GetAllNodesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetAllNodesResponseDefaultTypeInternal _GetAllNodesResponse_default_instance_;
constexpr DeviceConfig::DeviceConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : device_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeviceConfigDefaultTypeInternal {
  constexpr DeviceConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeviceConfigDefaultTypeInternal() {}
  union {
    DeviceConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeviceConfigDefaultTypeInternal _DeviceConfig_default_instance_;
constexpr ConfigMessage::ConfigMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_ids_()
  , _node_ids_cached_byte_size_(0)
  , uart_devices_()
  , i2c_devices_()
  , can_interface_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , grpc_server_address_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_socket_port_(0){}
struct ConfigMessageDefaultTypeInternal {
  constexpr ConfigMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigMessageDefaultTypeInternal() {}
  union {
    ConfigMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigMessageDefaultTypeInternal _ConfigMessage_default_instance_;
}  // namespace motor_control
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_motor_5fcontrol_2eproto[36];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_motor_5fcontrol_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_motor_5fcontrol_2eproto = nullptr;

const uint32_t TableStruct_motor_5fcontrol_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveDescription, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveDescription, mode_),
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveDescription, bedtype_),
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveDescription, targetmotioninfo_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveStatus, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveStatus, errorcode_),
  PROTOBUF_FIELD_OFFSET(::motor_control::StartMoveStatus, currentmotioninfo_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::CommonDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::CommonDescription, contextuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::CommonStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::CommonStatus, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::CommonStatus, errorcode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdDescription, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdDescription, bedtype_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdStatus, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdStatus, errorcode_),
  PROTOBUF_FIELD_OFFSET(::motor_control::GetPostIdStatus, postid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, softwareversion_),
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, timestamp_),
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, firstbedstatus_),
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, secondarybedstatus_),
  PROTOBUF_FIELD_OFFSET(::motor_control::SystemStatusInfoStatus, ownership_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetTriggerInfoDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetTriggerInfoDescription, contextuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, contextuid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, errorcode_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, triggerposition_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, triggertimestamp_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TriggerInfoStatus, exposuretime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionInfo, postion_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionInfo, velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, positionmin_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, positionmax_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, velocitymin_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, velocitymax_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, accelerationmax_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotionCapability, decelerationmax_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::BedStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::BedStatus, motioninfo_),
  PROTOBUF_FIELD_OFFSET(::motor_control::BedStatus, motionstatus_),
  PROTOBUF_FIELD_OFFSET(::motor_control::BedStatus, motioncapability_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::PostIdInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::PostIdInfo, vid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PostIdInfo, did_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PostIdInfo, hwid_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PostIdInfo, rid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlRequest, enable_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorControlResponse, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusRequest, node_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, state_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, actual_position_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, actual_velocity_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, actual_torque_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, current_mode_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, status_word_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, is_target_reached_),
  PROTOBUF_FIELD_OFFSET(::motor_control::MotorStatusResponse, has_fault_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeRequest, mode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::OperationModeResponse, current_mode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, target_position_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, profile_velocity_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, profile_acceleration_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, profile_deceleration_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, absolute_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlRequest, immediate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::PositionControlResponse, actual_position_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlRequest, target_velocity_),
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlRequest, profile_acceleration_),
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlRequest, profile_deceleration_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::VelocityControlResponse, actual_velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlRequest, target_torque_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlRequest, torque_slope_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::TorqueControlResponse, actual_torque_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, homing_method_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, homing_speed_switch_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, homing_speed_zero_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingRequest, homing_acceleration_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::HomingResponse, homing_completed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::EmergencyStopRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::EmergencyStopRequest, node_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::EmergencyStopResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::EmergencyStopResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::EmergencyStopResponse, message_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultRequest, node_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ClearFaultResponse, fault_cleared_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetAllNodesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, node_id_),
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, is_connected_),
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, state_),
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, vendor_name_),
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, product_name_),
  PROTOBUF_FIELD_OFFSET(::motor_control::NodeInfo, revision_number_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetAllNodesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::GetAllNodesResponse, success_),
  PROTOBUF_FIELD_OFFSET(::motor_control::GetAllNodesResponse, message_),
  PROTOBUF_FIELD_OFFSET(::motor_control::GetAllNodesResponse, nodes_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::DeviceConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::DeviceConfig, device_),
  PROTOBUF_FIELD_OFFSET(::motor_control::DeviceConfig, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, can_interface_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, grpc_server_address_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, status_socket_port_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, node_ids_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, uart_devices_),
  PROTOBUF_FIELD_OFFSET(::motor_control::ConfigMessage, i2c_devices_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::motor_control::StartMoveDescription)},
  { 10, -1, -1, sizeof(::motor_control::StartMoveStatus)},
  { 19, -1, -1, sizeof(::motor_control::CommonDescription)},
  { 26, -1, -1, sizeof(::motor_control::CommonStatus)},
  { 34, -1, -1, sizeof(::motor_control::GetPostIdDescription)},
  { 42, -1, -1, sizeof(::motor_control::GetPostIdStatus)},
  { 51, -1, -1, sizeof(::motor_control::SystemStatusInfoStatus)},
  { 62, -1, -1, sizeof(::motor_control::GetTriggerInfoDescription)},
  { 69, -1, -1, sizeof(::motor_control::TriggerInfoStatus)},
  { 80, -1, -1, sizeof(::motor_control::MotionInfo)},
  { 88, -1, -1, sizeof(::motor_control::MotionCapability)},
  { 100, -1, -1, sizeof(::motor_control::BedStatus)},
  { 109, -1, -1, sizeof(::motor_control::PostIdInfo)},
  { 119, -1, -1, sizeof(::motor_control::MotorControlRequest)},
  { 127, -1, -1, sizeof(::motor_control::MotorControlResponse)},
  { 136, -1, -1, sizeof(::motor_control::MotorStatusRequest)},
  { 143, -1, -1, sizeof(::motor_control::MotorStatusResponse)},
  { 159, -1, -1, sizeof(::motor_control::OperationModeRequest)},
  { 167, -1, -1, sizeof(::motor_control::OperationModeResponse)},
  { 176, -1, -1, sizeof(::motor_control::PositionControlRequest)},
  { 189, -1, -1, sizeof(::motor_control::PositionControlResponse)},
  { 198, -1, -1, sizeof(::motor_control::VelocityControlRequest)},
  { 208, -1, -1, sizeof(::motor_control::VelocityControlResponse)},
  { 217, -1, -1, sizeof(::motor_control::TorqueControlRequest)},
  { 226, -1, -1, sizeof(::motor_control::TorqueControlResponse)},
  { 235, -1, -1, sizeof(::motor_control::HomingRequest)},
  { 246, -1, -1, sizeof(::motor_control::HomingResponse)},
  { 255, -1, -1, sizeof(::motor_control::EmergencyStopRequest)},
  { 262, -1, -1, sizeof(::motor_control::EmergencyStopResponse)},
  { 270, -1, -1, sizeof(::motor_control::ClearFaultRequest)},
  { 277, -1, -1, sizeof(::motor_control::ClearFaultResponse)},
  { 286, -1, -1, sizeof(::motor_control::GetAllNodesRequest)},
  { 292, -1, -1, sizeof(::motor_control::NodeInfo)},
  { 304, -1, -1, sizeof(::motor_control::GetAllNodesResponse)},
  { 313, -1, -1, sizeof(::motor_control::DeviceConfig)},
  { 321, -1, -1, sizeof(::motor_control::ConfigMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_StartMoveDescription_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_StartMoveStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_CommonDescription_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_CommonStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_GetPostIdDescription_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_GetPostIdStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_SystemStatusInfoStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_GetTriggerInfoDescription_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_TriggerInfoStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotionInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotionCapability_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_BedStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_PostIdInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotorControlRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotorControlResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotorStatusRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_MotorStatusResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_OperationModeRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_OperationModeResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_PositionControlRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_PositionControlResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_VelocityControlRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_VelocityControlResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_TorqueControlRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_TorqueControlResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_HomingRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_HomingResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_EmergencyStopRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_EmergencyStopResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_ClearFaultRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_ClearFaultResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_GetAllNodesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_NodeInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_GetAllNodesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_DeviceConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::motor_control::_ConfigMessage_default_instance_),
};

const char descriptor_table_protodef_motor_5fcontrol_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023motor_control.proto\022\rmotor_control\"\261\001\n"
  "\024StartMoveDescription\022\022\n\ncontextUID\030\001 \001("
  "\t\022\'\n\004mode\030\002 \001(\0162\031.motor_control.MotionMo"
  "de\022\'\n\007bedType\030\003 \001(\0162\026.motor_control.BedT"
  "ype\0223\n\020targetMotionInfo\030\004 \001(\0132\031.motor_co"
  "ntrol.MotionInfo\"n\n\017StartMoveStatus\022\022\n\nc"
  "ontextUID\030\001 \001(\t\022\021\n\terrorCode\030\002 \001(\004\0224\n\021cu"
  "rrentMotionInfo\030\003 \001(\0132\031.motor_control.Mo"
  "tionInfo\"\'\n\021CommonDescription\022\022\n\ncontext"
  "UID\030\001 \001(\t\"5\n\014CommonStatus\022\022\n\ncontextUID\030"
  "\001 \001(\t\022\021\n\terrorCode\030\002 \001(\004\"S\n\024GetPostIdDes"
  "cription\022\022\n\ncontextUID\030\001 \001(\t\022\'\n\007bedType\030"
  "\002 \001(\0162\026.motor_control.BedType\"c\n\017GetPost"
  "IdStatus\022\022\n\ncontextUID\030\001 \001(\t\022\021\n\terrorCod"
  "e\030\002 \001(\004\022)\n\006postId\030\003 \001(\0132\031.motor_control."
  "PostIdInfo\"\330\001\n\026SystemStatusInfoStatus\022\027\n"
  "\017softwareVersion\030\001 \001(\t\022\021\n\ttimeStamp\030\002 \001("
  "\t\0220\n\016firstBedStatus\030\003 \001(\0132\030.motor_contro"
  "l.BedStatus\0224\n\022secondaryBedStatus\030\004 \001(\0132"
  "\030.motor_control.BedStatus\022*\n\townership\030\005"
  " \001(\0162\027.motor_control.HostType\"/\n\031GetTrig"
  "gerInfoDescription\022\022\n\ncontextUID\030\001 \001(\t\"\203"
  "\001\n\021TriggerInfoStatus\022\022\n\ncontextUID\030\001 \001(\t"
  "\022\021\n\terrorCode\030\002 \001(\004\022\027\n\017triggerPosition\030\003"
  " \001(\002\022\030\n\020triggerTimestamp\030\004 \001(\004\022\024\n\014exposu"
  "reTime\030\005 \001(\r\"/\n\nMotionInfo\022\017\n\007postion\030\001 "
  "\001(\002\022\020\n\010velocity\030\002 \001(\002\"\230\001\n\020MotionCapabili"
  "ty\022\023\n\013positionMin\030\001 \001(\002\022\023\n\013positionMax\030\002"
  " \001(\002\022\023\n\013velocityMin\030\003 \001(\002\022\023\n\013velocityMax"
  "\030\004 \001(\002\022\027\n\017accelerationMax\030\005 \001(\002\022\027\n\017decel"
  "erationMax\030\006 \001(\002\"\250\001\n\tBedStatus\022-\n\nmotion"
  "Info\030\001 \001(\0132\031.motor_control.MotionInfo\0221\n"
  "\014motionStatus\030\002 \001(\0162\033.motor_control.Moti"
  "onStatus\0229\n\020motionCapability\030\003 \001(\0132\037.mot"
  "or_control.MotionCapability\"A\n\nPostIdInf"
  "o\022\013\n\003VID\030\001 \001(\r\022\013\n\003DID\030\002 \001(\r\022\014\n\004HWID\030\003 \001("
  "\r\022\013\n\003RID\030\004 \001(\r\"6\n\023MotorControlRequest\022\017\n"
  "\007node_id\030\001 \001(\r\022\016\n\006enable\030\002 \001(\010\"b\n\024MotorC"
  "ontrolResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007messa"
  "ge\030\002 \001(\t\022(\n\005state\030\003 \001(\0162\031.motor_control."
  "DriveState\"%\n\022MotorStatusRequest\022\017\n\007node"
  "_id\030\001 \001(\r\"\241\002\n\023MotorStatusResponse\022\017\n\007suc"
  "cess\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022(\n\005state\030\003 \001"
  "(\0162\031.motor_control.DriveState\022\027\n\017actual_"
  "position\030\004 \001(\005\022\027\n\017actual_velocity\030\005 \001(\005\022"
  "\025\n\ractual_torque\030\006 \001(\005\0222\n\014current_mode\030\007"
  " \001(\0162\034.motor_control.OperationMode\022\023\n\013st"
  "atus_word\030\010 \001(\r\022\031\n\021is_target_reached\030\t \001"
  "(\010\022\021\n\thas_fault\030\n \001(\010\"S\n\024OperationModeRe"
  "quest\022\017\n\007node_id\030\001 \001(\r\022*\n\004mode\030\002 \001(\0162\034.m"
  "otor_control.OperationMode\"m\n\025OperationM"
  "odeResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030"
  "\002 \001(\t\0222\n\014current_mode\030\003 \001(\0162\034.motor_cont"
  "rol.OperationMode\"\275\001\n\026PositionControlReq"
  "uest\022\017\n\007node_id\030\001 \001(\r\022\027\n\017target_position"
  "\030\002 \001(\005\022\030\n\020profile_velocity\030\003 \001(\005\022\034\n\024prof"
  "ile_acceleration\030\004 \001(\005\022\034\n\024profile_decele"
  "ration\030\005 \001(\005\022\020\n\010absolute\030\006 \001(\010\022\021\n\timmedi"
  "ate\030\007 \001(\010\"T\n\027PositionControlResponse\022\017\n\007"
  "success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\027\n\017actual"
  "_position\030\003 \001(\005\"~\n\026VelocityControlReques"
  "t\022\017\n\007node_id\030\001 \001(\r\022\027\n\017target_velocity\030\002 "
  "\001(\005\022\034\n\024profile_acceleration\030\003 \001(\005\022\034\n\024pro"
  "file_deceleration\030\004 \001(\005\"T\n\027VelocityContr"
  "olResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002"
  " \001(\t\022\027\n\017actual_velocity\030\003 \001(\005\"T\n\024TorqueC"
  "ontrolRequest\022\017\n\007node_id\030\001 \001(\r\022\025\n\rtarget"
  "_torque\030\002 \001(\005\022\024\n\014torque_slope\030\003 \001(\005\"P\n\025T"
  "orqueControlResponse\022\017\n\007success\030\001 \001(\010\022\017\n"
  "\007message\030\002 \001(\t\022\025\n\ractual_torque\030\003 \001(\005\"\214\001"
  "\n\rHomingRequest\022\017\n\007node_id\030\001 \001(\r\022\025\n\rhomi"
  "ng_method\030\002 \001(\005\022\033\n\023homing_speed_switch\030\003"
  " \001(\005\022\031\n\021homing_speed_zero\030\004 \001(\005\022\033\n\023homin"
  "g_acceleration\030\005 \001(\005\"L\n\016HomingResponse\022\017"
  "\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\030\n\020homi"
  "ng_completed\030\003 \001(\010\"\'\n\024EmergencyStopReque"
  "st\022\017\n\007node_id\030\001 \001(\r\"9\n\025EmergencyStopResp"
  "onse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\"$"
  "\n\021ClearFaultRequest\022\017\n\007node_id\030\001 \001(\r\"M\n\022"
  "ClearFaultResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007m"
  "essage\030\002 \001(\t\022\025\n\rfault_cleared\030\003 \001(\010\"\024\n\022G"
  "etAllNodesRequest\"\237\001\n\010NodeInfo\022\017\n\007node_i"
  "d\030\001 \001(\r\022\024\n\014is_connected\030\002 \001(\010\022(\n\005state\030\003"
  " \001(\0162\031.motor_control.DriveState\022\023\n\013vendo"
  "r_name\030\004 \001(\t\022\024\n\014product_name\030\005 \001(\t\022\027\n\017re"
  "vision_number\030\006 \001(\t\"_\n\023GetAllNodesRespon"
  "se\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022&\n\005"
  "nodes\030\003 \003(\0132\027.motor_control.NodeInfo\",\n\014"
  "DeviceConfig\022\016\n\006device\030\001 \001(\t\022\014\n\004name\030\002 \001"
  "(\t\"\326\001\n\rConfigMessage\022\025\n\rcan_interface\030\001 "
  "\001(\t\022\033\n\023grpc_server_address\030\002 \001(\t\022\032\n\022stat"
  "us_socket_port\030\003 \001(\005\022\020\n\010node_ids\030\004 \003(\r\0221"
  "\n\014uart_devices\030\005 \003(\0132\033.motor_control.Dev"
  "iceConfig\0220\n\013i2c_devices\030\006 \003(\0132\033.motor_c"
  "ontrol.DeviceConfig*/\n\007BedType\022\013\n\007Primar"
  "y\020\000\022\r\n\tSecondary\020\001\022\010\n\004Both\020\002*@\n\nMotionMo"
  "de\022\020\n\014PositionMode\020\000\022\020\n\014VelocityMode\020\001\022\016"
  "\n\nHomingMode\020\002*;\n\014MotionStatus\022\t\n\005Ready\020"
  "\000\022\n\n\006Moving\020\001\022\t\n\005Estop\020\002\022\t\n\005Error\020\003*0\n\010H"
  "ostType\022\006\n\002CT\020\000\022\007\n\003PET\020\001\022\t\n\005SPECT\020\002\022\010\n\004N"
  "ONE\020\003*\320\001\n\rOperationMode\022\024\n\020PROFILE_POSIT"
  "ION\020\000\022\014\n\010VELOCITY\020\001\022\024\n\020PROFILE_VELOCITY\020"
  "\002\022\022\n\016PROFILE_TORQUE\020\003\022\n\n\006HOMING\020\006\022\031\n\025INT"
  "ERPOLATED_POSITION\020\007\022\030\n\024CYCLIC_SYNC_POSI"
  "TION\020\010\022\030\n\024CYCLIC_SYNC_VELOCITY\020\t\022\026\n\022CYCL"
  "IC_SYNC_TORQUE\020\n*\275\001\n\nDriveState\022\032\n\026NOT_R"
  "EADY_TO_SWITCH_ON\020\000\022\026\n\022SWITCH_ON_DISABLE"
  "D\020\001\022\026\n\022READY_TO_SWITCH_ON\020\002\022\017\n\013SWITCHED_"
  "ON\020\003\022\025\n\021OPERATION_ENABLED\020\004\022\025\n\021QUICK_STO"
  "P_ACTIVE\020\005\022\031\n\025FAULT_REACTION_ACTIVE\020\006\022\t\n"
  "\005FAULT\020\0072\306\005\n\023BedMasterAppService\022R\n\tStar"
  "tMove\022#.motor_control.StartMoveDescripti"
  "on\032\036.motor_control.StartMoveStatus\"\000\022K\n\010"
  "StopMove\022 .motor_control.CommonDescripti"
  "on\032\033.motor_control.CommonStatus\"\000\022R\n\tGet"
  "PostId\022#.motor_control.GetPostIdDescript"
  "ion\032\036.motor_control.GetPostIdStatus\"\000\022N\n"
  "\013GainControl\022 .motor_control.CommonDescr"
  "iption\032\033.motor_control.CommonStatus\"\000\022Q\n"
  "\016ReleaseControl\022 .motor_control.CommonDe"
  "scription\032\033.motor_control.CommonStatus\"\000"
  "\022Q\n\016HeartBeatCheck\022 .motor_control.Commo"
  "nDescription\032\033.motor_control.CommonStatu"
  "s\"\000\022`\n\023GetSystemStatusInfo\022 .motor_contr"
  "ol.CommonDescription\032%.motor_control.Sys"
  "temStatusInfoStatus\"\000\022b\n\016GetTriggerInfo\022"
  "(.motor_control.GetTriggerInfoDescriptio"
  "n\032 .motor_control.TriggerInfoStatus\"\000(\0010"
  "\001b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_motor_5fcontrol_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_motor_5fcontrol_2eproto = {
  false, false, 5129, descriptor_table_protodef_motor_5fcontrol_2eproto, "motor_control.proto", 
  &descriptor_table_motor_5fcontrol_2eproto_once, nullptr, 0, 36,
  schemas, file_default_instances, TableStruct_motor_5fcontrol_2eproto::offsets,
  file_level_metadata_motor_5fcontrol_2eproto, file_level_enum_descriptors_motor_5fcontrol_2eproto, file_level_service_descriptors_motor_5fcontrol_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_motor_5fcontrol_2eproto_getter() {
  return &descriptor_table_motor_5fcontrol_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_motor_5fcontrol_2eproto(&descriptor_table_motor_5fcontrol_2eproto);
namespace motor_control {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* BedType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[0];
}
bool BedType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MotionMode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[1];
}
bool MotionMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MotionStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[2];
}
bool MotionStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HostType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[3];
}
bool HostType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OperationMode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[4];
}
bool OperationMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DriveState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_motor_5fcontrol_2eproto);
  return file_level_enum_descriptors_motor_5fcontrol_2eproto[5];
}
bool DriveState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class StartMoveDescription::_Internal {
 public:
  static const ::motor_control::MotionInfo& targetmotioninfo(const StartMoveDescription* msg);
};

const ::motor_control::MotionInfo&
StartMoveDescription::_Internal::targetmotioninfo(const StartMoveDescription* msg) {
  return *msg->targetmotioninfo_;
}
StartMoveDescription::StartMoveDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.StartMoveDescription)
}
StartMoveDescription::StartMoveDescription(const StartMoveDescription& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_targetmotioninfo()) {
    targetmotioninfo_ = new ::motor_control::MotionInfo(*from.targetmotioninfo_);
  } else {
    targetmotioninfo_ = nullptr;
  }
  ::memcpy(&mode_, &from.mode_,
    static_cast<size_t>(reinterpret_cast<char*>(&bedtype_) -
    reinterpret_cast<char*>(&mode_)) + sizeof(bedtype_));
  // @@protoc_insertion_point(copy_constructor:motor_control.StartMoveDescription)
}

inline void StartMoveDescription::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&targetmotioninfo_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bedtype_) -
    reinterpret_cast<char*>(&targetmotioninfo_)) + sizeof(bedtype_));
}

StartMoveDescription::~StartMoveDescription() {
  // @@protoc_insertion_point(destructor:motor_control.StartMoveDescription)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartMoveDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete targetmotioninfo_;
}

void StartMoveDescription::ArenaDtor(void* object) {
  StartMoveDescription* _this = reinterpret_cast< StartMoveDescription* >(object);
  (void)_this;
}
void StartMoveDescription::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartMoveDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartMoveDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.StartMoveDescription)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && targetmotioninfo_ != nullptr) {
    delete targetmotioninfo_;
  }
  targetmotioninfo_ = nullptr;
  ::memset(&mode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bedtype_) -
      reinterpret_cast<char*>(&mode_)) + sizeof(bedtype_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartMoveDescription::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.StartMoveDescription.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.MotionMode mode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_mode(static_cast<::motor_control::MotionMode>(val));
        } else
          goto handle_unusual;
        continue;
      // .motor_control.BedType bedType = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bedtype(static_cast<::motor_control::BedType>(val));
        } else
          goto handle_unusual;
        continue;
      // .motor_control.MotionInfo targetMotionInfo = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_targetmotioninfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartMoveDescription::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.StartMoveDescription)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.StartMoveDescription.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // .motor_control.MotionMode mode = 2;
  if (this->_internal_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_mode(), target);
  }

  // .motor_control.BedType bedType = 3;
  if (this->_internal_bedtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_bedtype(), target);
  }

  // .motor_control.MotionInfo targetMotionInfo = 4;
  if (this->_internal_has_targetmotioninfo()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::targetmotioninfo(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.StartMoveDescription)
  return target;
}

size_t StartMoveDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.StartMoveDescription)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // .motor_control.MotionInfo targetMotionInfo = 4;
  if (this->_internal_has_targetmotioninfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *targetmotioninfo_);
  }

  // .motor_control.MotionMode mode = 2;
  if (this->_internal_mode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_mode());
  }

  // .motor_control.BedType bedType = 3;
  if (this->_internal_bedtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bedtype());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartMoveDescription::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartMoveDescription::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartMoveDescription::GetClassData() const { return &_class_data_; }

void StartMoveDescription::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartMoveDescription *>(to)->MergeFrom(
      static_cast<const StartMoveDescription &>(from));
}


void StartMoveDescription::MergeFrom(const StartMoveDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.StartMoveDescription)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_has_targetmotioninfo()) {
    _internal_mutable_targetmotioninfo()->::motor_control::MotionInfo::MergeFrom(from._internal_targetmotioninfo());
  }
  if (from._internal_mode() != 0) {
    _internal_set_mode(from._internal_mode());
  }
  if (from._internal_bedtype() != 0) {
    _internal_set_bedtype(from._internal_bedtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartMoveDescription::CopyFrom(const StartMoveDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.StartMoveDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartMoveDescription::IsInitialized() const {
  return true;
}

void StartMoveDescription::InternalSwap(StartMoveDescription* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StartMoveDescription, bedtype_)
      + sizeof(StartMoveDescription::bedtype_)
      - PROTOBUF_FIELD_OFFSET(StartMoveDescription, targetmotioninfo_)>(
          reinterpret_cast<char*>(&targetmotioninfo_),
          reinterpret_cast<char*>(&other->targetmotioninfo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StartMoveDescription::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[0]);
}

// ===================================================================

class StartMoveStatus::_Internal {
 public:
  static const ::motor_control::MotionInfo& currentmotioninfo(const StartMoveStatus* msg);
};

const ::motor_control::MotionInfo&
StartMoveStatus::_Internal::currentmotioninfo(const StartMoveStatus* msg) {
  return *msg->currentmotioninfo_;
}
StartMoveStatus::StartMoveStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.StartMoveStatus)
}
StartMoveStatus::StartMoveStatus(const StartMoveStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_currentmotioninfo()) {
    currentmotioninfo_ = new ::motor_control::MotionInfo(*from.currentmotioninfo_);
  } else {
    currentmotioninfo_ = nullptr;
  }
  errorcode_ = from.errorcode_;
  // @@protoc_insertion_point(copy_constructor:motor_control.StartMoveStatus)
}

inline void StartMoveStatus::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&currentmotioninfo_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&errorcode_) -
    reinterpret_cast<char*>(&currentmotioninfo_)) + sizeof(errorcode_));
}

StartMoveStatus::~StartMoveStatus() {
  // @@protoc_insertion_point(destructor:motor_control.StartMoveStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartMoveStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete currentmotioninfo_;
}

void StartMoveStatus::ArenaDtor(void* object) {
  StartMoveStatus* _this = reinterpret_cast< StartMoveStatus* >(object);
  (void)_this;
}
void StartMoveStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartMoveStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartMoveStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.StartMoveStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && currentmotioninfo_ != nullptr) {
    delete currentmotioninfo_;
  }
  currentmotioninfo_ = nullptr;
  errorcode_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartMoveStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.StartMoveStatus.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 errorCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          errorcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.MotionInfo currentMotionInfo = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_currentmotioninfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartMoveStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.StartMoveStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.StartMoveStatus.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_errorcode(), target);
  }

  // .motor_control.MotionInfo currentMotionInfo = 3;
  if (this->_internal_has_currentmotioninfo()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::currentmotioninfo(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.StartMoveStatus)
  return target;
}

size_t StartMoveStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.StartMoveStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // .motor_control.MotionInfo currentMotionInfo = 3;
  if (this->_internal_has_currentmotioninfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *currentmotioninfo_);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_errorcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartMoveStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartMoveStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartMoveStatus::GetClassData() const { return &_class_data_; }

void StartMoveStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartMoveStatus *>(to)->MergeFrom(
      static_cast<const StartMoveStatus &>(from));
}


void StartMoveStatus::MergeFrom(const StartMoveStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.StartMoveStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_has_currentmotioninfo()) {
    _internal_mutable_currentmotioninfo()->::motor_control::MotionInfo::MergeFrom(from._internal_currentmotioninfo());
  }
  if (from._internal_errorcode() != 0) {
    _internal_set_errorcode(from._internal_errorcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartMoveStatus::CopyFrom(const StartMoveStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.StartMoveStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartMoveStatus::IsInitialized() const {
  return true;
}

void StartMoveStatus::InternalSwap(StartMoveStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StartMoveStatus, errorcode_)
      + sizeof(StartMoveStatus::errorcode_)
      - PROTOBUF_FIELD_OFFSET(StartMoveStatus, currentmotioninfo_)>(
          reinterpret_cast<char*>(&currentmotioninfo_),
          reinterpret_cast<char*>(&other->currentmotioninfo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StartMoveStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[1]);
}

// ===================================================================

class CommonDescription::_Internal {
 public:
};

CommonDescription::CommonDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.CommonDescription)
}
CommonDescription::CommonDescription(const CommonDescription& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:motor_control.CommonDescription)
}

inline void CommonDescription::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CommonDescription::~CommonDescription() {
  // @@protoc_insertion_point(destructor:motor_control.CommonDescription)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CommonDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CommonDescription::ArenaDtor(void* object) {
  CommonDescription* _this = reinterpret_cast< CommonDescription* >(object);
  (void)_this;
}
void CommonDescription::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CommonDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CommonDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.CommonDescription)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CommonDescription::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.CommonDescription.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CommonDescription::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.CommonDescription)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.CommonDescription.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.CommonDescription)
  return target;
}

size_t CommonDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.CommonDescription)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CommonDescription::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CommonDescription::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CommonDescription::GetClassData() const { return &_class_data_; }

void CommonDescription::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CommonDescription *>(to)->MergeFrom(
      static_cast<const CommonDescription &>(from));
}


void CommonDescription::MergeFrom(const CommonDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.CommonDescription)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CommonDescription::CopyFrom(const CommonDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.CommonDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CommonDescription::IsInitialized() const {
  return true;
}

void CommonDescription::InternalSwap(CommonDescription* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata CommonDescription::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[2]);
}

// ===================================================================

class CommonStatus::_Internal {
 public:
};

CommonStatus::CommonStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.CommonStatus)
}
CommonStatus::CommonStatus(const CommonStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  errorcode_ = from.errorcode_;
  // @@protoc_insertion_point(copy_constructor:motor_control.CommonStatus)
}

inline void CommonStatus::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
errorcode_ = uint64_t{0u};
}

CommonStatus::~CommonStatus() {
  // @@protoc_insertion_point(destructor:motor_control.CommonStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CommonStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CommonStatus::ArenaDtor(void* object) {
  CommonStatus* _this = reinterpret_cast< CommonStatus* >(object);
  (void)_this;
}
void CommonStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CommonStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CommonStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.CommonStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  errorcode_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CommonStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.CommonStatus.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 errorCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          errorcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CommonStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.CommonStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.CommonStatus.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_errorcode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.CommonStatus)
  return target;
}

size_t CommonStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.CommonStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_errorcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CommonStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CommonStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CommonStatus::GetClassData() const { return &_class_data_; }

void CommonStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CommonStatus *>(to)->MergeFrom(
      static_cast<const CommonStatus &>(from));
}


void CommonStatus::MergeFrom(const CommonStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.CommonStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_errorcode() != 0) {
    _internal_set_errorcode(from._internal_errorcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CommonStatus::CopyFrom(const CommonStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.CommonStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CommonStatus::IsInitialized() const {
  return true;
}

void CommonStatus::InternalSwap(CommonStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  swap(errorcode_, other->errorcode_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CommonStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[3]);
}

// ===================================================================

class GetPostIdDescription::_Internal {
 public:
};

GetPostIdDescription::GetPostIdDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.GetPostIdDescription)
}
GetPostIdDescription::GetPostIdDescription(const GetPostIdDescription& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  bedtype_ = from.bedtype_;
  // @@protoc_insertion_point(copy_constructor:motor_control.GetPostIdDescription)
}

inline void GetPostIdDescription::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bedtype_ = 0;
}

GetPostIdDescription::~GetPostIdDescription() {
  // @@protoc_insertion_point(destructor:motor_control.GetPostIdDescription)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPostIdDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetPostIdDescription::ArenaDtor(void* object) {
  GetPostIdDescription* _this = reinterpret_cast< GetPostIdDescription* >(object);
  (void)_this;
}
void GetPostIdDescription::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPostIdDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPostIdDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.GetPostIdDescription)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  bedtype_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPostIdDescription::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.GetPostIdDescription.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.BedType bedType = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_bedtype(static_cast<::motor_control::BedType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPostIdDescription::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.GetPostIdDescription)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.GetPostIdDescription.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // .motor_control.BedType bedType = 2;
  if (this->_internal_bedtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_bedtype(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.GetPostIdDescription)
  return target;
}

size_t GetPostIdDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.GetPostIdDescription)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // .motor_control.BedType bedType = 2;
  if (this->_internal_bedtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_bedtype());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPostIdDescription::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPostIdDescription::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPostIdDescription::GetClassData() const { return &_class_data_; }

void GetPostIdDescription::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPostIdDescription *>(to)->MergeFrom(
      static_cast<const GetPostIdDescription &>(from));
}


void GetPostIdDescription::MergeFrom(const GetPostIdDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.GetPostIdDescription)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_bedtype() != 0) {
    _internal_set_bedtype(from._internal_bedtype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPostIdDescription::CopyFrom(const GetPostIdDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.GetPostIdDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPostIdDescription::IsInitialized() const {
  return true;
}

void GetPostIdDescription::InternalSwap(GetPostIdDescription* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  swap(bedtype_, other->bedtype_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPostIdDescription::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[4]);
}

// ===================================================================

class GetPostIdStatus::_Internal {
 public:
  static const ::motor_control::PostIdInfo& postid(const GetPostIdStatus* msg);
};

const ::motor_control::PostIdInfo&
GetPostIdStatus::_Internal::postid(const GetPostIdStatus* msg) {
  return *msg->postid_;
}
GetPostIdStatus::GetPostIdStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.GetPostIdStatus)
}
GetPostIdStatus::GetPostIdStatus(const GetPostIdStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_postid()) {
    postid_ = new ::motor_control::PostIdInfo(*from.postid_);
  } else {
    postid_ = nullptr;
  }
  errorcode_ = from.errorcode_;
  // @@protoc_insertion_point(copy_constructor:motor_control.GetPostIdStatus)
}

inline void GetPostIdStatus::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&postid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&errorcode_) -
    reinterpret_cast<char*>(&postid_)) + sizeof(errorcode_));
}

GetPostIdStatus::~GetPostIdStatus() {
  // @@protoc_insertion_point(destructor:motor_control.GetPostIdStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPostIdStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete postid_;
}

void GetPostIdStatus::ArenaDtor(void* object) {
  GetPostIdStatus* _this = reinterpret_cast< GetPostIdStatus* >(object);
  (void)_this;
}
void GetPostIdStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPostIdStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPostIdStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.GetPostIdStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && postid_ != nullptr) {
    delete postid_;
  }
  postid_ = nullptr;
  errorcode_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPostIdStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.GetPostIdStatus.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 errorCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          errorcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.PostIdInfo postId = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_postid(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPostIdStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.GetPostIdStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.GetPostIdStatus.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_errorcode(), target);
  }

  // .motor_control.PostIdInfo postId = 3;
  if (this->_internal_has_postid()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::postid(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.GetPostIdStatus)
  return target;
}

size_t GetPostIdStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.GetPostIdStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // .motor_control.PostIdInfo postId = 3;
  if (this->_internal_has_postid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *postid_);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_errorcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPostIdStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPostIdStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPostIdStatus::GetClassData() const { return &_class_data_; }

void GetPostIdStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPostIdStatus *>(to)->MergeFrom(
      static_cast<const GetPostIdStatus &>(from));
}


void GetPostIdStatus::MergeFrom(const GetPostIdStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.GetPostIdStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_has_postid()) {
    _internal_mutable_postid()->::motor_control::PostIdInfo::MergeFrom(from._internal_postid());
  }
  if (from._internal_errorcode() != 0) {
    _internal_set_errorcode(from._internal_errorcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPostIdStatus::CopyFrom(const GetPostIdStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.GetPostIdStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPostIdStatus::IsInitialized() const {
  return true;
}

void GetPostIdStatus::InternalSwap(GetPostIdStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetPostIdStatus, errorcode_)
      + sizeof(GetPostIdStatus::errorcode_)
      - PROTOBUF_FIELD_OFFSET(GetPostIdStatus, postid_)>(
          reinterpret_cast<char*>(&postid_),
          reinterpret_cast<char*>(&other->postid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPostIdStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[5]);
}

// ===================================================================

class SystemStatusInfoStatus::_Internal {
 public:
  static const ::motor_control::BedStatus& firstbedstatus(const SystemStatusInfoStatus* msg);
  static const ::motor_control::BedStatus& secondarybedstatus(const SystemStatusInfoStatus* msg);
};

const ::motor_control::BedStatus&
SystemStatusInfoStatus::_Internal::firstbedstatus(const SystemStatusInfoStatus* msg) {
  return *msg->firstbedstatus_;
}
const ::motor_control::BedStatus&
SystemStatusInfoStatus::_Internal::secondarybedstatus(const SystemStatusInfoStatus* msg) {
  return *msg->secondarybedstatus_;
}
SystemStatusInfoStatus::SystemStatusInfoStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.SystemStatusInfoStatus)
}
SystemStatusInfoStatus::SystemStatusInfoStatus(const SystemStatusInfoStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  softwareversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    softwareversion_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_softwareversion().empty()) {
    softwareversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_softwareversion(), 
      GetArenaForAllocation());
  }
  timestamp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    timestamp_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_timestamp().empty()) {
    timestamp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_timestamp(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_firstbedstatus()) {
    firstbedstatus_ = new ::motor_control::BedStatus(*from.firstbedstatus_);
  } else {
    firstbedstatus_ = nullptr;
  }
  if (from._internal_has_secondarybedstatus()) {
    secondarybedstatus_ = new ::motor_control::BedStatus(*from.secondarybedstatus_);
  } else {
    secondarybedstatus_ = nullptr;
  }
  ownership_ = from.ownership_;
  // @@protoc_insertion_point(copy_constructor:motor_control.SystemStatusInfoStatus)
}

inline void SystemStatusInfoStatus::SharedCtor() {
softwareversion_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  softwareversion_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  timestamp_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&firstbedstatus_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ownership_) -
    reinterpret_cast<char*>(&firstbedstatus_)) + sizeof(ownership_));
}

SystemStatusInfoStatus::~SystemStatusInfoStatus() {
  // @@protoc_insertion_point(destructor:motor_control.SystemStatusInfoStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SystemStatusInfoStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  softwareversion_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  timestamp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete firstbedstatus_;
  if (this != internal_default_instance()) delete secondarybedstatus_;
}

void SystemStatusInfoStatus::ArenaDtor(void* object) {
  SystemStatusInfoStatus* _this = reinterpret_cast< SystemStatusInfoStatus* >(object);
  (void)_this;
}
void SystemStatusInfoStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SystemStatusInfoStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SystemStatusInfoStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.SystemStatusInfoStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  softwareversion_.ClearToEmpty();
  timestamp_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && firstbedstatus_ != nullptr) {
    delete firstbedstatus_;
  }
  firstbedstatus_ = nullptr;
  if (GetArenaForAllocation() == nullptr && secondarybedstatus_ != nullptr) {
    delete secondarybedstatus_;
  }
  secondarybedstatus_ = nullptr;
  ownership_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SystemStatusInfoStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string softwareVersion = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_softwareversion();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.SystemStatusInfoStatus.softwareVersion"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string timeStamp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_timestamp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.SystemStatusInfoStatus.timeStamp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.BedStatus firstBedStatus = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_firstbedstatus(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.BedStatus secondaryBedStatus = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_secondarybedstatus(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.HostType ownership = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_ownership(static_cast<::motor_control::HostType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SystemStatusInfoStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.SystemStatusInfoStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string softwareVersion = 1;
  if (!this->_internal_softwareversion().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_softwareversion().data(), static_cast<int>(this->_internal_softwareversion().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.SystemStatusInfoStatus.softwareVersion");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_softwareversion(), target);
  }

  // string timeStamp = 2;
  if (!this->_internal_timestamp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_timestamp().data(), static_cast<int>(this->_internal_timestamp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.SystemStatusInfoStatus.timeStamp");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_timestamp(), target);
  }

  // .motor_control.BedStatus firstBedStatus = 3;
  if (this->_internal_has_firstbedstatus()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::firstbedstatus(this), target, stream);
  }

  // .motor_control.BedStatus secondaryBedStatus = 4;
  if (this->_internal_has_secondarybedstatus()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::secondarybedstatus(this), target, stream);
  }

  // .motor_control.HostType ownership = 5;
  if (this->_internal_ownership() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_ownership(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.SystemStatusInfoStatus)
  return target;
}

size_t SystemStatusInfoStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.SystemStatusInfoStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string softwareVersion = 1;
  if (!this->_internal_softwareversion().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_softwareversion());
  }

  // string timeStamp = 2;
  if (!this->_internal_timestamp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_timestamp());
  }

  // .motor_control.BedStatus firstBedStatus = 3;
  if (this->_internal_has_firstbedstatus()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *firstbedstatus_);
  }

  // .motor_control.BedStatus secondaryBedStatus = 4;
  if (this->_internal_has_secondarybedstatus()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *secondarybedstatus_);
  }

  // .motor_control.HostType ownership = 5;
  if (this->_internal_ownership() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_ownership());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SystemStatusInfoStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SystemStatusInfoStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SystemStatusInfoStatus::GetClassData() const { return &_class_data_; }

void SystemStatusInfoStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SystemStatusInfoStatus *>(to)->MergeFrom(
      static_cast<const SystemStatusInfoStatus &>(from));
}


void SystemStatusInfoStatus::MergeFrom(const SystemStatusInfoStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.SystemStatusInfoStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_softwareversion().empty()) {
    _internal_set_softwareversion(from._internal_softwareversion());
  }
  if (!from._internal_timestamp().empty()) {
    _internal_set_timestamp(from._internal_timestamp());
  }
  if (from._internal_has_firstbedstatus()) {
    _internal_mutable_firstbedstatus()->::motor_control::BedStatus::MergeFrom(from._internal_firstbedstatus());
  }
  if (from._internal_has_secondarybedstatus()) {
    _internal_mutable_secondarybedstatus()->::motor_control::BedStatus::MergeFrom(from._internal_secondarybedstatus());
  }
  if (from._internal_ownership() != 0) {
    _internal_set_ownership(from._internal_ownership());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SystemStatusInfoStatus::CopyFrom(const SystemStatusInfoStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.SystemStatusInfoStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SystemStatusInfoStatus::IsInitialized() const {
  return true;
}

void SystemStatusInfoStatus::InternalSwap(SystemStatusInfoStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &softwareversion_, lhs_arena,
      &other->softwareversion_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &timestamp_, lhs_arena,
      &other->timestamp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SystemStatusInfoStatus, ownership_)
      + sizeof(SystemStatusInfoStatus::ownership_)
      - PROTOBUF_FIELD_OFFSET(SystemStatusInfoStatus, firstbedstatus_)>(
          reinterpret_cast<char*>(&firstbedstatus_),
          reinterpret_cast<char*>(&other->firstbedstatus_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SystemStatusInfoStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[6]);
}

// ===================================================================

class GetTriggerInfoDescription::_Internal {
 public:
};

GetTriggerInfoDescription::GetTriggerInfoDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.GetTriggerInfoDescription)
}
GetTriggerInfoDescription::GetTriggerInfoDescription(const GetTriggerInfoDescription& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:motor_control.GetTriggerInfoDescription)
}

inline void GetTriggerInfoDescription::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetTriggerInfoDescription::~GetTriggerInfoDescription() {
  // @@protoc_insertion_point(destructor:motor_control.GetTriggerInfoDescription)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetTriggerInfoDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetTriggerInfoDescription::ArenaDtor(void* object) {
  GetTriggerInfoDescription* _this = reinterpret_cast< GetTriggerInfoDescription* >(object);
  (void)_this;
}
void GetTriggerInfoDescription::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetTriggerInfoDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetTriggerInfoDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.GetTriggerInfoDescription)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetTriggerInfoDescription::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.GetTriggerInfoDescription.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetTriggerInfoDescription::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.GetTriggerInfoDescription)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.GetTriggerInfoDescription.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.GetTriggerInfoDescription)
  return target;
}

size_t GetTriggerInfoDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.GetTriggerInfoDescription)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetTriggerInfoDescription::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetTriggerInfoDescription::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetTriggerInfoDescription::GetClassData() const { return &_class_data_; }

void GetTriggerInfoDescription::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetTriggerInfoDescription *>(to)->MergeFrom(
      static_cast<const GetTriggerInfoDescription &>(from));
}


void GetTriggerInfoDescription::MergeFrom(const GetTriggerInfoDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.GetTriggerInfoDescription)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetTriggerInfoDescription::CopyFrom(const GetTriggerInfoDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.GetTriggerInfoDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTriggerInfoDescription::IsInitialized() const {
  return true;
}

void GetTriggerInfoDescription::InternalSwap(GetTriggerInfoDescription* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetTriggerInfoDescription::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[7]);
}

// ===================================================================

class TriggerInfoStatus::_Internal {
 public:
};

TriggerInfoStatus::TriggerInfoStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.TriggerInfoStatus)
}
TriggerInfoStatus::TriggerInfoStatus(const TriggerInfoStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_contextuid().empty()) {
    contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_contextuid(), 
      GetArenaForAllocation());
  }
  ::memcpy(&errorcode_, &from.errorcode_,
    static_cast<size_t>(reinterpret_cast<char*>(&exposuretime_) -
    reinterpret_cast<char*>(&errorcode_)) + sizeof(exposuretime_));
  // @@protoc_insertion_point(copy_constructor:motor_control.TriggerInfoStatus)
}

inline void TriggerInfoStatus::SharedCtor() {
contextuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&errorcode_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&exposuretime_) -
    reinterpret_cast<char*>(&errorcode_)) + sizeof(exposuretime_));
}

TriggerInfoStatus::~TriggerInfoStatus() {
  // @@protoc_insertion_point(destructor:motor_control.TriggerInfoStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TriggerInfoStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  contextuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TriggerInfoStatus::ArenaDtor(void* object) {
  TriggerInfoStatus* _this = reinterpret_cast< TriggerInfoStatus* >(object);
  (void)_this;
}
void TriggerInfoStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TriggerInfoStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TriggerInfoStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.TriggerInfoStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contextuid_.ClearToEmpty();
  ::memset(&errorcode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&exposuretime_) -
      reinterpret_cast<char*>(&errorcode_)) + sizeof(exposuretime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TriggerInfoStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string contextUID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_contextuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.TriggerInfoStatus.contextUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 errorCode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          errorcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float triggerPosition = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          triggerposition_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint64 triggerTimestamp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          triggertimestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 exposureTime = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          exposuretime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TriggerInfoStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.TriggerInfoStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_contextuid().data(), static_cast<int>(this->_internal_contextuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.TriggerInfoStatus.contextUID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_contextuid(), target);
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_errorcode(), target);
  }

  // float triggerPosition = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_triggerposition = this->_internal_triggerposition();
  uint32_t raw_triggerposition;
  memcpy(&raw_triggerposition, &tmp_triggerposition, sizeof(tmp_triggerposition));
  if (raw_triggerposition != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_triggerposition(), target);
  }

  // uint64 triggerTimestamp = 4;
  if (this->_internal_triggertimestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_triggertimestamp(), target);
  }

  // uint32 exposureTime = 5;
  if (this->_internal_exposuretime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_exposuretime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.TriggerInfoStatus)
  return target;
}

size_t TriggerInfoStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.TriggerInfoStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string contextUID = 1;
  if (!this->_internal_contextuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_contextuid());
  }

  // uint64 errorCode = 2;
  if (this->_internal_errorcode() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_errorcode());
  }

  // uint64 triggerTimestamp = 4;
  if (this->_internal_triggertimestamp() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_triggertimestamp());
  }

  // float triggerPosition = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_triggerposition = this->_internal_triggerposition();
  uint32_t raw_triggerposition;
  memcpy(&raw_triggerposition, &tmp_triggerposition, sizeof(tmp_triggerposition));
  if (raw_triggerposition != 0) {
    total_size += 1 + 4;
  }

  // uint32 exposureTime = 5;
  if (this->_internal_exposuretime() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_exposuretime());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TriggerInfoStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TriggerInfoStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TriggerInfoStatus::GetClassData() const { return &_class_data_; }

void TriggerInfoStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TriggerInfoStatus *>(to)->MergeFrom(
      static_cast<const TriggerInfoStatus &>(from));
}


void TriggerInfoStatus::MergeFrom(const TriggerInfoStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.TriggerInfoStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_contextuid().empty()) {
    _internal_set_contextuid(from._internal_contextuid());
  }
  if (from._internal_errorcode() != 0) {
    _internal_set_errorcode(from._internal_errorcode());
  }
  if (from._internal_triggertimestamp() != 0) {
    _internal_set_triggertimestamp(from._internal_triggertimestamp());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_triggerposition = from._internal_triggerposition();
  uint32_t raw_triggerposition;
  memcpy(&raw_triggerposition, &tmp_triggerposition, sizeof(tmp_triggerposition));
  if (raw_triggerposition != 0) {
    _internal_set_triggerposition(from._internal_triggerposition());
  }
  if (from._internal_exposuretime() != 0) {
    _internal_set_exposuretime(from._internal_exposuretime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TriggerInfoStatus::CopyFrom(const TriggerInfoStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.TriggerInfoStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TriggerInfoStatus::IsInitialized() const {
  return true;
}

void TriggerInfoStatus::InternalSwap(TriggerInfoStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &contextuid_, lhs_arena,
      &other->contextuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TriggerInfoStatus, exposuretime_)
      + sizeof(TriggerInfoStatus::exposuretime_)
      - PROTOBUF_FIELD_OFFSET(TriggerInfoStatus, errorcode_)>(
          reinterpret_cast<char*>(&errorcode_),
          reinterpret_cast<char*>(&other->errorcode_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TriggerInfoStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[8]);
}

// ===================================================================

class MotionInfo::_Internal {
 public:
};

MotionInfo::MotionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotionInfo)
}
MotionInfo::MotionInfo(const MotionInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&postion_, &from.postion_,
    static_cast<size_t>(reinterpret_cast<char*>(&velocity_) -
    reinterpret_cast<char*>(&postion_)) + sizeof(velocity_));
  // @@protoc_insertion_point(copy_constructor:motor_control.MotionInfo)
}

inline void MotionInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&postion_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&velocity_) -
    reinterpret_cast<char*>(&postion_)) + sizeof(velocity_));
}

MotionInfo::~MotionInfo() {
  // @@protoc_insertion_point(destructor:motor_control.MotionInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotionInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MotionInfo::ArenaDtor(void* object) {
  MotionInfo* _this = reinterpret_cast< MotionInfo* >(object);
  (void)_this;
}
void MotionInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotionInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotionInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotionInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&postion_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&velocity_) -
      reinterpret_cast<char*>(&postion_)) + sizeof(velocity_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotionInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float postion = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          postion_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float velocity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotionInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotionInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float postion = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_postion = this->_internal_postion();
  uint32_t raw_postion;
  memcpy(&raw_postion, &tmp_postion, sizeof(tmp_postion));
  if (raw_postion != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_postion(), target);
  }

  // float velocity = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_velocity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotionInfo)
  return target;
}

size_t MotionInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotionInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float postion = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_postion = this->_internal_postion();
  uint32_t raw_postion;
  memcpy(&raw_postion, &tmp_postion, sizeof(tmp_postion));
  if (raw_postion != 0) {
    total_size += 1 + 4;
  }

  // float velocity = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = this->_internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotionInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotionInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotionInfo::GetClassData() const { return &_class_data_; }

void MotionInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotionInfo *>(to)->MergeFrom(
      static_cast<const MotionInfo &>(from));
}


void MotionInfo::MergeFrom(const MotionInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_postion = from._internal_postion();
  uint32_t raw_postion;
  memcpy(&raw_postion, &tmp_postion, sizeof(tmp_postion));
  if (raw_postion != 0) {
    _internal_set_postion(from._internal_postion());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocity = from._internal_velocity();
  uint32_t raw_velocity;
  memcpy(&raw_velocity, &tmp_velocity, sizeof(tmp_velocity));
  if (raw_velocity != 0) {
    _internal_set_velocity(from._internal_velocity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotionInfo::CopyFrom(const MotionInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotionInfo::IsInitialized() const {
  return true;
}

void MotionInfo::InternalSwap(MotionInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MotionInfo, velocity_)
      + sizeof(MotionInfo::velocity_)
      - PROTOBUF_FIELD_OFFSET(MotionInfo, postion_)>(
          reinterpret_cast<char*>(&postion_),
          reinterpret_cast<char*>(&other->postion_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MotionInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[9]);
}

// ===================================================================

class MotionCapability::_Internal {
 public:
};

MotionCapability::MotionCapability(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotionCapability)
}
MotionCapability::MotionCapability(const MotionCapability& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&positionmin_, &from.positionmin_,
    static_cast<size_t>(reinterpret_cast<char*>(&decelerationmax_) -
    reinterpret_cast<char*>(&positionmin_)) + sizeof(decelerationmax_));
  // @@protoc_insertion_point(copy_constructor:motor_control.MotionCapability)
}

inline void MotionCapability::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&positionmin_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&decelerationmax_) -
    reinterpret_cast<char*>(&positionmin_)) + sizeof(decelerationmax_));
}

MotionCapability::~MotionCapability() {
  // @@protoc_insertion_point(destructor:motor_control.MotionCapability)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotionCapability::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MotionCapability::ArenaDtor(void* object) {
  MotionCapability* _this = reinterpret_cast< MotionCapability* >(object);
  (void)_this;
}
void MotionCapability::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotionCapability::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotionCapability::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotionCapability)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&positionmin_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&decelerationmax_) -
      reinterpret_cast<char*>(&positionmin_)) + sizeof(decelerationmax_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotionCapability::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float positionMin = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          positionmin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float positionMax = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          positionmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float velocityMin = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          velocitymin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float velocityMax = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          velocitymax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float accelerationMax = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          accelerationmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float decelerationMax = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          decelerationmax_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotionCapability::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotionCapability)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float positionMin = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmin = this->_internal_positionmin();
  uint32_t raw_positionmin;
  memcpy(&raw_positionmin, &tmp_positionmin, sizeof(tmp_positionmin));
  if (raw_positionmin != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_positionmin(), target);
  }

  // float positionMax = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmax = this->_internal_positionmax();
  uint32_t raw_positionmax;
  memcpy(&raw_positionmax, &tmp_positionmax, sizeof(tmp_positionmax));
  if (raw_positionmax != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_positionmax(), target);
  }

  // float velocityMin = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymin = this->_internal_velocitymin();
  uint32_t raw_velocitymin;
  memcpy(&raw_velocitymin, &tmp_velocitymin, sizeof(tmp_velocitymin));
  if (raw_velocitymin != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_velocitymin(), target);
  }

  // float velocityMax = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymax = this->_internal_velocitymax();
  uint32_t raw_velocitymax;
  memcpy(&raw_velocitymax, &tmp_velocitymax, sizeof(tmp_velocitymax));
  if (raw_velocitymax != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_velocitymax(), target);
  }

  // float accelerationMax = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_accelerationmax = this->_internal_accelerationmax();
  uint32_t raw_accelerationmax;
  memcpy(&raw_accelerationmax, &tmp_accelerationmax, sizeof(tmp_accelerationmax));
  if (raw_accelerationmax != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_accelerationmax(), target);
  }

  // float decelerationMax = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decelerationmax = this->_internal_decelerationmax();
  uint32_t raw_decelerationmax;
  memcpy(&raw_decelerationmax, &tmp_decelerationmax, sizeof(tmp_decelerationmax));
  if (raw_decelerationmax != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_decelerationmax(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotionCapability)
  return target;
}

size_t MotionCapability::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotionCapability)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float positionMin = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmin = this->_internal_positionmin();
  uint32_t raw_positionmin;
  memcpy(&raw_positionmin, &tmp_positionmin, sizeof(tmp_positionmin));
  if (raw_positionmin != 0) {
    total_size += 1 + 4;
  }

  // float positionMax = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmax = this->_internal_positionmax();
  uint32_t raw_positionmax;
  memcpy(&raw_positionmax, &tmp_positionmax, sizeof(tmp_positionmax));
  if (raw_positionmax != 0) {
    total_size += 1 + 4;
  }

  // float velocityMin = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymin = this->_internal_velocitymin();
  uint32_t raw_velocitymin;
  memcpy(&raw_velocitymin, &tmp_velocitymin, sizeof(tmp_velocitymin));
  if (raw_velocitymin != 0) {
    total_size += 1 + 4;
  }

  // float velocityMax = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymax = this->_internal_velocitymax();
  uint32_t raw_velocitymax;
  memcpy(&raw_velocitymax, &tmp_velocitymax, sizeof(tmp_velocitymax));
  if (raw_velocitymax != 0) {
    total_size += 1 + 4;
  }

  // float accelerationMax = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_accelerationmax = this->_internal_accelerationmax();
  uint32_t raw_accelerationmax;
  memcpy(&raw_accelerationmax, &tmp_accelerationmax, sizeof(tmp_accelerationmax));
  if (raw_accelerationmax != 0) {
    total_size += 1 + 4;
  }

  // float decelerationMax = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decelerationmax = this->_internal_decelerationmax();
  uint32_t raw_decelerationmax;
  memcpy(&raw_decelerationmax, &tmp_decelerationmax, sizeof(tmp_decelerationmax));
  if (raw_decelerationmax != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotionCapability::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotionCapability::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotionCapability::GetClassData() const { return &_class_data_; }

void MotionCapability::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotionCapability *>(to)->MergeFrom(
      static_cast<const MotionCapability &>(from));
}


void MotionCapability::MergeFrom(const MotionCapability& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotionCapability)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmin = from._internal_positionmin();
  uint32_t raw_positionmin;
  memcpy(&raw_positionmin, &tmp_positionmin, sizeof(tmp_positionmin));
  if (raw_positionmin != 0) {
    _internal_set_positionmin(from._internal_positionmin());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_positionmax = from._internal_positionmax();
  uint32_t raw_positionmax;
  memcpy(&raw_positionmax, &tmp_positionmax, sizeof(tmp_positionmax));
  if (raw_positionmax != 0) {
    _internal_set_positionmax(from._internal_positionmax());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymin = from._internal_velocitymin();
  uint32_t raw_velocitymin;
  memcpy(&raw_velocitymin, &tmp_velocitymin, sizeof(tmp_velocitymin));
  if (raw_velocitymin != 0) {
    _internal_set_velocitymin(from._internal_velocitymin());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_velocitymax = from._internal_velocitymax();
  uint32_t raw_velocitymax;
  memcpy(&raw_velocitymax, &tmp_velocitymax, sizeof(tmp_velocitymax));
  if (raw_velocitymax != 0) {
    _internal_set_velocitymax(from._internal_velocitymax());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_accelerationmax = from._internal_accelerationmax();
  uint32_t raw_accelerationmax;
  memcpy(&raw_accelerationmax, &tmp_accelerationmax, sizeof(tmp_accelerationmax));
  if (raw_accelerationmax != 0) {
    _internal_set_accelerationmax(from._internal_accelerationmax());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decelerationmax = from._internal_decelerationmax();
  uint32_t raw_decelerationmax;
  memcpy(&raw_decelerationmax, &tmp_decelerationmax, sizeof(tmp_decelerationmax));
  if (raw_decelerationmax != 0) {
    _internal_set_decelerationmax(from._internal_decelerationmax());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotionCapability::CopyFrom(const MotionCapability& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotionCapability)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotionCapability::IsInitialized() const {
  return true;
}

void MotionCapability::InternalSwap(MotionCapability* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MotionCapability, decelerationmax_)
      + sizeof(MotionCapability::decelerationmax_)
      - PROTOBUF_FIELD_OFFSET(MotionCapability, positionmin_)>(
          reinterpret_cast<char*>(&positionmin_),
          reinterpret_cast<char*>(&other->positionmin_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MotionCapability::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[10]);
}

// ===================================================================

class BedStatus::_Internal {
 public:
  static const ::motor_control::MotionInfo& motioninfo(const BedStatus* msg);
  static const ::motor_control::MotionCapability& motioncapability(const BedStatus* msg);
};

const ::motor_control::MotionInfo&
BedStatus::_Internal::motioninfo(const BedStatus* msg) {
  return *msg->motioninfo_;
}
const ::motor_control::MotionCapability&
BedStatus::_Internal::motioncapability(const BedStatus* msg) {
  return *msg->motioncapability_;
}
BedStatus::BedStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.BedStatus)
}
BedStatus::BedStatus(const BedStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_motioninfo()) {
    motioninfo_ = new ::motor_control::MotionInfo(*from.motioninfo_);
  } else {
    motioninfo_ = nullptr;
  }
  if (from._internal_has_motioncapability()) {
    motioncapability_ = new ::motor_control::MotionCapability(*from.motioncapability_);
  } else {
    motioncapability_ = nullptr;
  }
  motionstatus_ = from.motionstatus_;
  // @@protoc_insertion_point(copy_constructor:motor_control.BedStatus)
}

inline void BedStatus::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&motioninfo_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&motionstatus_) -
    reinterpret_cast<char*>(&motioninfo_)) + sizeof(motionstatus_));
}

BedStatus::~BedStatus() {
  // @@protoc_insertion_point(destructor:motor_control.BedStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BedStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete motioninfo_;
  if (this != internal_default_instance()) delete motioncapability_;
}

void BedStatus::ArenaDtor(void* object) {
  BedStatus* _this = reinterpret_cast< BedStatus* >(object);
  (void)_this;
}
void BedStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BedStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BedStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.BedStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && motioninfo_ != nullptr) {
    delete motioninfo_;
  }
  motioninfo_ = nullptr;
  if (GetArenaForAllocation() == nullptr && motioncapability_ != nullptr) {
    delete motioncapability_;
  }
  motioncapability_ = nullptr;
  motionstatus_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BedStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .motor_control.MotionInfo motionInfo = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_motioninfo(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.MotionStatus motionStatus = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_motionstatus(static_cast<::motor_control::MotionStatus>(val));
        } else
          goto handle_unusual;
        continue;
      // .motor_control.MotionCapability motionCapability = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_motioncapability(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BedStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.BedStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .motor_control.MotionInfo motionInfo = 1;
  if (this->_internal_has_motioninfo()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::motioninfo(this), target, stream);
  }

  // .motor_control.MotionStatus motionStatus = 2;
  if (this->_internal_motionstatus() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_motionstatus(), target);
  }

  // .motor_control.MotionCapability motionCapability = 3;
  if (this->_internal_has_motioncapability()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::motioncapability(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.BedStatus)
  return target;
}

size_t BedStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.BedStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .motor_control.MotionInfo motionInfo = 1;
  if (this->_internal_has_motioninfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *motioninfo_);
  }

  // .motor_control.MotionCapability motionCapability = 3;
  if (this->_internal_has_motioncapability()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *motioncapability_);
  }

  // .motor_control.MotionStatus motionStatus = 2;
  if (this->_internal_motionstatus() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_motionstatus());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BedStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BedStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BedStatus::GetClassData() const { return &_class_data_; }

void BedStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BedStatus *>(to)->MergeFrom(
      static_cast<const BedStatus &>(from));
}


void BedStatus::MergeFrom(const BedStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.BedStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_motioninfo()) {
    _internal_mutable_motioninfo()->::motor_control::MotionInfo::MergeFrom(from._internal_motioninfo());
  }
  if (from._internal_has_motioncapability()) {
    _internal_mutable_motioncapability()->::motor_control::MotionCapability::MergeFrom(from._internal_motioncapability());
  }
  if (from._internal_motionstatus() != 0) {
    _internal_set_motionstatus(from._internal_motionstatus());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BedStatus::CopyFrom(const BedStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.BedStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BedStatus::IsInitialized() const {
  return true;
}

void BedStatus::InternalSwap(BedStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BedStatus, motionstatus_)
      + sizeof(BedStatus::motionstatus_)
      - PROTOBUF_FIELD_OFFSET(BedStatus, motioninfo_)>(
          reinterpret_cast<char*>(&motioninfo_),
          reinterpret_cast<char*>(&other->motioninfo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BedStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[11]);
}

// ===================================================================

class PostIdInfo::_Internal {
 public:
};

PostIdInfo::PostIdInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.PostIdInfo)
}
PostIdInfo::PostIdInfo(const PostIdInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&vid_, &from.vid_,
    static_cast<size_t>(reinterpret_cast<char*>(&rid_) -
    reinterpret_cast<char*>(&vid_)) + sizeof(rid_));
  // @@protoc_insertion_point(copy_constructor:motor_control.PostIdInfo)
}

inline void PostIdInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&vid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&rid_) -
    reinterpret_cast<char*>(&vid_)) + sizeof(rid_));
}

PostIdInfo::~PostIdInfo() {
  // @@protoc_insertion_point(destructor:motor_control.PostIdInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PostIdInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PostIdInfo::ArenaDtor(void* object) {
  PostIdInfo* _this = reinterpret_cast< PostIdInfo* >(object);
  (void)_this;
}
void PostIdInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PostIdInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PostIdInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.PostIdInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&vid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&rid_) -
      reinterpret_cast<char*>(&vid_)) + sizeof(rid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PostIdInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 VID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          vid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 DID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          did_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 HWID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          hwid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 RID = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          rid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PostIdInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.PostIdInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 VID = 1;
  if (this->_internal_vid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_vid(), target);
  }

  // uint32 DID = 2;
  if (this->_internal_did() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_did(), target);
  }

  // uint32 HWID = 3;
  if (this->_internal_hwid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_hwid(), target);
  }

  // uint32 RID = 4;
  if (this->_internal_rid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_rid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.PostIdInfo)
  return target;
}

size_t PostIdInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.PostIdInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 VID = 1;
  if (this->_internal_vid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_vid());
  }

  // uint32 DID = 2;
  if (this->_internal_did() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_did());
  }

  // uint32 HWID = 3;
  if (this->_internal_hwid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_hwid());
  }

  // uint32 RID = 4;
  if (this->_internal_rid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_rid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PostIdInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PostIdInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PostIdInfo::GetClassData() const { return &_class_data_; }

void PostIdInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PostIdInfo *>(to)->MergeFrom(
      static_cast<const PostIdInfo &>(from));
}


void PostIdInfo::MergeFrom(const PostIdInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.PostIdInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_vid() != 0) {
    _internal_set_vid(from._internal_vid());
  }
  if (from._internal_did() != 0) {
    _internal_set_did(from._internal_did());
  }
  if (from._internal_hwid() != 0) {
    _internal_set_hwid(from._internal_hwid());
  }
  if (from._internal_rid() != 0) {
    _internal_set_rid(from._internal_rid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PostIdInfo::CopyFrom(const PostIdInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.PostIdInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PostIdInfo::IsInitialized() const {
  return true;
}

void PostIdInfo::InternalSwap(PostIdInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PostIdInfo, rid_)
      + sizeof(PostIdInfo::rid_)
      - PROTOBUF_FIELD_OFFSET(PostIdInfo, vid_)>(
          reinterpret_cast<char*>(&vid_),
          reinterpret_cast<char*>(&other->vid_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PostIdInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[12]);
}

// ===================================================================

class MotorControlRequest::_Internal {
 public:
};

MotorControlRequest::MotorControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotorControlRequest)
}
MotorControlRequest::MotorControlRequest(const MotorControlRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&enable_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(enable_));
  // @@protoc_insertion_point(copy_constructor:motor_control.MotorControlRequest)
}

inline void MotorControlRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enable_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(enable_));
}

MotorControlRequest::~MotorControlRequest() {
  // @@protoc_insertion_point(destructor:motor_control.MotorControlRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotorControlRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MotorControlRequest::ArenaDtor(void* object) {
  MotorControlRequest* _this = reinterpret_cast< MotorControlRequest* >(object);
  (void)_this;
}
void MotorControlRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotorControlRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotorControlRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotorControlRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enable_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(enable_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotorControlRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool enable = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          enable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotorControlRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotorControlRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // bool enable = 2;
  if (this->_internal_enable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_enable(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotorControlRequest)
  return target;
}

size_t MotorControlRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotorControlRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // bool enable = 2;
  if (this->_internal_enable() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotorControlRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotorControlRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotorControlRequest::GetClassData() const { return &_class_data_; }

void MotorControlRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotorControlRequest *>(to)->MergeFrom(
      static_cast<const MotorControlRequest &>(from));
}


void MotorControlRequest::MergeFrom(const MotorControlRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotorControlRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_enable() != 0) {
    _internal_set_enable(from._internal_enable());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotorControlRequest::CopyFrom(const MotorControlRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotorControlRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotorControlRequest::IsInitialized() const {
  return true;
}

void MotorControlRequest::InternalSwap(MotorControlRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MotorControlRequest, enable_)
      + sizeof(MotorControlRequest::enable_)
      - PROTOBUF_FIELD_OFFSET(MotorControlRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MotorControlRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[13]);
}

// ===================================================================

class MotorControlResponse::_Internal {
 public:
};

MotorControlResponse::MotorControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotorControlResponse)
}
MotorControlResponse::MotorControlResponse(const MotorControlResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&success_)) + sizeof(state_));
  // @@protoc_insertion_point(copy_constructor:motor_control.MotorControlResponse)
}

inline void MotorControlResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&success_)) + sizeof(state_));
}

MotorControlResponse::~MotorControlResponse() {
  // @@protoc_insertion_point(destructor:motor_control.MotorControlResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotorControlResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MotorControlResponse::ArenaDtor(void* object) {
  MotorControlResponse* _this = reinterpret_cast< MotorControlResponse* >(object);
  (void)_this;
}
void MotorControlResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotorControlResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotorControlResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotorControlResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&state_) -
      reinterpret_cast<char*>(&success_)) + sizeof(state_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotorControlResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.MotorControlResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.DriveState state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::motor_control::DriveState>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotorControlResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotorControlResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.MotorControlResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotorControlResponse)
  return target;
}

size_t MotorControlResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotorControlResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotorControlResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotorControlResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotorControlResponse::GetClassData() const { return &_class_data_; }

void MotorControlResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotorControlResponse *>(to)->MergeFrom(
      static_cast<const MotorControlResponse &>(from));
}


void MotorControlResponse::MergeFrom(const MotorControlResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotorControlResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotorControlResponse::CopyFrom(const MotorControlResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotorControlResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotorControlResponse::IsInitialized() const {
  return true;
}

void MotorControlResponse::InternalSwap(MotorControlResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MotorControlResponse, state_)
      + sizeof(MotorControlResponse::state_)
      - PROTOBUF_FIELD_OFFSET(MotorControlResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MotorControlResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[14]);
}

// ===================================================================

class MotorStatusRequest::_Internal {
 public:
};

MotorStatusRequest::MotorStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotorStatusRequest)
}
MotorStatusRequest::MotorStatusRequest(const MotorStatusRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:motor_control.MotorStatusRequest)
}

inline void MotorStatusRequest::SharedCtor() {
node_id_ = 0u;
}

MotorStatusRequest::~MotorStatusRequest() {
  // @@protoc_insertion_point(destructor:motor_control.MotorStatusRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotorStatusRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MotorStatusRequest::ArenaDtor(void* object) {
  MotorStatusRequest* _this = reinterpret_cast< MotorStatusRequest* >(object);
  (void)_this;
}
void MotorStatusRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotorStatusRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotorStatusRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotorStatusRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotorStatusRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotorStatusRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotorStatusRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotorStatusRequest)
  return target;
}

size_t MotorStatusRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotorStatusRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotorStatusRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotorStatusRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotorStatusRequest::GetClassData() const { return &_class_data_; }

void MotorStatusRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotorStatusRequest *>(to)->MergeFrom(
      static_cast<const MotorStatusRequest &>(from));
}


void MotorStatusRequest::MergeFrom(const MotorStatusRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotorStatusRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotorStatusRequest::CopyFrom(const MotorStatusRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotorStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotorStatusRequest::IsInitialized() const {
  return true;
}

void MotorStatusRequest::InternalSwap(MotorStatusRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(node_id_, other->node_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MotorStatusRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[15]);
}

// ===================================================================

class MotorStatusResponse::_Internal {
 public:
};

MotorStatusResponse::MotorStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.MotorStatusResponse)
}
MotorStatusResponse::MotorStatusResponse(const MotorStatusResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&state_, &from.state_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_word_) -
    reinterpret_cast<char*>(&state_)) + sizeof(status_word_));
  // @@protoc_insertion_point(copy_constructor:motor_control.MotorStatusResponse)
}

inline void MotorStatusResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&status_word_) -
    reinterpret_cast<char*>(&state_)) + sizeof(status_word_));
}

MotorStatusResponse::~MotorStatusResponse() {
  // @@protoc_insertion_point(destructor:motor_control.MotorStatusResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MotorStatusResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MotorStatusResponse::ArenaDtor(void* object) {
  MotorStatusResponse* _this = reinterpret_cast< MotorStatusResponse* >(object);
  (void)_this;
}
void MotorStatusResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MotorStatusResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MotorStatusResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.MotorStatusResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_word_) -
      reinterpret_cast<char*>(&state_)) + sizeof(status_word_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MotorStatusResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.MotorStatusResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.DriveState state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::motor_control::DriveState>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 actual_position = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          actual_position_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 actual_velocity = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          actual_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 actual_torque = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          actual_torque_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.OperationMode current_mode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_current_mode(static_cast<::motor_control::OperationMode>(val));
        } else
          goto handle_unusual;
        continue;
      // uint32 status_word = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          status_word_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_target_reached = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          is_target_reached_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool has_fault = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          has_fault_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MotorStatusResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.MotorStatusResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.MotorStatusResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_state(), target);
  }

  // int32 actual_position = 4;
  if (this->_internal_actual_position() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_actual_position(), target);
  }

  // int32 actual_velocity = 5;
  if (this->_internal_actual_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_actual_velocity(), target);
  }

  // int32 actual_torque = 6;
  if (this->_internal_actual_torque() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_actual_torque(), target);
  }

  // .motor_control.OperationMode current_mode = 7;
  if (this->_internal_current_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_current_mode(), target);
  }

  // uint32 status_word = 8;
  if (this->_internal_status_word() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_status_word(), target);
  }

  // bool is_target_reached = 9;
  if (this->_internal_is_target_reached() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_is_target_reached(), target);
  }

  // bool has_fault = 10;
  if (this->_internal_has_fault() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(10, this->_internal_has_fault(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.MotorStatusResponse)
  return target;
}

size_t MotorStatusResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.MotorStatusResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  // int32 actual_position = 4;
  if (this->_internal_actual_position() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_position());
  }

  // int32 actual_velocity = 5;
  if (this->_internal_actual_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_velocity());
  }

  // int32 actual_torque = 6;
  if (this->_internal_actual_torque() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_torque());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // bool is_target_reached = 9;
  if (this->_internal_is_target_reached() != 0) {
    total_size += 1 + 1;
  }

  // bool has_fault = 10;
  if (this->_internal_has_fault() != 0) {
    total_size += 1 + 1;
  }

  // .motor_control.OperationMode current_mode = 7;
  if (this->_internal_current_mode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_current_mode());
  }

  // uint32 status_word = 8;
  if (this->_internal_status_word() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_status_word());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MotorStatusResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MotorStatusResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MotorStatusResponse::GetClassData() const { return &_class_data_; }

void MotorStatusResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MotorStatusResponse *>(to)->MergeFrom(
      static_cast<const MotorStatusResponse &>(from));
}


void MotorStatusResponse::MergeFrom(const MotorStatusResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.MotorStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  if (from._internal_actual_position() != 0) {
    _internal_set_actual_position(from._internal_actual_position());
  }
  if (from._internal_actual_velocity() != 0) {
    _internal_set_actual_velocity(from._internal_actual_velocity());
  }
  if (from._internal_actual_torque() != 0) {
    _internal_set_actual_torque(from._internal_actual_torque());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_is_target_reached() != 0) {
    _internal_set_is_target_reached(from._internal_is_target_reached());
  }
  if (from._internal_has_fault() != 0) {
    _internal_set_has_fault(from._internal_has_fault());
  }
  if (from._internal_current_mode() != 0) {
    _internal_set_current_mode(from._internal_current_mode());
  }
  if (from._internal_status_word() != 0) {
    _internal_set_status_word(from._internal_status_word());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MotorStatusResponse::CopyFrom(const MotorStatusResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.MotorStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MotorStatusResponse::IsInitialized() const {
  return true;
}

void MotorStatusResponse::InternalSwap(MotorStatusResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MotorStatusResponse, status_word_)
      + sizeof(MotorStatusResponse::status_word_)
      - PROTOBUF_FIELD_OFFSET(MotorStatusResponse, state_)>(
          reinterpret_cast<char*>(&state_),
          reinterpret_cast<char*>(&other->state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MotorStatusResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[16]);
}

// ===================================================================

class OperationModeRequest::_Internal {
 public:
};

OperationModeRequest::OperationModeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.OperationModeRequest)
}
OperationModeRequest::OperationModeRequest(const OperationModeRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&mode_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(mode_));
  // @@protoc_insertion_point(copy_constructor:motor_control.OperationModeRequest)
}

inline void OperationModeRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&mode_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(mode_));
}

OperationModeRequest::~OperationModeRequest() {
  // @@protoc_insertion_point(destructor:motor_control.OperationModeRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OperationModeRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void OperationModeRequest::ArenaDtor(void* object) {
  OperationModeRequest* _this = reinterpret_cast< OperationModeRequest* >(object);
  (void)_this;
}
void OperationModeRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OperationModeRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OperationModeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.OperationModeRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&mode_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(mode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OperationModeRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.OperationMode mode = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_mode(static_cast<::motor_control::OperationMode>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* OperationModeRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.OperationModeRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // .motor_control.OperationMode mode = 2;
  if (this->_internal_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_mode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.OperationModeRequest)
  return target;
}

size_t OperationModeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.OperationModeRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // .motor_control.OperationMode mode = 2;
  if (this->_internal_mode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_mode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OperationModeRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OperationModeRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OperationModeRequest::GetClassData() const { return &_class_data_; }

void OperationModeRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<OperationModeRequest *>(to)->MergeFrom(
      static_cast<const OperationModeRequest &>(from));
}


void OperationModeRequest::MergeFrom(const OperationModeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.OperationModeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_mode() != 0) {
    _internal_set_mode(from._internal_mode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OperationModeRequest::CopyFrom(const OperationModeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.OperationModeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OperationModeRequest::IsInitialized() const {
  return true;
}

void OperationModeRequest::InternalSwap(OperationModeRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OperationModeRequest, mode_)
      + sizeof(OperationModeRequest::mode_)
      - PROTOBUF_FIELD_OFFSET(OperationModeRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OperationModeRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[17]);
}

// ===================================================================

class OperationModeResponse::_Internal {
 public:
};

OperationModeResponse::OperationModeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.OperationModeResponse)
}
OperationModeResponse::OperationModeResponse(const OperationModeResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&current_mode_) -
    reinterpret_cast<char*>(&success_)) + sizeof(current_mode_));
  // @@protoc_insertion_point(copy_constructor:motor_control.OperationModeResponse)
}

inline void OperationModeResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&current_mode_) -
    reinterpret_cast<char*>(&success_)) + sizeof(current_mode_));
}

OperationModeResponse::~OperationModeResponse() {
  // @@protoc_insertion_point(destructor:motor_control.OperationModeResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OperationModeResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void OperationModeResponse::ArenaDtor(void* object) {
  OperationModeResponse* _this = reinterpret_cast< OperationModeResponse* >(object);
  (void)_this;
}
void OperationModeResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OperationModeResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OperationModeResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.OperationModeResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&current_mode_) -
      reinterpret_cast<char*>(&success_)) + sizeof(current_mode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OperationModeResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.OperationModeResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.OperationMode current_mode = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_current_mode(static_cast<::motor_control::OperationMode>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* OperationModeResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.OperationModeResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.OperationModeResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // .motor_control.OperationMode current_mode = 3;
  if (this->_internal_current_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_current_mode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.OperationModeResponse)
  return target;
}

size_t OperationModeResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.OperationModeResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // .motor_control.OperationMode current_mode = 3;
  if (this->_internal_current_mode() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_current_mode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OperationModeResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OperationModeResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OperationModeResponse::GetClassData() const { return &_class_data_; }

void OperationModeResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<OperationModeResponse *>(to)->MergeFrom(
      static_cast<const OperationModeResponse &>(from));
}


void OperationModeResponse::MergeFrom(const OperationModeResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.OperationModeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_current_mode() != 0) {
    _internal_set_current_mode(from._internal_current_mode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OperationModeResponse::CopyFrom(const OperationModeResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.OperationModeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OperationModeResponse::IsInitialized() const {
  return true;
}

void OperationModeResponse::InternalSwap(OperationModeResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OperationModeResponse, current_mode_)
      + sizeof(OperationModeResponse::current_mode_)
      - PROTOBUF_FIELD_OFFSET(OperationModeResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OperationModeResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[18]);
}

// ===================================================================

class PositionControlRequest::_Internal {
 public:
};

PositionControlRequest::PositionControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.PositionControlRequest)
}
PositionControlRequest::PositionControlRequest(const PositionControlRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&immediate_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(immediate_));
  // @@protoc_insertion_point(copy_constructor:motor_control.PositionControlRequest)
}

inline void PositionControlRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&immediate_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(immediate_));
}

PositionControlRequest::~PositionControlRequest() {
  // @@protoc_insertion_point(destructor:motor_control.PositionControlRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PositionControlRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PositionControlRequest::ArenaDtor(void* object) {
  PositionControlRequest* _this = reinterpret_cast< PositionControlRequest* >(object);
  (void)_this;
}
void PositionControlRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PositionControlRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PositionControlRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.PositionControlRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&immediate_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(immediate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PositionControlRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 target_position = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          target_position_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_velocity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          profile_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_acceleration = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          profile_acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_deceleration = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          profile_deceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool absolute = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          absolute_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool immediate = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          immediate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PositionControlRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.PositionControlRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // int32 target_position = 2;
  if (this->_internal_target_position() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_target_position(), target);
  }

  // int32 profile_velocity = 3;
  if (this->_internal_profile_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_profile_velocity(), target);
  }

  // int32 profile_acceleration = 4;
  if (this->_internal_profile_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_profile_acceleration(), target);
  }

  // int32 profile_deceleration = 5;
  if (this->_internal_profile_deceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_profile_deceleration(), target);
  }

  // bool absolute = 6;
  if (this->_internal_absolute() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_absolute(), target);
  }

  // bool immediate = 7;
  if (this->_internal_immediate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_immediate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.PositionControlRequest)
  return target;
}

size_t PositionControlRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.PositionControlRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // int32 target_position = 2;
  if (this->_internal_target_position() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_target_position());
  }

  // int32 profile_velocity = 3;
  if (this->_internal_profile_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_velocity());
  }

  // int32 profile_acceleration = 4;
  if (this->_internal_profile_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_acceleration());
  }

  // int32 profile_deceleration = 5;
  if (this->_internal_profile_deceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_deceleration());
  }

  // bool absolute = 6;
  if (this->_internal_absolute() != 0) {
    total_size += 1 + 1;
  }

  // bool immediate = 7;
  if (this->_internal_immediate() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PositionControlRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PositionControlRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PositionControlRequest::GetClassData() const { return &_class_data_; }

void PositionControlRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PositionControlRequest *>(to)->MergeFrom(
      static_cast<const PositionControlRequest &>(from));
}


void PositionControlRequest::MergeFrom(const PositionControlRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.PositionControlRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_target_position() != 0) {
    _internal_set_target_position(from._internal_target_position());
  }
  if (from._internal_profile_velocity() != 0) {
    _internal_set_profile_velocity(from._internal_profile_velocity());
  }
  if (from._internal_profile_acceleration() != 0) {
    _internal_set_profile_acceleration(from._internal_profile_acceleration());
  }
  if (from._internal_profile_deceleration() != 0) {
    _internal_set_profile_deceleration(from._internal_profile_deceleration());
  }
  if (from._internal_absolute() != 0) {
    _internal_set_absolute(from._internal_absolute());
  }
  if (from._internal_immediate() != 0) {
    _internal_set_immediate(from._internal_immediate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PositionControlRequest::CopyFrom(const PositionControlRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.PositionControlRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PositionControlRequest::IsInitialized() const {
  return true;
}

void PositionControlRequest::InternalSwap(PositionControlRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PositionControlRequest, immediate_)
      + sizeof(PositionControlRequest::immediate_)
      - PROTOBUF_FIELD_OFFSET(PositionControlRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PositionControlRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[19]);
}

// ===================================================================

class PositionControlResponse::_Internal {
 public:
};

PositionControlResponse::PositionControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.PositionControlResponse)
}
PositionControlResponse::PositionControlResponse(const PositionControlResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&actual_position_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_position_));
  // @@protoc_insertion_point(copy_constructor:motor_control.PositionControlResponse)
}

inline void PositionControlResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&actual_position_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_position_));
}

PositionControlResponse::~PositionControlResponse() {
  // @@protoc_insertion_point(destructor:motor_control.PositionControlResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PositionControlResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PositionControlResponse::ArenaDtor(void* object) {
  PositionControlResponse* _this = reinterpret_cast< PositionControlResponse* >(object);
  (void)_this;
}
void PositionControlResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PositionControlResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PositionControlResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.PositionControlResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&actual_position_) -
      reinterpret_cast<char*>(&success_)) + sizeof(actual_position_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PositionControlResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.PositionControlResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 actual_position = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          actual_position_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PositionControlResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.PositionControlResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.PositionControlResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // int32 actual_position = 3;
  if (this->_internal_actual_position() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_actual_position(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.PositionControlResponse)
  return target;
}

size_t PositionControlResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.PositionControlResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // int32 actual_position = 3;
  if (this->_internal_actual_position() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_position());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PositionControlResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PositionControlResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PositionControlResponse::GetClassData() const { return &_class_data_; }

void PositionControlResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PositionControlResponse *>(to)->MergeFrom(
      static_cast<const PositionControlResponse &>(from));
}


void PositionControlResponse::MergeFrom(const PositionControlResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.PositionControlResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_actual_position() != 0) {
    _internal_set_actual_position(from._internal_actual_position());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PositionControlResponse::CopyFrom(const PositionControlResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.PositionControlResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PositionControlResponse::IsInitialized() const {
  return true;
}

void PositionControlResponse::InternalSwap(PositionControlResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PositionControlResponse, actual_position_)
      + sizeof(PositionControlResponse::actual_position_)
      - PROTOBUF_FIELD_OFFSET(PositionControlResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PositionControlResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[20]);
}

// ===================================================================

class VelocityControlRequest::_Internal {
 public:
};

VelocityControlRequest::VelocityControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.VelocityControlRequest)
}
VelocityControlRequest::VelocityControlRequest(const VelocityControlRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&profile_deceleration_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(profile_deceleration_));
  // @@protoc_insertion_point(copy_constructor:motor_control.VelocityControlRequest)
}

inline void VelocityControlRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&profile_deceleration_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(profile_deceleration_));
}

VelocityControlRequest::~VelocityControlRequest() {
  // @@protoc_insertion_point(destructor:motor_control.VelocityControlRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VelocityControlRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VelocityControlRequest::ArenaDtor(void* object) {
  VelocityControlRequest* _this = reinterpret_cast< VelocityControlRequest* >(object);
  (void)_this;
}
void VelocityControlRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VelocityControlRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VelocityControlRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.VelocityControlRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&profile_deceleration_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(profile_deceleration_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VelocityControlRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 target_velocity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          target_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_acceleration = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          profile_acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_deceleration = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          profile_deceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VelocityControlRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.VelocityControlRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // int32 target_velocity = 2;
  if (this->_internal_target_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_target_velocity(), target);
  }

  // int32 profile_acceleration = 3;
  if (this->_internal_profile_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_profile_acceleration(), target);
  }

  // int32 profile_deceleration = 4;
  if (this->_internal_profile_deceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_profile_deceleration(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.VelocityControlRequest)
  return target;
}

size_t VelocityControlRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.VelocityControlRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // int32 target_velocity = 2;
  if (this->_internal_target_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_target_velocity());
  }

  // int32 profile_acceleration = 3;
  if (this->_internal_profile_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_acceleration());
  }

  // int32 profile_deceleration = 4;
  if (this->_internal_profile_deceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_deceleration());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VelocityControlRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VelocityControlRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VelocityControlRequest::GetClassData() const { return &_class_data_; }

void VelocityControlRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VelocityControlRequest *>(to)->MergeFrom(
      static_cast<const VelocityControlRequest &>(from));
}


void VelocityControlRequest::MergeFrom(const VelocityControlRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.VelocityControlRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_target_velocity() != 0) {
    _internal_set_target_velocity(from._internal_target_velocity());
  }
  if (from._internal_profile_acceleration() != 0) {
    _internal_set_profile_acceleration(from._internal_profile_acceleration());
  }
  if (from._internal_profile_deceleration() != 0) {
    _internal_set_profile_deceleration(from._internal_profile_deceleration());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VelocityControlRequest::CopyFrom(const VelocityControlRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.VelocityControlRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VelocityControlRequest::IsInitialized() const {
  return true;
}

void VelocityControlRequest::InternalSwap(VelocityControlRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VelocityControlRequest, profile_deceleration_)
      + sizeof(VelocityControlRequest::profile_deceleration_)
      - PROTOBUF_FIELD_OFFSET(VelocityControlRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VelocityControlRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[21]);
}

// ===================================================================

class VelocityControlResponse::_Internal {
 public:
};

VelocityControlResponse::VelocityControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.VelocityControlResponse)
}
VelocityControlResponse::VelocityControlResponse(const VelocityControlResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&actual_velocity_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_velocity_));
  // @@protoc_insertion_point(copy_constructor:motor_control.VelocityControlResponse)
}

inline void VelocityControlResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&actual_velocity_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_velocity_));
}

VelocityControlResponse::~VelocityControlResponse() {
  // @@protoc_insertion_point(destructor:motor_control.VelocityControlResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VelocityControlResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void VelocityControlResponse::ArenaDtor(void* object) {
  VelocityControlResponse* _this = reinterpret_cast< VelocityControlResponse* >(object);
  (void)_this;
}
void VelocityControlResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VelocityControlResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VelocityControlResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.VelocityControlResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&actual_velocity_) -
      reinterpret_cast<char*>(&success_)) + sizeof(actual_velocity_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VelocityControlResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.VelocityControlResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 actual_velocity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          actual_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VelocityControlResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.VelocityControlResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.VelocityControlResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // int32 actual_velocity = 3;
  if (this->_internal_actual_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_actual_velocity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.VelocityControlResponse)
  return target;
}

size_t VelocityControlResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.VelocityControlResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // int32 actual_velocity = 3;
  if (this->_internal_actual_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_velocity());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VelocityControlResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VelocityControlResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VelocityControlResponse::GetClassData() const { return &_class_data_; }

void VelocityControlResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VelocityControlResponse *>(to)->MergeFrom(
      static_cast<const VelocityControlResponse &>(from));
}


void VelocityControlResponse::MergeFrom(const VelocityControlResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.VelocityControlResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_actual_velocity() != 0) {
    _internal_set_actual_velocity(from._internal_actual_velocity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VelocityControlResponse::CopyFrom(const VelocityControlResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.VelocityControlResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VelocityControlResponse::IsInitialized() const {
  return true;
}

void VelocityControlResponse::InternalSwap(VelocityControlResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VelocityControlResponse, actual_velocity_)
      + sizeof(VelocityControlResponse::actual_velocity_)
      - PROTOBUF_FIELD_OFFSET(VelocityControlResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VelocityControlResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[22]);
}

// ===================================================================

class TorqueControlRequest::_Internal {
 public:
};

TorqueControlRequest::TorqueControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.TorqueControlRequest)
}
TorqueControlRequest::TorqueControlRequest(const TorqueControlRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&torque_slope_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(torque_slope_));
  // @@protoc_insertion_point(copy_constructor:motor_control.TorqueControlRequest)
}

inline void TorqueControlRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&torque_slope_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(torque_slope_));
}

TorqueControlRequest::~TorqueControlRequest() {
  // @@protoc_insertion_point(destructor:motor_control.TorqueControlRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TorqueControlRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TorqueControlRequest::ArenaDtor(void* object) {
  TorqueControlRequest* _this = reinterpret_cast< TorqueControlRequest* >(object);
  (void)_this;
}
void TorqueControlRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TorqueControlRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TorqueControlRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.TorqueControlRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&torque_slope_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(torque_slope_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TorqueControlRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 target_torque = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          target_torque_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 torque_slope = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          torque_slope_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TorqueControlRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.TorqueControlRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // int32 target_torque = 2;
  if (this->_internal_target_torque() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_target_torque(), target);
  }

  // int32 torque_slope = 3;
  if (this->_internal_torque_slope() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_torque_slope(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.TorqueControlRequest)
  return target;
}

size_t TorqueControlRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.TorqueControlRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // int32 target_torque = 2;
  if (this->_internal_target_torque() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_target_torque());
  }

  // int32 torque_slope = 3;
  if (this->_internal_torque_slope() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_torque_slope());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TorqueControlRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TorqueControlRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TorqueControlRequest::GetClassData() const { return &_class_data_; }

void TorqueControlRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TorqueControlRequest *>(to)->MergeFrom(
      static_cast<const TorqueControlRequest &>(from));
}


void TorqueControlRequest::MergeFrom(const TorqueControlRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.TorqueControlRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_target_torque() != 0) {
    _internal_set_target_torque(from._internal_target_torque());
  }
  if (from._internal_torque_slope() != 0) {
    _internal_set_torque_slope(from._internal_torque_slope());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TorqueControlRequest::CopyFrom(const TorqueControlRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.TorqueControlRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TorqueControlRequest::IsInitialized() const {
  return true;
}

void TorqueControlRequest::InternalSwap(TorqueControlRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TorqueControlRequest, torque_slope_)
      + sizeof(TorqueControlRequest::torque_slope_)
      - PROTOBUF_FIELD_OFFSET(TorqueControlRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TorqueControlRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[23]);
}

// ===================================================================

class TorqueControlResponse::_Internal {
 public:
};

TorqueControlResponse::TorqueControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.TorqueControlResponse)
}
TorqueControlResponse::TorqueControlResponse(const TorqueControlResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&actual_torque_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_torque_));
  // @@protoc_insertion_point(copy_constructor:motor_control.TorqueControlResponse)
}

inline void TorqueControlResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&actual_torque_) -
    reinterpret_cast<char*>(&success_)) + sizeof(actual_torque_));
}

TorqueControlResponse::~TorqueControlResponse() {
  // @@protoc_insertion_point(destructor:motor_control.TorqueControlResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TorqueControlResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TorqueControlResponse::ArenaDtor(void* object) {
  TorqueControlResponse* _this = reinterpret_cast< TorqueControlResponse* >(object);
  (void)_this;
}
void TorqueControlResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TorqueControlResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TorqueControlResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.TorqueControlResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&actual_torque_) -
      reinterpret_cast<char*>(&success_)) + sizeof(actual_torque_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TorqueControlResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.TorqueControlResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 actual_torque = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          actual_torque_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TorqueControlResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.TorqueControlResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.TorqueControlResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // int32 actual_torque = 3;
  if (this->_internal_actual_torque() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_actual_torque(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.TorqueControlResponse)
  return target;
}

size_t TorqueControlResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.TorqueControlResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // int32 actual_torque = 3;
  if (this->_internal_actual_torque() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_actual_torque());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TorqueControlResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TorqueControlResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TorqueControlResponse::GetClassData() const { return &_class_data_; }

void TorqueControlResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TorqueControlResponse *>(to)->MergeFrom(
      static_cast<const TorqueControlResponse &>(from));
}


void TorqueControlResponse::MergeFrom(const TorqueControlResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.TorqueControlResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_actual_torque() != 0) {
    _internal_set_actual_torque(from._internal_actual_torque());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TorqueControlResponse::CopyFrom(const TorqueControlResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.TorqueControlResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TorqueControlResponse::IsInitialized() const {
  return true;
}

void TorqueControlResponse::InternalSwap(TorqueControlResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TorqueControlResponse, actual_torque_)
      + sizeof(TorqueControlResponse::actual_torque_)
      - PROTOBUF_FIELD_OFFSET(TorqueControlResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TorqueControlResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[24]);
}

// ===================================================================

class HomingRequest::_Internal {
 public:
};

HomingRequest::HomingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.HomingRequest)
}
HomingRequest::HomingRequest(const HomingRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&homing_acceleration_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(homing_acceleration_));
  // @@protoc_insertion_point(copy_constructor:motor_control.HomingRequest)
}

inline void HomingRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&homing_acceleration_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(homing_acceleration_));
}

HomingRequest::~HomingRequest() {
  // @@protoc_insertion_point(destructor:motor_control.HomingRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HomingRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HomingRequest::ArenaDtor(void* object) {
  HomingRequest* _this = reinterpret_cast< HomingRequest* >(object);
  (void)_this;
}
void HomingRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HomingRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HomingRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.HomingRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&homing_acceleration_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(homing_acceleration_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HomingRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 homing_method = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          homing_method_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 homing_speed_switch = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          homing_speed_switch_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 homing_speed_zero = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          homing_speed_zero_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 homing_acceleration = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          homing_acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HomingRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.HomingRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // int32 homing_method = 2;
  if (this->_internal_homing_method() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_homing_method(), target);
  }

  // int32 homing_speed_switch = 3;
  if (this->_internal_homing_speed_switch() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_homing_speed_switch(), target);
  }

  // int32 homing_speed_zero = 4;
  if (this->_internal_homing_speed_zero() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_homing_speed_zero(), target);
  }

  // int32 homing_acceleration = 5;
  if (this->_internal_homing_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_homing_acceleration(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.HomingRequest)
  return target;
}

size_t HomingRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.HomingRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // int32 homing_method = 2;
  if (this->_internal_homing_method() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_homing_method());
  }

  // int32 homing_speed_switch = 3;
  if (this->_internal_homing_speed_switch() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_homing_speed_switch());
  }

  // int32 homing_speed_zero = 4;
  if (this->_internal_homing_speed_zero() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_homing_speed_zero());
  }

  // int32 homing_acceleration = 5;
  if (this->_internal_homing_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_homing_acceleration());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HomingRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HomingRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HomingRequest::GetClassData() const { return &_class_data_; }

void HomingRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HomingRequest *>(to)->MergeFrom(
      static_cast<const HomingRequest &>(from));
}


void HomingRequest::MergeFrom(const HomingRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.HomingRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_homing_method() != 0) {
    _internal_set_homing_method(from._internal_homing_method());
  }
  if (from._internal_homing_speed_switch() != 0) {
    _internal_set_homing_speed_switch(from._internal_homing_speed_switch());
  }
  if (from._internal_homing_speed_zero() != 0) {
    _internal_set_homing_speed_zero(from._internal_homing_speed_zero());
  }
  if (from._internal_homing_acceleration() != 0) {
    _internal_set_homing_acceleration(from._internal_homing_acceleration());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HomingRequest::CopyFrom(const HomingRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.HomingRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HomingRequest::IsInitialized() const {
  return true;
}

void HomingRequest::InternalSwap(HomingRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HomingRequest, homing_acceleration_)
      + sizeof(HomingRequest::homing_acceleration_)
      - PROTOBUF_FIELD_OFFSET(HomingRequest, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HomingRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[25]);
}

// ===================================================================

class HomingResponse::_Internal {
 public:
};

HomingResponse::HomingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.HomingResponse)
}
HomingResponse::HomingResponse(const HomingResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&homing_completed_) -
    reinterpret_cast<char*>(&success_)) + sizeof(homing_completed_));
  // @@protoc_insertion_point(copy_constructor:motor_control.HomingResponse)
}

inline void HomingResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&homing_completed_) -
    reinterpret_cast<char*>(&success_)) + sizeof(homing_completed_));
}

HomingResponse::~HomingResponse() {
  // @@protoc_insertion_point(destructor:motor_control.HomingResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HomingResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void HomingResponse::ArenaDtor(void* object) {
  HomingResponse* _this = reinterpret_cast< HomingResponse* >(object);
  (void)_this;
}
void HomingResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HomingResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HomingResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.HomingResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&homing_completed_) -
      reinterpret_cast<char*>(&success_)) + sizeof(homing_completed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HomingResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.HomingResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool homing_completed = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          homing_completed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HomingResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.HomingResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.HomingResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // bool homing_completed = 3;
  if (this->_internal_homing_completed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_homing_completed(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.HomingResponse)
  return target;
}

size_t HomingResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.HomingResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // bool homing_completed = 3;
  if (this->_internal_homing_completed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HomingResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HomingResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HomingResponse::GetClassData() const { return &_class_data_; }

void HomingResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HomingResponse *>(to)->MergeFrom(
      static_cast<const HomingResponse &>(from));
}


void HomingResponse::MergeFrom(const HomingResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.HomingResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_homing_completed() != 0) {
    _internal_set_homing_completed(from._internal_homing_completed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HomingResponse::CopyFrom(const HomingResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.HomingResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HomingResponse::IsInitialized() const {
  return true;
}

void HomingResponse::InternalSwap(HomingResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HomingResponse, homing_completed_)
      + sizeof(HomingResponse::homing_completed_)
      - PROTOBUF_FIELD_OFFSET(HomingResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HomingResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[26]);
}

// ===================================================================

class EmergencyStopRequest::_Internal {
 public:
};

EmergencyStopRequest::EmergencyStopRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.EmergencyStopRequest)
}
EmergencyStopRequest::EmergencyStopRequest(const EmergencyStopRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:motor_control.EmergencyStopRequest)
}

inline void EmergencyStopRequest::SharedCtor() {
node_id_ = 0u;
}

EmergencyStopRequest::~EmergencyStopRequest() {
  // @@protoc_insertion_point(destructor:motor_control.EmergencyStopRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EmergencyStopRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EmergencyStopRequest::ArenaDtor(void* object) {
  EmergencyStopRequest* _this = reinterpret_cast< EmergencyStopRequest* >(object);
  (void)_this;
}
void EmergencyStopRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EmergencyStopRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EmergencyStopRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.EmergencyStopRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EmergencyStopRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EmergencyStopRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.EmergencyStopRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.EmergencyStopRequest)
  return target;
}

size_t EmergencyStopRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.EmergencyStopRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EmergencyStopRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EmergencyStopRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EmergencyStopRequest::GetClassData() const { return &_class_data_; }

void EmergencyStopRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EmergencyStopRequest *>(to)->MergeFrom(
      static_cast<const EmergencyStopRequest &>(from));
}


void EmergencyStopRequest::MergeFrom(const EmergencyStopRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.EmergencyStopRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EmergencyStopRequest::CopyFrom(const EmergencyStopRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.EmergencyStopRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EmergencyStopRequest::IsInitialized() const {
  return true;
}

void EmergencyStopRequest::InternalSwap(EmergencyStopRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(node_id_, other->node_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EmergencyStopRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[27]);
}

// ===================================================================

class EmergencyStopResponse::_Internal {
 public:
};

EmergencyStopResponse::EmergencyStopResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.EmergencyStopResponse)
}
EmergencyStopResponse::EmergencyStopResponse(const EmergencyStopResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  success_ = from.success_;
  // @@protoc_insertion_point(copy_constructor:motor_control.EmergencyStopResponse)
}

inline void EmergencyStopResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
success_ = false;
}

EmergencyStopResponse::~EmergencyStopResponse() {
  // @@protoc_insertion_point(destructor:motor_control.EmergencyStopResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EmergencyStopResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EmergencyStopResponse::ArenaDtor(void* object) {
  EmergencyStopResponse* _this = reinterpret_cast< EmergencyStopResponse* >(object);
  (void)_this;
}
void EmergencyStopResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EmergencyStopResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EmergencyStopResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.EmergencyStopResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  success_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EmergencyStopResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.EmergencyStopResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EmergencyStopResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.EmergencyStopResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.EmergencyStopResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.EmergencyStopResponse)
  return target;
}

size_t EmergencyStopResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.EmergencyStopResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EmergencyStopResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EmergencyStopResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EmergencyStopResponse::GetClassData() const { return &_class_data_; }

void EmergencyStopResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EmergencyStopResponse *>(to)->MergeFrom(
      static_cast<const EmergencyStopResponse &>(from));
}


void EmergencyStopResponse::MergeFrom(const EmergencyStopResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.EmergencyStopResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EmergencyStopResponse::CopyFrom(const EmergencyStopResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.EmergencyStopResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EmergencyStopResponse::IsInitialized() const {
  return true;
}

void EmergencyStopResponse::InternalSwap(EmergencyStopResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  swap(success_, other->success_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EmergencyStopResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[28]);
}

// ===================================================================

class ClearFaultRequest::_Internal {
 public:
};

ClearFaultRequest::ClearFaultRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.ClearFaultRequest)
}
ClearFaultRequest::ClearFaultRequest(const ClearFaultRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:motor_control.ClearFaultRequest)
}

inline void ClearFaultRequest::SharedCtor() {
node_id_ = 0u;
}

ClearFaultRequest::~ClearFaultRequest() {
  // @@protoc_insertion_point(destructor:motor_control.ClearFaultRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ClearFaultRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ClearFaultRequest::ArenaDtor(void* object) {
  ClearFaultRequest* _this = reinterpret_cast< ClearFaultRequest* >(object);
  (void)_this;
}
void ClearFaultRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ClearFaultRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ClearFaultRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.ClearFaultRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ClearFaultRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ClearFaultRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.ClearFaultRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.ClearFaultRequest)
  return target;
}

size_t ClearFaultRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.ClearFaultRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ClearFaultRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ClearFaultRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ClearFaultRequest::GetClassData() const { return &_class_data_; }

void ClearFaultRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ClearFaultRequest *>(to)->MergeFrom(
      static_cast<const ClearFaultRequest &>(from));
}


void ClearFaultRequest::MergeFrom(const ClearFaultRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.ClearFaultRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ClearFaultRequest::CopyFrom(const ClearFaultRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.ClearFaultRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClearFaultRequest::IsInitialized() const {
  return true;
}

void ClearFaultRequest::InternalSwap(ClearFaultRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(node_id_, other->node_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ClearFaultRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[29]);
}

// ===================================================================

class ClearFaultResponse::_Internal {
 public:
};

ClearFaultResponse::ClearFaultResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.ClearFaultResponse)
}
ClearFaultResponse::ClearFaultResponse(const ClearFaultResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  ::memcpy(&success_, &from.success_,
    static_cast<size_t>(reinterpret_cast<char*>(&fault_cleared_) -
    reinterpret_cast<char*>(&success_)) + sizeof(fault_cleared_));
  // @@protoc_insertion_point(copy_constructor:motor_control.ClearFaultResponse)
}

inline void ClearFaultResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&success_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&fault_cleared_) -
    reinterpret_cast<char*>(&success_)) + sizeof(fault_cleared_));
}

ClearFaultResponse::~ClearFaultResponse() {
  // @@protoc_insertion_point(destructor:motor_control.ClearFaultResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ClearFaultResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ClearFaultResponse::ArenaDtor(void* object) {
  ClearFaultResponse* _this = reinterpret_cast< ClearFaultResponse* >(object);
  (void)_this;
}
void ClearFaultResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ClearFaultResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ClearFaultResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.ClearFaultResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  ::memset(&success_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fault_cleared_) -
      reinterpret_cast<char*>(&success_)) + sizeof(fault_cleared_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ClearFaultResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.ClearFaultResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fault_cleared = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          fault_cleared_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ClearFaultResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.ClearFaultResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.ClearFaultResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // bool fault_cleared = 3;
  if (this->_internal_fault_cleared() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_fault_cleared(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.ClearFaultResponse)
  return target;
}

size_t ClearFaultResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.ClearFaultResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  // bool fault_cleared = 3;
  if (this->_internal_fault_cleared() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ClearFaultResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ClearFaultResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ClearFaultResponse::GetClassData() const { return &_class_data_; }

void ClearFaultResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ClearFaultResponse *>(to)->MergeFrom(
      static_cast<const ClearFaultResponse &>(from));
}


void ClearFaultResponse::MergeFrom(const ClearFaultResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.ClearFaultResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  if (from._internal_fault_cleared() != 0) {
    _internal_set_fault_cleared(from._internal_fault_cleared());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ClearFaultResponse::CopyFrom(const ClearFaultResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.ClearFaultResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClearFaultResponse::IsInitialized() const {
  return true;
}

void ClearFaultResponse::InternalSwap(ClearFaultResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ClearFaultResponse, fault_cleared_)
      + sizeof(ClearFaultResponse::fault_cleared_)
      - PROTOBUF_FIELD_OFFSET(ClearFaultResponse, success_)>(
          reinterpret_cast<char*>(&success_),
          reinterpret_cast<char*>(&other->success_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ClearFaultResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[30]);
}

// ===================================================================

class GetAllNodesRequest::_Internal {
 public:
};

GetAllNodesRequest::GetAllNodesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:motor_control.GetAllNodesRequest)
}
GetAllNodesRequest::GetAllNodesRequest(const GetAllNodesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:motor_control.GetAllNodesRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetAllNodesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetAllNodesRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetAllNodesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[31]);
}

// ===================================================================

class NodeInfo::_Internal {
 public:
};

NodeInfo::NodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.NodeInfo)
}
NodeInfo::NodeInfo(const NodeInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  vendor_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    vendor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_vendor_name().empty()) {
    vendor_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_vendor_name(), 
      GetArenaForAllocation());
  }
  product_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    product_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_product_name().empty()) {
    product_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_product_name(), 
      GetArenaForAllocation());
  }
  revision_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    revision_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_revision_number().empty()) {
    revision_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_revision_number(), 
      GetArenaForAllocation());
  }
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(state_));
  // @@protoc_insertion_point(copy_constructor:motor_control.NodeInfo)
}

inline void NodeInfo::SharedCtor() {
vendor_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  vendor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
product_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  product_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
revision_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  revision_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&node_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(state_));
}

NodeInfo::~NodeInfo() {
  // @@protoc_insertion_point(destructor:motor_control.NodeInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void NodeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  vendor_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  product_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  revision_number_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void NodeInfo::ArenaDtor(void* object) {
  NodeInfo* _this = reinterpret_cast< NodeInfo* >(object);
  (void)_this;
}
void NodeInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void NodeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void NodeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.NodeInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  vendor_name_.ClearToEmpty();
  product_name_.ClearToEmpty();
  revision_number_.ClearToEmpty();
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&state_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(state_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NodeInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_connected = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          is_connected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .motor_control.DriveState state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::motor_control::DriveState>(val));
        } else
          goto handle_unusual;
        continue;
      // string vendor_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_vendor_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.NodeInfo.vendor_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string product_name = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_product_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.NodeInfo.product_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string revision_number = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_revision_number();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.NodeInfo.revision_number"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* NodeInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.NodeInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // bool is_connected = 2;
  if (this->_internal_is_connected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_is_connected(), target);
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_state(), target);
  }

  // string vendor_name = 4;
  if (!this->_internal_vendor_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_vendor_name().data(), static_cast<int>(this->_internal_vendor_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.NodeInfo.vendor_name");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_vendor_name(), target);
  }

  // string product_name = 5;
  if (!this->_internal_product_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_product_name().data(), static_cast<int>(this->_internal_product_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.NodeInfo.product_name");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_product_name(), target);
  }

  // string revision_number = 6;
  if (!this->_internal_revision_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_revision_number().data(), static_cast<int>(this->_internal_revision_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.NodeInfo.revision_number");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_revision_number(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.NodeInfo)
  return target;
}

size_t NodeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.NodeInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string vendor_name = 4;
  if (!this->_internal_vendor_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_vendor_name());
  }

  // string product_name = 5;
  if (!this->_internal_product_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_product_name());
  }

  // string revision_number = 6;
  if (!this->_internal_revision_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_revision_number());
  }

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // bool is_connected = 2;
  if (this->_internal_is_connected() != 0) {
    total_size += 1 + 1;
  }

  // .motor_control.DriveState state = 3;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData NodeInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    NodeInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*NodeInfo::GetClassData() const { return &_class_data_; }

void NodeInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<NodeInfo *>(to)->MergeFrom(
      static_cast<const NodeInfo &>(from));
}


void NodeInfo::MergeFrom(const NodeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.NodeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_vendor_name().empty()) {
    _internal_set_vendor_name(from._internal_vendor_name());
  }
  if (!from._internal_product_name().empty()) {
    _internal_set_product_name(from._internal_product_name());
  }
  if (!from._internal_revision_number().empty()) {
    _internal_set_revision_number(from._internal_revision_number());
  }
  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_is_connected() != 0) {
    _internal_set_is_connected(from._internal_is_connected());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void NodeInfo::CopyFrom(const NodeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.NodeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeInfo::IsInitialized() const {
  return true;
}

void NodeInfo::InternalSwap(NodeInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &vendor_name_, lhs_arena,
      &other->vendor_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &product_name_, lhs_arena,
      &other->product_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &revision_number_, lhs_arena,
      &other->revision_number_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeInfo, state_)
      + sizeof(NodeInfo::state_)
      - PROTOBUF_FIELD_OFFSET(NodeInfo, node_id_)>(
          reinterpret_cast<char*>(&node_id_),
          reinterpret_cast<char*>(&other->node_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata NodeInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[32]);
}

// ===================================================================

class GetAllNodesResponse::_Internal {
 public:
};

GetAllNodesResponse::GetAllNodesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  nodes_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.GetAllNodesResponse)
}
GetAllNodesResponse::GetAllNodesResponse(const GetAllNodesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      nodes_(from.nodes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  success_ = from.success_;
  // @@protoc_insertion_point(copy_constructor:motor_control.GetAllNodesResponse)
}

inline void GetAllNodesResponse::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
success_ = false;
}

GetAllNodesResponse::~GetAllNodesResponse() {
  // @@protoc_insertion_point(destructor:motor_control.GetAllNodesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetAllNodesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetAllNodesResponse::ArenaDtor(void* object) {
  GetAllNodesResponse* _this = reinterpret_cast< GetAllNodesResponse* >(object);
  (void)_this;
}
void GetAllNodesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetAllNodesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetAllNodesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.GetAllNodesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nodes_.Clear();
  message_.ClearToEmpty();
  success_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetAllNodesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.GetAllNodesResponse.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .motor_control.NodeInfo nodes = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_nodes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetAllNodesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.GetAllNodesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.GetAllNodesResponse.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // repeated .motor_control.NodeInfo nodes = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_nodes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_nodes(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.GetAllNodesResponse)
  return target;
}

size_t GetAllNodesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.GetAllNodesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .motor_control.NodeInfo nodes = 3;
  total_size += 1UL * this->_internal_nodes_size();
  for (const auto& msg : this->nodes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetAllNodesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetAllNodesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetAllNodesResponse::GetClassData() const { return &_class_data_; }

void GetAllNodesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetAllNodesResponse *>(to)->MergeFrom(
      static_cast<const GetAllNodesResponse &>(from));
}


void GetAllNodesResponse::MergeFrom(const GetAllNodesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.GetAllNodesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  nodes_.MergeFrom(from.nodes_);
  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetAllNodesResponse::CopyFrom(const GetAllNodesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.GetAllNodesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetAllNodesResponse::IsInitialized() const {
  return true;
}

void GetAllNodesResponse::InternalSwap(GetAllNodesResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  nodes_.InternalSwap(&other->nodes_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  swap(success_, other->success_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetAllNodesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[33]);
}

// ===================================================================

class DeviceConfig::_Internal {
 public:
};

DeviceConfig::DeviceConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.DeviceConfig)
}
DeviceConfig::DeviceConfig(const DeviceConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  device_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device().empty()) {
    device_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_device(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:motor_control.DeviceConfig)
}

inline void DeviceConfig::SharedCtor() {
device_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeviceConfig::~DeviceConfig() {
  // @@protoc_insertion_point(destructor:motor_control.DeviceConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeviceConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  device_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeviceConfig::ArenaDtor(void* object) {
  DeviceConfig* _this = reinterpret_cast< DeviceConfig* >(object);
  (void)_this;
}
void DeviceConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeviceConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeviceConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.DeviceConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_.ClearToEmpty();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeviceConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string device = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_device();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.DeviceConfig.device"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.DeviceConfig.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeviceConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.DeviceConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string device = 1;
  if (!this->_internal_device().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device().data(), static_cast<int>(this->_internal_device().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.DeviceConfig.device");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_device(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.DeviceConfig.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.DeviceConfig)
  return target;
}

size_t DeviceConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.DeviceConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string device = 1;
  if (!this->_internal_device().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeviceConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeviceConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeviceConfig::GetClassData() const { return &_class_data_; }

void DeviceConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeviceConfig *>(to)->MergeFrom(
      static_cast<const DeviceConfig &>(from));
}


void DeviceConfig::MergeFrom(const DeviceConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.DeviceConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_device().empty()) {
    _internal_set_device(from._internal_device());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeviceConfig::CopyFrom(const DeviceConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.DeviceConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceConfig::IsInitialized() const {
  return true;
}

void DeviceConfig::InternalSwap(DeviceConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &device_, lhs_arena,
      &other->device_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeviceConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[34]);
}

// ===================================================================

class ConfigMessage::_Internal {
 public:
};

ConfigMessage::ConfigMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  node_ids_(arena),
  uart_devices_(arena),
  i2c_devices_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:motor_control.ConfigMessage)
}
ConfigMessage::ConfigMessage(const ConfigMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      node_ids_(from.node_ids_),
      uart_devices_(from.uart_devices_),
      i2c_devices_(from.i2c_devices_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  can_interface_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    can_interface_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_can_interface().empty()) {
    can_interface_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_can_interface(), 
      GetArenaForAllocation());
  }
  grpc_server_address_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    grpc_server_address_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_grpc_server_address().empty()) {
    grpc_server_address_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_grpc_server_address(), 
      GetArenaForAllocation());
  }
  status_socket_port_ = from.status_socket_port_;
  // @@protoc_insertion_point(copy_constructor:motor_control.ConfigMessage)
}

inline void ConfigMessage::SharedCtor() {
can_interface_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  can_interface_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
grpc_server_address_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  grpc_server_address_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_socket_port_ = 0;
}

ConfigMessage::~ConfigMessage() {
  // @@protoc_insertion_point(destructor:motor_control.ConfigMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  can_interface_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  grpc_server_address_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ConfigMessage::ArenaDtor(void* object) {
  ConfigMessage* _this = reinterpret_cast< ConfigMessage* >(object);
  (void)_this;
}
void ConfigMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:motor_control.ConfigMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_ids_.Clear();
  uart_devices_.Clear();
  i2c_devices_.Clear();
  can_interface_.ClearToEmpty();
  grpc_server_address_.ClearToEmpty();
  status_socket_port_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string can_interface = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_can_interface();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.ConfigMessage.can_interface"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string grpc_server_address = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_grpc_server_address();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "motor_control.ConfigMessage.grpc_server_address"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 status_socket_port = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          status_socket_port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 node_ids = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_node_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 32) {
          _internal_add_node_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .motor_control.DeviceConfig uart_devices = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_uart_devices(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .motor_control.DeviceConfig i2c_devices = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_i2c_devices(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:motor_control.ConfigMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string can_interface = 1;
  if (!this->_internal_can_interface().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_can_interface().data(), static_cast<int>(this->_internal_can_interface().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.ConfigMessage.can_interface");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_can_interface(), target);
  }

  // string grpc_server_address = 2;
  if (!this->_internal_grpc_server_address().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_grpc_server_address().data(), static_cast<int>(this->_internal_grpc_server_address().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "motor_control.ConfigMessage.grpc_server_address");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_grpc_server_address(), target);
  }

  // int32 status_socket_port = 3;
  if (this->_internal_status_socket_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_status_socket_port(), target);
  }

  // repeated uint32 node_ids = 4;
  {
    int byte_size = _node_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          4, _internal_node_ids(), byte_size, target);
    }
  }

  // repeated .motor_control.DeviceConfig uart_devices = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_uart_devices_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_uart_devices(i), target, stream);
  }

  // repeated .motor_control.DeviceConfig i2c_devices = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_i2c_devices_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_i2c_devices(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:motor_control.ConfigMessage)
  return target;
}

size_t ConfigMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:motor_control.ConfigMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint32 node_ids = 4;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->node_ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _node_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .motor_control.DeviceConfig uart_devices = 5;
  total_size += 1UL * this->_internal_uart_devices_size();
  for (const auto& msg : this->uart_devices_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .motor_control.DeviceConfig i2c_devices = 6;
  total_size += 1UL * this->_internal_i2c_devices_size();
  for (const auto& msg : this->i2c_devices_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string can_interface = 1;
  if (!this->_internal_can_interface().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_can_interface());
  }

  // string grpc_server_address = 2;
  if (!this->_internal_grpc_server_address().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_grpc_server_address());
  }

  // int32 status_socket_port = 3;
  if (this->_internal_status_socket_port() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_status_socket_port());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigMessage::GetClassData() const { return &_class_data_; }

void ConfigMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigMessage *>(to)->MergeFrom(
      static_cast<const ConfigMessage &>(from));
}


void ConfigMessage::MergeFrom(const ConfigMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:motor_control.ConfigMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  node_ids_.MergeFrom(from.node_ids_);
  uart_devices_.MergeFrom(from.uart_devices_);
  i2c_devices_.MergeFrom(from.i2c_devices_);
  if (!from._internal_can_interface().empty()) {
    _internal_set_can_interface(from._internal_can_interface());
  }
  if (!from._internal_grpc_server_address().empty()) {
    _internal_set_grpc_server_address(from._internal_grpc_server_address());
  }
  if (from._internal_status_socket_port() != 0) {
    _internal_set_status_socket_port(from._internal_status_socket_port());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigMessage::CopyFrom(const ConfigMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:motor_control.ConfigMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigMessage::IsInitialized() const {
  return true;
}

void ConfigMessage::InternalSwap(ConfigMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  node_ids_.InternalSwap(&other->node_ids_);
  uart_devices_.InternalSwap(&other->uart_devices_);
  i2c_devices_.InternalSwap(&other->i2c_devices_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &can_interface_, lhs_arena,
      &other->can_interface_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &grpc_server_address_, lhs_arena,
      &other->grpc_server_address_, rhs_arena
  );
  swap(status_socket_port_, other->status_socket_port_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_motor_5fcontrol_2eproto_getter, &descriptor_table_motor_5fcontrol_2eproto_once,
      file_level_metadata_motor_5fcontrol_2eproto[35]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace motor_control
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::motor_control::StartMoveDescription* Arena::CreateMaybeMessage< ::motor_control::StartMoveDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::StartMoveDescription >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::StartMoveStatus* Arena::CreateMaybeMessage< ::motor_control::StartMoveStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::StartMoveStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::CommonDescription* Arena::CreateMaybeMessage< ::motor_control::CommonDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::CommonDescription >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::CommonStatus* Arena::CreateMaybeMessage< ::motor_control::CommonStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::CommonStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::GetPostIdDescription* Arena::CreateMaybeMessage< ::motor_control::GetPostIdDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::GetPostIdDescription >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::GetPostIdStatus* Arena::CreateMaybeMessage< ::motor_control::GetPostIdStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::GetPostIdStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::SystemStatusInfoStatus* Arena::CreateMaybeMessage< ::motor_control::SystemStatusInfoStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::SystemStatusInfoStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::GetTriggerInfoDescription* Arena::CreateMaybeMessage< ::motor_control::GetTriggerInfoDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::GetTriggerInfoDescription >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::TriggerInfoStatus* Arena::CreateMaybeMessage< ::motor_control::TriggerInfoStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::TriggerInfoStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotionInfo* Arena::CreateMaybeMessage< ::motor_control::MotionInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotionInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotionCapability* Arena::CreateMaybeMessage< ::motor_control::MotionCapability >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotionCapability >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::BedStatus* Arena::CreateMaybeMessage< ::motor_control::BedStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::BedStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::PostIdInfo* Arena::CreateMaybeMessage< ::motor_control::PostIdInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::PostIdInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotorControlRequest* Arena::CreateMaybeMessage< ::motor_control::MotorControlRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotorControlRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotorControlResponse* Arena::CreateMaybeMessage< ::motor_control::MotorControlResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotorControlResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotorStatusRequest* Arena::CreateMaybeMessage< ::motor_control::MotorStatusRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotorStatusRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::MotorStatusResponse* Arena::CreateMaybeMessage< ::motor_control::MotorStatusResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::MotorStatusResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::OperationModeRequest* Arena::CreateMaybeMessage< ::motor_control::OperationModeRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::OperationModeRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::OperationModeResponse* Arena::CreateMaybeMessage< ::motor_control::OperationModeResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::OperationModeResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::PositionControlRequest* Arena::CreateMaybeMessage< ::motor_control::PositionControlRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::PositionControlRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::PositionControlResponse* Arena::CreateMaybeMessage< ::motor_control::PositionControlResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::PositionControlResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::VelocityControlRequest* Arena::CreateMaybeMessage< ::motor_control::VelocityControlRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::VelocityControlRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::VelocityControlResponse* Arena::CreateMaybeMessage< ::motor_control::VelocityControlResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::VelocityControlResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::TorqueControlRequest* Arena::CreateMaybeMessage< ::motor_control::TorqueControlRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::TorqueControlRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::TorqueControlResponse* Arena::CreateMaybeMessage< ::motor_control::TorqueControlResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::TorqueControlResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::HomingRequest* Arena::CreateMaybeMessage< ::motor_control::HomingRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::HomingRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::HomingResponse* Arena::CreateMaybeMessage< ::motor_control::HomingResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::HomingResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::EmergencyStopRequest* Arena::CreateMaybeMessage< ::motor_control::EmergencyStopRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::EmergencyStopRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::EmergencyStopResponse* Arena::CreateMaybeMessage< ::motor_control::EmergencyStopResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::EmergencyStopResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::ClearFaultRequest* Arena::CreateMaybeMessage< ::motor_control::ClearFaultRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::ClearFaultRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::ClearFaultResponse* Arena::CreateMaybeMessage< ::motor_control::ClearFaultResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::ClearFaultResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::GetAllNodesRequest* Arena::CreateMaybeMessage< ::motor_control::GetAllNodesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::GetAllNodesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::NodeInfo* Arena::CreateMaybeMessage< ::motor_control::NodeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::NodeInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::GetAllNodesResponse* Arena::CreateMaybeMessage< ::motor_control::GetAllNodesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::GetAllNodesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::DeviceConfig* Arena::CreateMaybeMessage< ::motor_control::DeviceConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::DeviceConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::motor_control::ConfigMessage* Arena::CreateMaybeMessage< ::motor_control::ConfigMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::motor_control::ConfigMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
