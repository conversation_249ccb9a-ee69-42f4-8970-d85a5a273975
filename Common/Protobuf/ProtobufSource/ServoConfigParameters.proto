syntax = "proto3";

package ServoConfigProto;
///////////////////////////////////////////json配置文件相关定义////////////////////////////////////////
// 伺服电机类型
enum ServoMotorType {
    GENERIC = 0;       // 通用伺服电机
    COPLEY_FIRST = 1;  // 一级床伺服,
    COPLEY_SECOND = 2;  // 二级床伺服
}

// 伺服配置相关消息定义
// 位置模式配置
message PositionModeConfig {
  int32 profile_velocity = 1;
  int32 profile_acceleration = 2;
  int32 profile_deceleration = 3;
  bool use_limit_switches = 4;
  int32 positive_limit = 5;
  int32 negative_limit = 6;
  int32 position_tracking_window = 7;
  int32 position_tracking_window_time = 8;
}

// 速度模式配置
message VelocityModeConfig {
  int32 profile_acceleration = 1;
  int32 profile_deceleration = 2;
  int32 max_velocity = 3;
  int32 min_velocity = 4;
}

// 回零配置
message HomingConfig {
  int32 method = 1;
  int32 speed_switch = 2;
  int32 speed_zero = 3;
  int32 acceleration = 4;
  int32 offset = 5;
}

// 输入引脚配置
message InputPinConfig {
  int32 pin_index = 1;
  int32 configuration = 2;
  uint32 debounce_values = 3;
}

// 触摸探针配置
message TouchProbeConfig {
  int32 probe_index = 1;
  int32 function = 2;
  int32 io_select = 3;
  uint32 debounce_values = 4;
}

// PDO映射条目配置
message PDOMappingEntry {
  uint32 object_index = 1;    // 对象字典索引（如0x6041）
  uint32 sub_index = 2;       // 子索引
  uint32 data_length = 3;     // 数据长度（位数）
}

// TPDO配置
message TPDOConfig {
  uint32 pdo_number = 1;                        // TPDO编号（1, 2, 3...）
  uint32 transmission_type = 2;                 // 传输类型
  repeated PDOMappingEntry mapping_entries = 3; // PDO映射条目数组
}

// 伺服电机配置
message ServoMotorConfig {
  uint32 node_id = 1;
  string name = 2;
  string type = 3;
  int32 encoder_resolution = 4;
  PositionModeConfig position_mode = 5;
  VelocityModeConfig velocity_mode = 6;
  HomingConfig homing = 7;
  repeated InputPinConfig input_pins = 8;
  repeated TouchProbeConfig touch_probes = 9;
  repeated TPDOConfig tpdo_config = 10;  // TPDO配置数组
}

// 心跳配置
message HeartbeatConfig {
  uint32 timeout_ms = 1;              // 心跳超时时间（毫秒）
  uint32 check_interval_ms = 2;       // 检查间隔（毫秒）
  bool enable_auto_homing = 3;        // 是否启用自动回零
  bool enable_heartbeat_monitor = 4;  // 是否启用心跳监控
  uint32 max_missed_heartbeats = 5;   // 最大允许丢失心跳次数
}

// 伺服配置消息
message ServoConfigMessage {
  repeated ServoMotorConfig servo_motors = 1;
  HeartbeatConfig heartbeat_config = 2;  // 心跳配置
}