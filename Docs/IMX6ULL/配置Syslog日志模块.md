# 1. 配置syslog日志路径

## 修改syslog配置文件 (/etc/rsyslog.conf)

```shell

# 为你的应用配置专用日志文件
local6.*    /var/log/bedmaster/bedmaster.log

# 或者按级别分离
local6.info    /var/log/bedmaster/bedmaster.log
local6.err     /var/log/bedmaster/bedmaster_error.log
```

## 创建日志目录

```shell
sudo mkdir -p /var/log/bedmaster
sudo chown syslog:adm /var/log/bedmaster
sudo chmod 755 /var/log/bedmaster
```

# 2. 配置logrotate进行日志轮转

## 创建logrotate配置文件 (/etc/logrotate.d/bedmaster)

```text
/var/log/bedmaster/bedmaster.log {
    daily
    size 50M               # 每日或达到50MB时轮转
    rotate 15
    compress
    delaycompress
    missingok
    notifempty
    create 644 syslog adm
    dateext                # 使用日期作为后缀
    dateformat -%Y%m%d-%s
    postrotate
        /bin/kill -HUP `cat /var/run/rsyslogd.pid 2> /dev/null` 2> /dev/null || true
    endscript
}

/var/log/bedmaster/bedmaster_error.log {
    weekly                 # 错误日志每周轮转
    rotate 52              # 保留一年
    compress
    delaycompress
    missingok
    notifempty
    create 644 syslog adm
    dateext
}
```

# 3.测试配置

```shell
# 测试配置语法
sudo logrotate -d /etc/logrotate.d/bedmaster

# 强制执行轮转（测试用）
sudo logrotate -f /etc/logrotate.d/bedmaster

# 重启rsyslog服务
sudo systemctl restart rsyslog

# 检查服务状态
sudo systemctl status rsyslog
```