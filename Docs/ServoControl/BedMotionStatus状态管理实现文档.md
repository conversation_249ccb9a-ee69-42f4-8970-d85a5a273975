# BedMotionStatus 状态管理功能实现文档

## 概述

本文档详细描述了在ServoControlManager类中实现的BedMotionStatus枚举状态管理功能。该功能提供了完整的床位运动状态管理，包括状态切换验证、线程安全保护和完整的日志记录。

## 状态定义

```cpp
enum class BedMotionStatus {
    Ready = 0,   // 就绪状态 - 可以接受新的运动命令
    Moving = 1,  // 运动中状态 - 正在执行运动命令
    Estop = 2,   // 紧急停止状态 - 执行了紧急停止或ESTOP信号触发
    Error = 3    // 错误状态 - 电机故障、操作超时或系统异常
};
```

## 核心功能

### 1. 状态存储和管理

**私有成员变量：**
```cpp
std::map<BedType, BedMotionStatus> bed_motion_status_;  // 床位运动状态存储
mutable std::mutex bed_status_mutex_;                   // 床位状态互斥锁
```

**初始化：**
- 在构造函数中将Primary和Secondary床位状态初始化为Ready

### 2. 公共接口

**获取床位状态：**
```cpp
BedMotionStatus GetBedMotionStatus(BedType bedType);
```
- 支持单个床位状态查询（Primary/Secondary）
- 支持Both类型查询，返回两个床位中较"严重"的状态
- 状态优先级：Error > Estop > Moving > Ready

### 3. 内部状态管理

**设置床位状态：**
```cpp
bool SetBedMotionStatus(BedType bedType, BedMotionStatus newStatus, const std::string& reason);
```
- 包含状态转换验证
- 记录详细的状态变化日志
- 线程安全保护

**状态转换验证：**
```cpp
bool IsValidStatusTransition(BedMotionStatus currentStatus, BedMotionStatus newStatus);
```

## 状态转换规则

| 当前状态 | 可转换到 | 触发条件 |
|---------|---------|---------|
| Ready | Moving | startMoveBed(), homingBed() |
| Ready | Estop | emergencyStop(), ESTOP信号 |
| Ready | Error | 电机故障, 系统异常 |
| Moving | Ready | 运动完成成功 |
| Moving | Estop | stopMoveBed(), emergencyStop(), ESTOP信号 |
| Moving | Error | 运动失败, 操作超时, 异常 |
| Estop | Ready | 紧急清理成功, 状态恢复 |
| Estop | Error | 紧急清理失败, 系统故障 |
| Error | Ready | 故障清除, 电机状态恢复正常 |

## 集成到床位控制方法

### 1. startMoveBed 方法
```cpp
// 设置床位状态为运动中
if (!SetBedMotionStatus(bedType, BedMotionStatus::Moving, "开始移床操作")) {
    return false;
}

// 运动完成后的状态更新
if (result) {
    SetBedMotionStatus(bedType, BedMotionStatus::Ready, "移床操作完成");
} else {
    SetBedMotionStatus(bedType, BedMotionStatus::Error, "移床操作失败");
}
```

### 2. stopMoveBed 方法
```cpp
if (result) {
    SetBedMotionStatus(bedType, BedMotionStatus::Estop, "执行停床操作");
} else {
    SetBedMotionStatus(bedType, BedMotionStatus::Error, "停床操作失败");
}
```

### 3. homingBed 方法
```cpp
// 开始回零
SetBedMotionStatus(bedType, BedMotionStatus::Moving, "开始回零操作");

// 回零完成
if (result) {
    SetBedMotionStatus(bedType, BedMotionStatus::Ready, "回零操作完成");
} else {
    SetBedMotionStatus(bedType, BedMotionStatus::Error, "回零操作失败");
}
```

### 4. emergencyStop 方法
```cpp
// 紧急停止成功
SetBedMotionStatus(bedType, BedMotionStatus::Estop, "执行紧急停止");
```

## 状态监控集成

在状态监控线程中添加了自动状态同步功能：

```cpp
// 检查电机故障状态
if (status.second.hasFault) {
    SetBedMotionStatus(bedType, BedMotionStatus::Error, "电机故障检测");
}

// 检查ESTOP信号
if (!(status.second.ioStatus & 0x0001)) {
    SetBedMotionStatus(bedType, BedMotionStatus::Estop, "ESTOP信号触发");
}

// 自动恢复到Ready状态
if (currentBedStatus == BedMotionStatus::Error &&
    status.second.state == DS402State::OPERATION_ENABLED) {
    SetBedMotionStatus(bedType, BedMotionStatus::Ready, "电机状态恢复正常");
}
```

## 线程安全设计

- 使用专门的`bed_status_mutex_`互斥锁保护状态访问
- 所有状态读写操作都在锁保护下进行
- 支持多线程环境下的并发访问

## 日志记录

使用Linux syslog系统记录状态变化：
```cpp
LOG_INFO_MSG(domain_, "床位状态变化 - 床位: %s, 从 %s 变为 %s, 原因: %s", 
             GetBedTypeName(bedType), GetStatusName(currentStatus), 
             GetStatusName(newStatus), reason.c_str());
```

日志包含：
- 时间戳（毫秒/微秒精度）
- 线程ID
- 类名（ServoControlManager）
- 床位类型
- 状态变化详情
- 变化原因

## 辅助方法

```cpp
// 状态名称转换
const char* GetStatusName(BedMotionStatus status);

// 床位类型名称转换  
const char* GetBedTypeName(BedType bedType);

// 节点ID到床位类型转换
BedType nodeIdToBedType(uint8_t nodeId);
```

## 错误处理

- 完善的状态转换验证，防止无效状态切换
- 异常情况下自动设置Error状态
- 支持从Error状态自动恢复到Ready状态
- 详细的错误日志记录

## 使用示例

```cpp
// 获取床位状态
BedMotionStatus status = GetBedMotionStatus(BedType::Primary);

// 检查是否可以开始运动
if (status == BedMotionStatus::Ready) {
    // 可以开始运动
    startMoveBed(BedType::Primary, mode, position, velocity, owner);
}

// 获取两张床的综合状态
BedMotionStatus bothStatus = GetBedMotionStatus(BedType::Both);
```

## 注意事项

1. **状态一致性**：状态管理与电机状态保持同步
2. **线程安全**：所有状态操作都是线程安全的
3. **状态验证**：严格的状态转换规则验证
4. **日志完整性**：所有状态变化都有详细记录
5. **错误恢复**：支持自动和手动错误恢复机制

## 状态机图表

### 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Ready : 系统初始化

    Ready --> Moving : 开始移床操作<br/>startMoveBed()
    Ready --> Estop : 紧急停止<br/>emergencyStop()
    Ready --> Error : 系统错误<br/>故障检测

    Moving --> Ready : 运动完成<br/>操作成功
    Moving --> Estop : 紧急停止<br/>emergencyStop()<br/>ESTOP信号触发
    Moving --> Error : 运动失败<br/>超时/异常/故障

    Estop --> Ready : 状态恢复<br/>紧急清理成功
    Estop --> Error : 清理失败<br/>系统故障

    Error --> Ready : 错误恢复<br/>故障清除<br/>电机状态正常
```

### 状态转换矩阵

| 从\到 | Ready | Moving | Estop | Error |
|-------|-------|--------|-------|-------|
| **Ready** | ❌ | ✅ 开始运动 | ✅ 紧急停止 | ✅ 故障检测 |
| **Moving** | ✅ 运动完成 | ❌ | ✅ 紧急停止 | ✅ 运动失败 |
| **Estop** | ✅ 状态恢复 | ❌ | ❌ | ✅ 清理失败 |
| **Error** | ✅ 错误恢复 | ❌ | ❌ | ❌ |

## 实现文件清单

### 修改的文件

1. **Business/ServoControl/ServoControlManager.hpp**
   - 添加状态管理相关的公共和私有方法声明
   - 添加状态存储成员变量和互斥锁

2. **Business/ServoControl/ServoControlManager.cpp**
   - 实现状态管理核心功能
   - 集成到现有床位控制方法
   - 添加状态监控线程的状态同步逻辑

### 新增功能模块

- 状态获取和设置接口
- 状态转换验证逻辑
- 状态名称转换辅助方法
- 节点ID到床位类型映射
- 自动状态同步机制

## 测试建议

1. **状态转换测试**：验证所有有效和无效的状态转换
2. **并发测试**：多线程环境下的状态管理测试
3. **异常处理测试**：各种异常情况下的状态行为
4. **日志验证**：确保状态变化日志的完整性和准确性
5. **恢复机制测试**：验证从错误状态的恢复能力

## 版本信息

- **实现日期**：2025-01-22
- **开发环境**：Linux
- **编程语言**：C++17
- **命名规范**：Google C++命名规范
- **日志系统**：Linux syslog
