<mxfile host="65bd71144e">
    <diagram id="-DBLpzhUpLZF21YyUsdR" name="Page-1">
        <mxGraphModel dx="874" dy="1884" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="3" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="GainControl" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="346" y="-946" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="5" target="11">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="StartMove" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="346" y="-692" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="7" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="gRpc客户端开始发送控床工作流" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="346" y="-1080" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="已返回" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="11" target="14">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="11">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="406" y="-614" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="282" y="-538"/>
                            <mxPoint x="282" y="-614"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="未返回" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="9">
                    <mxGeometry x="-0.2745" y="-1" relative="1" as="geometry">
                        <mxPoint x="-1" y="6" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="等待返回请求结果" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="319.5" y="-578" width="173" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="结束" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="346" y="-334" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="14" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="ReleaseControl" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="346" y="-442" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="18" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="18" target="12">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="572.5" y="-792" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="568" y="-792"/>
                            <mxPoint x="568" y="-294"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="16">
                    <mxGeometry x="-0.0705" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="是否获得控制权？" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="319.5" y="-832" width="173" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>