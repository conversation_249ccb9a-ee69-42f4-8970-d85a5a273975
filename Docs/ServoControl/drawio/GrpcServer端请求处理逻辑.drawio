<mxfile host="65bd71144e">
    <diagram name="MicroCouch启动流程" id="microcouch-startup-flow">
        <mxGraphModel dx="866" dy="3119" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="8" value="&lt;b&gt;GEAR_RATIO = 1000count/mm)&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="3553" y="-2093" width="197" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="982" y="1" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="178" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="110" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="843" y="-148" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="773" y="-817"/>
                            <mxPoint x="773" y="-93"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="180" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="178" vertex="1" connectable="0">
                    <mxGeometry x="-0.0998" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="222" value="是" style="edgeStyle=none;html=1;" parent="1" source="44" target="221" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="能否开始动床？&lt;div&gt;（判断HostType是否匹配）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="916.75" y="-857" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="" style="edgeStyle=none;html=1;" parent="1" source="46" target="44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="Start&lt;div&gt;Move&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="997" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="213" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="46" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2349.5" y="-1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="214" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2349.5" y="-1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="215" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="54" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2349.5" y="-1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="216" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="55" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2349.5" y="-1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="217" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="57" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2661" y="-1425.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="218" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2661" y="-1425.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="219" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="58" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2661" y="-1425.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="220" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="51" target="148" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2661" y="-1425.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="&lt;font style=&quot;font-size: 35px;&quot;&gt;Grpc服务器开始处理请求&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#60a917;strokeColor=#2D7600;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="2118" y="-1576" width="463" height="301" as="geometry"/>
                </mxCell>
                <mxCell id="120" value="" style="edgeStyle=none;html=1;" parent="1" source="53" target="85" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="Stopt&lt;div&gt;Move&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="1590.25" y="-989" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="183" value="" style="edgeStyle=none;html=1;" parent="1" source="54" target="140" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="Gain&lt;div&gt;Control&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="1975" y="-989" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="157" value="" style="edgeStyle=none;html=1;" parent="1" source="55" target="144" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="55" value="Release&lt;div&gt;Control&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="2341" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="169" value="" style="edgeStyle=none;html=1;" parent="1" source="56" target="152" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="Get&lt;div&gt;PostId&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="2855" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="166" value="" style="edgeStyle=none;html=1;" parent="1" source="57" target="149" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="Get&lt;div&gt;SystemStatus&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="2617" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="201" value="" style="edgeStyle=none;html=1;" parent="1" source="58" target="189" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="Get&lt;div&gt;TriggerInfo&lt;/div&gt;&lt;div&gt;（双向流）&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="3195" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="edgeStyle=none;html=1;" parent="1" source="60" target="61" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="根据输入参数开始对应模式的运动" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="975" y="-618" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="" style="edgeStyle=none;html=1;" parent="1" source="61" target="62" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="66" vertex="1" connectable="0">
                    <mxGeometry x="-0.1996" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="109" value="" style="edgeStyle=none;html=1;" parent="1" source="61" target="108" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="117" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="109" vertex="1" connectable="0">
                    <mxGeometry x="0.0033" y="-2" relative="1" as="geometry">
                        <mxPoint x="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" value="是否接收到StopMove请求？&lt;div&gt;（通过检查StopFlag）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="935" y="-519" width="204" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="93" value="" style="edgeStyle=none;html=1;" parent="1" source="62" target="92" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="102" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="93" vertex="1" connectable="0">
                    <mxGeometry x="-0.1793" y="-1" relative="1" as="geometry">
                        <mxPoint y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="132" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="62" target="110" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1265" y="-129" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1210" y="-343"/>
                            <mxPoint x="1210" y="-93"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="133" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="132" vertex="1" connectable="0">
                    <mxGeometry x="-0.2745" y="3" relative="1" as="geometry">
                        <mxPoint x="-4" y="23" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="是否完成运动？" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=default;" parent="1" vertex="1">
                    <mxGeometry x="935" y="-383" width="204" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="121" value="" style="edgeStyle=none;html=1;" parent="1" source="85" target="101" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="138" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="121" vertex="1" connectable="0">
                    <mxGeometry x="-0.0833" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="125" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="85" target="122" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1758" y="-545" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1773" y="-813"/>
                            <mxPoint x="1773" y="-546"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="139" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="125" vertex="1" connectable="0">
                    <mxGeometry x="-0.249" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="85" value="&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;能否停止移床？&lt;/span&gt;&lt;div&gt;（判断HostType是否匹配）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="1510" y="-853" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="135" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="92" target="61" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="782" y="-457" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="876" y="-226"/>
                            <mxPoint x="876" y="-479"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="92" value="等待500ms" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;arcSize=0;" parent="1" vertex="1">
                    <mxGeometry x="977" y="-256" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="123" value="" style="edgeStyle=none;html=1;" parent="1" source="101" target="122" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="101" value="设定StopFlag" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1570.25" y="-701" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="130" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="108" target="110" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1290" y="-82" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1300" y="-93"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="108" value="提前结束当前运动" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1240" y="-509" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="118" value="" style="edgeStyle=none;html=1;" parent="1" source="110" target="43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="110" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;arcSize=0;" parent="1" vertex="1">
                    <mxGeometry x="977" y="-123" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="119" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="1575.25" y="-449" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="124" value="" style="edgeStyle=none;html=1;" parent="1" source="122" target="119" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="122" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1570.25" y="-576" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="182" value="" style="edgeStyle=none;html=1;" parent="1" source="140" target="141" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="184" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="182" vertex="1" connectable="0">
                    <mxGeometry x="0.0046" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="187" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="140" target="142" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1817.75" y="-817" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1885" y="-817"/>
                            <mxPoint x="1885" y="-543"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="188" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="187" vertex="1" connectable="0">
                    <mxGeometry x="-0.0907" y="4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="140" value="能否获取控制权？&lt;div&gt;（判断HostType是否为None）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="1897.75" y="-857" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="185" value="" style="edgeStyle=none;html=1;" parent="1" source="141" target="142" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="141" value="将HostType设置为对应类型" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1958" y="-707" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="186" value="" style="edgeStyle=none;html=1;" parent="1" source="142" target="143" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="142" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="1958" y="-573" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="143" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="1963" y="-440" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="158" value="" style="edgeStyle=none;html=1;" parent="1" source="144" target="145" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="165" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="158" vertex="1" connectable="0">
                    <mxGeometry x="-0.1055" y="-1" relative="1" as="geometry">
                        <mxPoint y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="161" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="144" target="146" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2213" y="-508" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="2237" y="-813"/>
                            <mxPoint x="2237" y="-541"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="162" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="161" vertex="1" connectable="0">
                    <mxGeometry x="-0.179" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="144" value="能否释放控制权？&lt;div&gt;（判断HostType是否匹配）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="2260.75" y="-853" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="159" value="" style="edgeStyle=none;html=1;" parent="1" source="145" target="146" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="145" value="将HostType设置为对应None" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2320" y="-701" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="160" value="" style="edgeStyle=none;html=1;" parent="1" source="146" target="147" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="146" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2321" y="-571" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="147" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="2326" y="-449" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="175" value="" style="edgeStyle=none;html=1;" parent="1" source="148" target="172" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="148" value="HeartBeat&lt;div&gt;Check&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" parent="1" vertex="1">
                    <mxGeometry x="3527" y="-995" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="167" value="" style="edgeStyle=none;html=1;" parent="1" source="149" target="150" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="149" value="根据输入参数获取驱动器系统状态" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2597" y="-808" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="168" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="150" target="151" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2657" y="-504" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="150" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2597" y="-644" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="151" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="2602" y="-449" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="170" value="" style="edgeStyle=none;html=1;" parent="1" source="152" target="153" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="152" value="根据输入参数获取驱动器PostId" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2835" y="-811" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="171" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="153" target="154" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="2895" y="-517" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="153" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="2835" y="-657" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="154" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="2840" y="-452" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="176" value="" style="edgeStyle=none;html=1;" parent="1" source="172" target="173" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="172" value="将对应HostType的网络连接标志位置为True" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="3507" y="-821" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="177" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="173" target="174" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="3567" y="-537" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="173" value="返回请求结果" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="3507" y="-650" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="174" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="3512" y="-463" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="194" value="" style="edgeStyle=none;html=1;" parent="1" source="189" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="195" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="194" vertex="1" connectable="0">
                    <mxGeometry x="-0.3461" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="204" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="189" target="202" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="3020" y="-535" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="3056" y="-813"/>
                            <mxPoint x="3056" y="-467"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="205" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="204" vertex="1" connectable="0">
                    <mxGeometry x="-0.1409" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="189" value="是否接收到结束服务请求？&lt;div&gt;（判断是否收到EOF）&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="3114.75" y="-853" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="210" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="190" target="189" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="3375" y="-571" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="3413" y="-562"/>
                            <mxPoint x="3413" y="-813"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="190" value="流式返回TriggerInfo" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="3175" y="-592" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="196" value="" style="edgeStyle=none;html=1;" parent="1" source="192" target="190" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="197" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="196" vertex="1" connectable="0">
                    <mxGeometry x="-0.3712" y="2" relative="1" as="geometry">
                        <mxPoint x="-2" y="-4" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="206" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="192" target="189" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="3435.25" y="-690" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="3413" y="-690"/>
                            <mxPoint x="3413" y="-813"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="212" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="206" vertex="1" connectable="0">
                    <mxGeometry x="-0.846" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="192" value="驱动器上传的TriggerInfo是否发生变化？" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="3114.75" y="-730" width="240.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="209" value="" style="edgeStyle=none;html=1;" parent="1" source="199" target="203" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="199" value="返回 EndSream" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="3175" y="-393" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="208" value="" style="edgeStyle=none;html=1;" parent="1" source="202" target="199" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="202" value="最后一次返回TriggerInfo" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="3175" y="-497" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="203" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="3180" y="-273" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="223" value="否" style="edgeStyle=none;html=1;" parent="1" source="221" target="60" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="224" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="221" target="110" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="849" y="-178" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="819" y="-697"/>
                            <mxPoint x="819" y="-93"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="225" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="224" vertex="1" connectable="0">
                    <mxGeometry x="-0.1747" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="221" value="根据输入的运动参数判断是否会产生运动干涉？" style="rhombus;whiteSpace=wrap;html=1;fontStyle=1;fillColor=#fa6800;fontColor=#000000;strokeColor=#C73500;" parent="1" vertex="1">
                    <mxGeometry x="914.75" y="-737" width="240.5" height="80" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>