<mxfile host="65bd71144e">
    <diagram id="-qr2hSE-LJUAoiBMW91U" name="第 1 页">
        <mxGraphModel dx="738" dy="903" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="21">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="根据配置文件参数来添加子节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;strokeWidth=2;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="568" y="399" width="120" height="68" as="geometry"/>
                </mxCell>
                <mxCell id="4" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="628" y="284" as="sourcePoint"/>
                        <mxPoint x="628" y="324" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="6" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="ServoManger启动" style="ellipse;whiteSpace=wrap;html=1;fillColor=#60a917;strokeColor=#2D7600;fontColor=#ffffff;" vertex="1" parent="1">
                    <mxGeometry x="573" y="283" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="切换NMT状态至Pre-Operational" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="283" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="10" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="配置TPDO" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="391" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="12" target="14">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="配置IO" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="495" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="14" target="23">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="清除驱动器故障" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="602" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="16" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="开始初始化子节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#60a917;strokeColor=#2D7600;fontColor=#ffffff;" vertex="1" parent="1">
                    <mxGeometry x="205" y="153" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="18" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="回零操作" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="827" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" vertex="1" parent="1">
                    <mxGeometry x="205" y="929" width="110" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="21" target="25">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="分别初始化各子节点" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="568" y="520" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="23" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="切换NMT状态至Operational" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="200" y="713" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="25" target="26">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="开启状态监控线程" style="whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#1565C0;fontColor=#FFFFFF;rounded=1;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="568" y="645" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="结束启动" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e51400;strokeColor=#B20000;fontColor=#ffffff;" vertex="1" parent="1">
                    <mxGeometry x="573" y="770" width="110" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>