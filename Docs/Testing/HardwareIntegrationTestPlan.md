# 驱动器控制系统硬件集成测试方案

## 1. 概述

### 1.1 测试目标
本测试方案旨在验证驱动器控制系统在真实硬件环境中的功能完整性、性能指标和安全可靠性。测试覆盖ServoControlManager、gRPC服务接口和CANopen通信的所有关键功能。

### 1.2 测试范围
- **功能测试**：床体控制、权限管理、心跳检测、异常处理
- **性能测试**：响应时间、并发处理、长期稳定性
- **安全测试**：限位保护、紧急停止、故障恢复
- **集成测试**：多客户端协同、系统级功能验证

### 1.3 系统架构概述
```
┌─────────────────┐    gRPC     ┌──────────────────┐    CANopen    ┌─────────────┐
│   客户端应用    │ ◄────────► │   GrpcServer     │ ◄──────────► │  驱动器硬件  │
│ (CT/PET/SPECT) │             │                  │               │             │
└─────────────────┘             │ ServoControl     │               │ 一级床/二级床│
                                │ Manager          │               │             │
                                └──────────────────┘               └─────────────┘
```

## 2. 测试环境配置

### 2.1 硬件设备清单

#### 2.1.1 核心设备
| 设备名称 | 型号/规格 | 数量 | 节点ID | 功能描述 |
|---------|----------|------|--------|----------|
| 一级床驱动器 | Copley伺服驱动器 | 1 | 0x06 | 一级床位置/速度控制 |
| 二级床驱动器 | Copley伺服驱动器 | 1 | 0x0A | 二级床位置/速度控制 |
| 床体机械结构 | 医用床体 | 1 | - | 承载和运动执行 |
| CAN总线适配器 | USB-CAN/PCIe-CAN | 1 | - | CANopen通信接口 |

#### 2.1.2 辅助设备
| 设备名称 | 规格 | 数量 | 用途 |
|---------|------|------|------|
| 限位开关 | 常闭型 | 4 | 正负限位保护 |
| 编码器 | 2048线/圈 | 2 | 位置反馈 |
| 紧急停止按钮 | 24V | 2 | 安全保护 |
| 电源模块 | 24V/48V | 1 | 系统供电 |
| 示波器 | 100MHz | 1 | 信号调试 |
| 万用表 | 数字式 | 1 | 电气测量 |

### 2.2 网络拓扑配置

#### 2.2.1 CANopen网络参数
```
网络配置：
- 波特率：1Mbps
- 主站节点ID：0x01
- 一级床节点ID：0x06
- 二级床节点ID：0x0A
- 心跳周期：1000ms
- SYNC周期：500ms
```

#### 2.2.2 网络拓扑图
```
    ┌─────────────┐
    │   主站PC    │
    │  (Node 0x01)│
    └──────┬──────┘
           │ CAN-H/CAN-L
    ┌──────┴──────┐
    │  CAN总线    │
    │  120Ω终端   │
    └──┬────────┬─┘
       │        │
┌──────┴──┐  ┌──┴──────┐
│一级床   │  │二级床   │
│Node 0x06│  │Node 0x0A│
└─────────┘  └─────────┘
```

### 2.3 软件环境配置

#### 2.3.1 操作系统要求
- **操作系统**：Linux (Ubuntu 20.04+ 或 CentOS 8+)
- **内核版本**：5.4+
- **实时性**：建议使用RT内核

#### 2.3.2 依赖库安装
```bash
# 安装基础依赖
sudo apt-get update
sudo apt-get install build-essential cmake git

# 安装CANopen相关
sudo apt-get install can-utils libsocketcan-dev

# 安装gRPC依赖
sudo apt-get install libgrpc++-dev libprotobuf-dev protobuf-compiler-grpc

# 安装测试工具
sudo apt-get install valgrind gdb strace
```

#### 2.3.3 CAN接口配置
```bash
# 配置CAN接口
sudo modprobe can
sudo modprobe can_raw
sudo modprobe socketcan
sudo ip link set can0 type can bitrate 1000000
sudo ip link set up can0

# 验证CAN接口
candump can0 &
cansend can0 123#DEADBEEF
```

### 2.4 配置文件设置

#### 2.4.1 servo_config.json配置
```json
{
  "servo_motors": [
    {
      "node_id": 6,
      "name": "PrimaryBed",
      "type": "COPLEY_FIRST",
      "encoder_resolution": 2048,
      "position_mode": {
        "profile_velocity": 81920,
        "profile_acceleration": 209664,
        "profile_deceleration": 209664,
        "positive_limit": 1000000,
        "negative_limit": -1000000
      }
    },
    {
      "node_id": 10,
      "name": "SecondaryBed", 
      "type": "COPLEY_SECOND",
      "encoder_resolution": 2048,
      "position_mode": {
        "profile_velocity": 81920,
        "profile_acceleration": 209664,
        "profile_deceleration": 209664,
        "positive_limit": 800000,
        "negative_limit": -800000
      }
    }
  ],
  "heartbeat_config": {
    "timeout_ms": 30000,
    "check_interval_ms": 1000,
    "enable_auto_homing": true,
    "enable_heartbeat_monitor": true,
    "max_missed_heartbeats": 3
  }
}
```

## 3. 测试环境搭建步骤

### 3.1 硬件连接步骤
1. **电源连接**：连接24V/48V电源到驱动器
2. **CAN总线连接**：按拓扑图连接CAN-H、CAN-L和GND
3. **编码器连接**：连接编码器信号线到驱动器
4. **限位开关连接**：连接正负限位开关到驱动器输入端
5. **紧急停止连接**：连接急停按钮到安全回路
6. **机械装配**：安装床体到导轨，确保运动顺畅

### 3.2 软件部署步骤
1. **编译系统**：
```bash
cd /path/to/CANopenNodePorting
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

2. **配置部署**：
```bash
# 复制配置文件
cp Configuration/servo_config.json /etc/bedmaster/
# 设置权限
sudo chmod 644 /etc/bedmaster/servo_config.json
```

3. **启动服务**：
```bash
# 启动主程序
sudo ./BedMasterApp --config=/etc/bedmaster/servo_config.json --can-interface=can0
```

### 3.3 初始化验证
1. **CAN通信验证**：
```bash
# 检查节点在线状态
candump can0 | grep "70[6A]"  # 心跳消息
```

2. **驱动器状态检查**：
```bash
# 检查驱动器状态字
cansend can0 60C#4041600000000000  # 读取状态字
```

3. **gRPC服务验证**：
```bash
# 测试gRPC连接
grpc_cli call localhost:50051 motor_control.BedMasterAppService.GetSystemStatusInfo "contextUID:'test'"
```

## 4. 功能测试用例

### 4.1 基础通信测试

#### 4.1.1 CANopen节点发现测试
**测试目标**：验证系统能够正确发现和初始化所有CANopen节点

**测试步骤**：
1. 启动系统，监控CAN总线
2. 检查节点心跳消息（700+NodeID）
3. 验证节点状态转换到Operational

**预期结果**：
- 节点0x06和0x0A心跳正常（1Hz频率）
- 节点状态为Operational
- 无通信错误

**通过标准**：
```bash
# 心跳检查命令
candump can0 | grep -E "706|70A" | head -10
# 预期输出：每秒一次心跳消息
```

#### 4.1.2 gRPC接口连通性测试
**测试目标**：验证所有gRPC接口的基本连通性

**测试步骤**：
1. 逐一调用所有gRPC接口
2. 检查返回状态码
3. 验证错误处理机制

**测试接口列表**：
- GainControl
- ReleaseControl  
- StartMove
- StopMove
- GetSystemStatusInfo
- GetTriggerInfo
- HeartBeatCheck
- GetPostId

**预期结果**：所有接口返回正确的状态码和响应消息

### 4.2 床体控制功能测试

#### 4.2.1 位置控制测试
**测试目标**：验证床体精确位置控制功能

**测试用例TC-001：一级床绝对位置控制**
```json
{
  "testCase": "TC-001",
  "description": "一级床绝对位置控制",
  "steps": [
    {
      "step": 1,
      "action": "获取控制权",
      "grpcCall": "GainControl",
      "parameters": {"contextUID": "TC-001", "hostType": "CT"}
    },
    {
      "step": 2, 
      "action": "移动到位置100mm",
      "grpcCall": "StartMove",
      "parameters": {
        "contextUID": "TC-001",
        "bedType": "Primary",
        "motionMode": "PositionMode",
        "targetMotionInfo": {"position": 100.0, "velocity": 50.0}
      }
    },
    {
      "step": 3,
      "action": "等待运动完成",
      "timeout": "30s"
    },
    {
      "step": 4,
      "action": "验证位置精度",
      "tolerance": "±0.1mm"
    }
  ],
  "expectedResult": "床体移动到目标位置，精度在±0.1mm内",
  "passCondition": "实际位置与目标位置差值 < 0.1mm"
}
```

**测试用例TC-002：二级床相对位置控制**
**测试用例TC-003：双床顺序控制（Both模式）**

#### 4.2.2 速度控制测试
**测试用例TC-010：恒速运动控制**
**测试用例TC-011：变速运动控制**

#### 4.2.3 回零功能测试  
**测试用例TC-020：一级床回零**
**测试用例TC-021：二级床回零**
**测试用例TC-022：双床回零**

### 4.3 权限管理测试

#### 4.3.1 单客户端权限测试
**测试用例TC-030：权限获取和释放**

#### 4.3.2 多客户端权限冲突测试
**测试用例TC-040：权限冲突处理**
```json
{
  "testCase": "TC-040",
  "description": "多客户端权限冲突测试",
  "steps": [
    {
      "step": 1,
      "action": "客户端A获取控制权",
      "client": "CT",
      "expectedResult": "成功获取控制权"
    },
    {
      "step": 2,
      "action": "客户端B尝试获取控制权", 
      "client": "PET",
      "expectedResult": "权限被拒绝"
    },
    {
      "step": 3,
      "action": "客户端B尝试控制床体",
      "client": "PET", 
      "expectedResult": "操作被拒绝"
    },
    {
      "step": 4,
      "action": "客户端A释放控制权",
      "client": "CT",
      "expectedResult": "成功释放控制权"
    },
    {
      "step": 5,
      "action": "客户端B获取控制权",
      "client": "PET",
      "expectedResult": "成功获取控制权"
    }
  ]
}
```

### 4.4 心跳检测测试

#### 4.4.1 正常心跳测试
**测试用例TC-050：心跳维持测试**

#### 4.4.2 心跳超时测试
**测试用例TC-060：心跳超时自动回零**
```json
{
  "testCase": "TC-060", 
  "description": "心跳超时自动回零测试",
  "precondition": "客户端已获取控制权并移动床体到非零位置",
  "steps": [
    {
      "step": 1,
      "action": "停止发送心跳",
      "duration": "35s"
    },
    {
      "step": 2,
      "action": "监控系统行为",
      "expectedBehavior": [
        "检测到心跳超时",
        "自动执行回零操作", 
        "释放控制权"
      ]
    },
    {
      "step": 3,
      "action": "验证最终状态",
      "expectedResult": "床体回到零位，控制权为NONE"
    }
  ],
  "timeout": "120s",
  "safetyNote": "确保回零路径无障碍物"
}
```

## 5. 异常处理和安全测试

### 5.1 限位保护测试
**测试用例TC-070：正限位保护**
**测试用例TC-071：负限位保护**

### 5.2 紧急停止测试
**测试用例TC-080：软件紧急停止**
**测试用例TC-081：硬件紧急停止**

### 5.3 故障恢复测试
**测试用例TC-090：通信中断恢复**
**测试用例TC-091：驱动器故障恢复**

## 6. 性能测试

### 6.1 响应时间测试

#### 6.1.1 gRPC接口响应时间
**测试目标**：验证gRPC接口响应时间满足要求（<100ms）

**测试方法**：
```bash
# 使用grpc_cli测试响应时间
time grpc_cli call localhost:50051 motor_control.BedMasterAppService.GetSystemStatusInfo "contextUID:'perf-test'"
```

**性能指标**：
- 平均响应时间：< 50ms
- 95%响应时间：< 100ms  
- 99%响应时间：< 200ms

#### 6.1.2 运动控制响应时间
**测试目标**：验证运动控制指令的执行延迟

**测试方法**：
1. 发送位置控制指令
2. 监控实际运动开始时间
3. 计算指令延迟

**性能指标**：
- 指令处理延迟：< 10ms
- 运动启动延迟：< 50ms

### 6.2 并发处理测试

#### 6.2.1 多客户端并发测试
**测试目标**：验证系统处理多客户端并发请求的能力

**测试场景**：
- 10个客户端同时请求状态信息
- 5个客户端同时尝试获取控制权
- 混合读写操作并发测试

### 6.3 长期稳定性测试

#### 6.3.1 24小时连续运行测试
**测试目标**：验证系统长期稳定运行能力

**测试内容**：
- 连续24小时运行
- 每分钟执行一次床体控制操作
- 监控内存泄漏和性能衰减
- 记录所有异常和错误

**监控指标**：
- CPU使用率
- 内存使用率
- 网络延迟
- 错误率

## 7. 测试执行计划

### 7.1 测试阶段划分

#### 阶段1：环境搭建和基础验证（1天）
- 硬件连接和配置
- 软件部署和初始化
- 基础通信测试

#### 阶段2：功能测试（3天）
- 床体控制功能测试
- 权限管理测试
- 心跳检测测试

#### 阶段3：异常和安全测试（2天）
- 限位保护测试
- 紧急停止测试
- 故障恢复测试

#### 阶段4：性能测试（2天）
- 响应时间测试
- 并发处理测试
- 稳定性测试

#### 阶段5：集成验证（1天）
- 端到端测试
- 用户场景测试
- 测试报告整理

### 7.2 测试数据记录格式

#### 7.2.1 测试日志格式
```
[时间戳] [线程ID] [日志级别] [模块名] [测试用例ID] [消息内容]
示例：
2024-07-18 14:30:25.123 [12345] [INFO] [ServoControlManager] [TC-001] 开始执行一级床位置控制测试
2024-07-18 14:30:25.145 [12345] [DEBUG] [CANopenMaster] [TC-001] 发送位置控制指令：目标位置=100000
2024-07-18 14:30:27.234 [12345] [INFO] [ServoControlManager] [TC-001] 位置控制完成：实际位置=99998，误差=-2
```

#### 7.2.2 性能数据记录
```json
{
  "testCase": "TC-001",
  "timestamp": "2024-07-18T14:30:25.123Z",
  "metrics": {
    "responseTime": 45.2,
    "accuracy": 0.002,
    "cpuUsage": 12.5,
    "memoryUsage": 256.7
  },
  "result": "PASS"
}
```

### 7.3 通过/失败判定标准

#### 7.3.1 功能测试判定标准
- **PASS**：功能正常，满足所有预期结果
- **FAIL**：功能异常，不满足预期结果
- **BLOCKED**：测试无法执行（环境问题等）

#### 7.3.2 性能测试判定标准
- **PASS**：所有性能指标满足要求
- **WARNING**：部分指标接近阈值但仍在可接受范围
- **FAIL**：关键性能指标超出阈值

#### 7.3.3 安全测试判定标准
- **PASS**：安全机制正常工作，无安全风险
- **FAIL**：安全机制失效，存在安全风险

## 8. 风险评估和安全措施

### 8.1 硬件安全风险

#### 8.1.1 机械碰撞风险
**风险描述**：床体运动过程中可能发生碰撞
**风险等级**：高
**预防措施**：
- 安装可靠的限位开关
- 设置软件限位保护
- 运动前检查路径是否畅通
- 设置合理的运动速度和加速度

#### 8.1.2 电气安全风险
**风险描述**：电气故障可能导致设备损坏或人员伤害
**风险等级**：中
**预防措施**：
- 使用隔离电源
- 安装过流保护装置
- 定期检查电气连接
- 遵循电气安全规范

### 8.2 软件安全风险

#### 8.2.1 控制权冲突风险
**风险描述**：多客户端同时控制可能导致不可预期的行为
**风险等级**：中
**预防措施**：
- 严格的权限管理机制
- 心跳检测和超时保护
- 操作日志记录和审计

#### 8.2.2 通信中断风险
**风险描述**：CANopen通信中断可能导致控制失效
**风险等级**：高
**预防措施**：
- 通信状态监控
- 自动重连机制
- 通信中断时的安全停止

### 8.3 测试过程风险

#### 8.3.1 设备损坏风险
**风险描述**：测试过程中可能损坏硬件设备
**风险等级**：中
**预防措施**：
- 测试前充分验证测试用例
- 使用保护性参数设置
- 准备备用设备
- 购买设备保险

#### 8.3.2 数据丢失风险
**风险描述**：测试数据可能丢失或损坏
**风险等级**：低
**预防措施**：
- 定期备份测试数据
- 使用版本控制系统
- 多地点数据存储

### 8.4 应急处理预案

#### 8.4.1 紧急停止程序
1. **立即停止**：按下硬件急停按钮
2. **软件停止**：发送StopMove指令
3. **断电保护**：必要时切断主电源
4. **现场保护**：疏散人员，保护现场

#### 8.4.2 故障处理流程
1. **故障识别**：确定故障类型和影响范围
2. **安全措施**：确保人员和设备安全
3. **故障记录**：详细记录故障现象和处理过程
4. **故障分析**：分析故障原因和改进措施
5. **恢复测试**：修复后重新验证功能

#### 8.4.3 联系方式
- **技术支持**：[技术负责人联系方式]
- **安全负责人**：[安全负责人联系方式]
- **紧急联系人**：[紧急联系人信息]

## 9. 测试工具和脚本

### 9.1 自动化测试工具

#### 9.1.1 gRPC客户端测试工具
```cpp
// GrpcTestClient.hpp
class GrpcTestClient {
public:
    bool connectToServer(const std::string& address);
    bool testGainControl(const std::string& contextUID, motor_control::HostType hostType);
    bool testStartMove(const motor_control::StartMoveDescription& request);
    bool testStopMove(const std::string& contextUID);
    // ... 其他测试方法
private:
    std::unique_ptr<motor_control::BedMasterAppService::Stub> stub_;
};
```

#### 9.1.2 性能监控工具
```bash
#!/bin/bash
# performance_monitor.sh
# 监控系统性能指标

while true; do
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    echo "$timestamp,CPU:$cpu_usage%,MEM:$mem_usage%" >> performance.log
    sleep 1
done
```

### 9.2 测试数据分析工具

#### 9.2.1 日志分析脚本
```python
#!/usr/bin/env python3
# log_analyzer.py
import re
import json
from datetime import datetime

def analyze_test_logs(log_file):
    """分析测试日志，生成测试报告"""
    test_results = {}
    
    with open(log_file, 'r') as f:
        for line in f:
            # 解析日志行
            match = re.match(r'\[(.*?)\] \[(.*?)\] \[(.*?)\] \[(.*?)\] \[(.*?)\] (.*)', line)
            if match:
                timestamp, thread_id, level, module, test_case, message = match.groups()
                
                if test_case not in test_results:
                    test_results[test_case] = {
                        'start_time': timestamp,
                        'events': [],
                        'status': 'RUNNING'
                    }
                
                test_results[test_case]['events'].append({
                    'timestamp': timestamp,
                    'level': level,
                    'module': module,
                    'message': message
                })
    
    return test_results

def generate_report(test_results):
    """生成测试报告"""
    report = {
        'summary': {
            'total_tests': len(test_results),
            'passed': 0,
            'failed': 0,
            'blocked': 0
        },
        'details': test_results
    }
    
    for test_case, result in test_results.items():
        # 根据日志内容判断测试结果
        if any('ERROR' in event['level'] for event in result['events']):
            result['status'] = 'FAILED'
            report['summary']['failed'] += 1
        elif any('测试完成' in event['message'] for event in result['events']):
            result['status'] = 'PASSED'
            report['summary']['passed'] += 1
        else:
            result['status'] = 'BLOCKED'
            report['summary']['blocked'] += 1
    
    return report

if __name__ == '__main__':
    results = analyze_test_logs('test.log')
    report = generate_report(results)
    
    with open('test_report.json', 'w') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"测试报告已生成：test_report.json")
    print(f"总测试数：{report['summary']['total_tests']}")
    print(f"通过：{report['summary']['passed']}")
    print(f"失败：{report['summary']['failed']}")
    print(f"阻塞：{report['summary']['blocked']}")
```

## 10. 附录

### 10.1 配置文件模板
[详见第2.4节配置文件设置]

### 10.2 测试用例清单
[详见第4节功能测试用例]

### 10.3 性能基准值
| 指标 | 目标值 | 可接受值 | 不可接受值 |
|------|--------|----------|-----------|
| gRPC响应时间 | <50ms | <100ms | ≥100ms |
| 位置控制精度 | ±0.05mm | ±0.1mm | >±0.1mm |
| 运动启动延迟 | <20ms | <50ms | ≥50ms |
| 心跳检测延迟 | <1s | <2s | ≥2s |
| 系统可用性 | >99.9% | >99% | <99% |

### 10.4 故障代码对照表
| 错误代码 | 描述 | 处理方法 |
|----------|------|----------|
| 0x8000 | CANopen通信超时 | 检查CAN总线连接 |
| 0x8001 | 驱动器故障 | 检查驱动器状态和电源 |
| 0x8002 | 限位触发 | 检查机械位置和限位开关 |
| 0x8003 | 权限冲突 | 释放当前控制权后重试 |
| 0x8004 | 心跳超时 | 检查客户端连接状态 |

---

**文档版本**：v1.0  
**创建日期**：2024-07-18  
**最后更新**：2024-07-18  
**审核状态**：待审核
