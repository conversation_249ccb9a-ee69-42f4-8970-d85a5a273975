# gRPC 控床接口调用指南

## 系统架构概览

```
客户端 (CT/PET/SPECT) 
    ↓ gRPC 调用
控床服务器
    ↓ 权限验证 & 业务处理
控床系统
    ↓ 硬件控制
伺服电机
```

---

## 接口调用流程详解

### 1. **StartMove** - 开始移床

**功能**: 控制床位按指定模式运动到目标位置

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 控床服务器
    participant BedSystem as 控床系统

    Client->>Server: StartMove(运动模式, 床类型, 目标位置/速度)
    
    Server->>Server: 权限检查
    alt 无控制权限
        Server-->>Client: 错误: 权限被拒绝
    end
    
    Server->>BedSystem: 执行运动命令
    alt 单张床
        BedSystem->>BedSystem: 移动指定床位
    end
    
    alt 运动成功
        BedSystem-->>Server: 运动完成 + 当前状态
        Server-->>Client: 成功 + 当前位置信息
    else 运动失败
        BedSystem-->>Server: 运动失败
        Server-->>Client: 错误: 移动失败
    end
```

**关键要点**:
- **权限要求**: 必须先获取控制权才能移动床位
- **运动模式**: 支持位置模式、速度模式、回零模式
- **同步阻塞调用**: 接口会等待运动完成后返回结果

---

### 2. **StopMove** - 停止移床

**功能**: 立即停止所有床位运动

**调用流程**:

```mermaid
sequenceDiagram
    %%{init: {'theme':'base', 'themeVariables': {'background': '#ffffff'}}}%%
    participant Client as 客户端
    participant Server as 控床服务器
    participant BedSystem as 控床系统

    Client->>Server: StopMove()
    
    Server->>Server: 权限检查
    alt 无控制权限
        Server-->>Client: 错误: 权限被拒绝
    end
    
    Server->>BedSystem: 紧急停止命令
    Note over BedSystem: 同时停止所有床位运动
    
    BedSystem->>BedSystem: 停止一级床
    BedSystem->>BedSystem: 停止二级床
    
    alt 所有床停止成功
        BedSystem-->>Server: 停止成功
        Server-->>Client: 成功
    else 所有床停止失败
        BedSystem-->>Server: 停止失败
        Server-->>Client: 错误: 停止失败
    end
```

**关键要点**:
- **紧急停止**: 立即中断正在进行的运动，包括正在执行的 StartMove 调用
- **并行停止**: 同时停止所有床位

---

### 3. **控制权管理** - GainControl / ReleaseControl

**功能**: 获取或释放床位控制权限

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 控床服务器
    participant PermissionMgr as 权限管理

    Note over Client,PermissionMgr: 获取控制权 (GainControl)
    Client->>Server: GainControl()
    Server->>PermissionMgr: 检查当前控制权状态
    
    alt 当前无人控制
        PermissionMgr->>PermissionMgr: 分配控制权给客户端
        PermissionMgr-->>Server: 成功
        Server-->>Client: 成功: 已获取控制权
    else 客户端已有控制权
        PermissionMgr-->>Server: 成功 (重复获取)
        Server-->>Client: 成功: 控制权已存在
    else 其他客户端占用
        PermissionMgr-->>Server: 失败
        Server-->>Client: 错误: 设备被其他主机控制
    end
    
    Note over Client,PermissionMgr: 释放控制权 (ReleaseControl)
    Client->>Server: ReleaseControl()
    Server->>PermissionMgr: 验证控制权归属
    
    alt 客户端拥有控制权
        PermissionMgr->>PermissionMgr: 释放控制权
        PermissionMgr-->>Server: 成功
        Server-->>Client: 成功: 控制权已释放
    else 客户端无控制权
        PermissionMgr-->>Server: 失败
        Server-->>Client: 错误: 无权限释放
    end
```

**关键要点**:
- **互斥控制**: 同一时间只能有一个客户端拥有控制权
- **客户端类型**: 支持 CT、PET、SPECT 三种客户端类型
- **重复操作**: 重复获取已有的控制权会返回成功

---

### 4. **HeartBeatCheck** - 心跳检测

**功能**: 维持客户端连接状态，防止异常断开导致的安全问题

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 控床服务器
    participant HeartbeatMgr as 心跳管理
    participant BedSystem as 控床系统

    Note over Client,BedSystem: 正常心跳流程
    loop 定期发送 
        Client->>Server: HeartBeatCheck()
        Server->>HeartbeatMgr: 更新心跳时间戳
        
        alt 客户端有控制权
            HeartbeatMgr-->>Server: 心跳更新成功
            Server-->>Client: 成功
        else 客户端无控制权
            HeartbeatMgr-->>Server: 权限错误
            Server-->>Client: 错误: 权限被拒绝
        end
    end
    
    Note over Client,BedSystem: 心跳超时处理
    alt 客户端停止发送心跳
        HeartbeatMgr->>HeartbeatMgr: 检测到心跳超时
        HeartbeatMgr->>BedSystem: 紧急停止所有运动
        HeartbeatMgr->>BedSystem: 执行安全回零 (可配置)
        HeartbeatMgr->>HeartbeatMgr: 自动释放控制权
        Note over HeartbeatMgr: 客户端需重新获取控制权
    end
```

**关键要点**:
- **定期发送**: 客户端需要在指定时间内发送心跳
- **权限绑定**: 只有拥有控制权的客户端心跳才有效
- **自动保护**: 心跳超时后系统自动执行安全停止和回零（可选操作）
- **重新连接**: 超时后需要重新获取控制权

---

### 5. **GetSystemStatusInfo** - 获取系统状态

**功能**: 获取床位当前状态和控制权信息

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 控床服务器
    participant BedSystem as 控床系统

    Client->>Server: GetSystemStatusInfo()
    
    Server->>BedSystem: 获取床位状态
    BedSystem->>BedSystem: 读取一级床状态
    BedSystem->>BedSystem: 读取二级床状态
    BedSystem->>BedSystem: 获取当前控制权信息
    
    BedSystem-->>Server: 系统状态信息
    Note over Server: 包含：<br/>- 两张床的位置、速度<br/>- 运动状态 (就绪/运动中/急停/错误)<br/>- 当前控制权拥有者
    
    Server-->>Client: 系统状态详情
```

**返回信息**:
- **床位状态**: Ready(就绪) / Moving(运动中) / Estop(急停) / Error(错误)
- **位置信息**: 当前位置和速度
- **控制权**: 当前控制权拥有者 (CT/PET/SPECT/NONE)

---

### 6. **GetPostId** - 获取设备标识

**功能**: 获取指定床位的设备标识信息，用于设备识别和配置

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 床控服务器
    participant BedSystem as 床控系统

    Client->>Server: GetPostId(床类型)

    Server->>Server: 参数验证
    alt 无效床类型
        Server-->>Client: 错误: 无效参数
    end

    Server->>BedSystem: 获取设备标识信息
    BedSystem->>BedSystem: 读取指定床位的PostId

    alt 成功获取PostId
        BedSystem-->>Server: PostId信息 (VID, DID, HWID, RID)
        Server-->>Client: 成功 + PostId详情
    else 获取失败或无效床位
        BedSystem-->>Server: 获取失败
        Server-->>Client: 错误: 无效参数或获取失败
    end
```

**返回信息**:
- **VID**: 厂商ID (Vendor ID)
- **DID**: 设备ID (Device ID)
- **HWID**: 硬件ID (Hardware ID)
- **RID**: 区域ID (Region ID)

**关键要点**:
- **无权限要求**: 该接口不需要控制权即可调用
- **床位识别**: 可用于识别和区分不同的床位设备
- **配置用途**: 通常用于系统初始化和设备配置验证

---

### 7. **GetTriggerInfo** - 获取触发信息 (实时流)

**功能**: 实时接收床位触发信号，用于同步扫描等操作

**调用流程**:

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 控床服务器
    participant TriggerSystem as Trigger系统

    Client->>Server: GetTriggerInfo() [建立流连接]
    
    Server->>Server: 权限检查
    alt 无控制权限
        Server-->>Client: 错误: 权限被拒绝 [流断开]
    end
    
    Client->>Server: 发送启动请求
    
    Note over Client,TriggerSystem: 实时数据流
    loop 持续监听
        TriggerSystem->>TriggerSystem: 检测到新的Trigger信号
        TriggerSystem->>Server: 触发信息 (位置, 时间戳)
        Server->>Client: 推送触发数据
        
        alt 客户端发送 "STOP"
            Client->>Server: 停止请求
            Server->>Server: 结束流
        end
        
        alt 客户端断开连接
            Note over Server: 自动检测并清理资源
        end
    end
    
    Server-->>Client: 流结束
```

**关键要点**:
- **双向流**: 客户端可发送控制消息，服务器推送实时数据
- **权限要求**: 需要控制权才能接收触发信息
- **实时性**: 触发信号产生后立即推送给客户端
- **优雅断开**: 客户端可发送 "STOP" 消息主动结束流

---

## 典型使用场景

### 场景1: 系统初始化和设备识别

```
1. GetPostId(Primary)     // 获取一级床设备标识
2. GetPostId(Secondary)   // 获取二级床设备标识
3. 验证设备配置和兼容性
4. GetSystemStatusInfo()  // 检查初始状态
```

### 场景2: 基本床位控制

```
1. GainControl()          // 获取控制权
2. StartMove()            // 移动到指定位置
3. GetSystemStatusInfo()  // 检查状态
4. StopMove()             // 停止运动 (如需要)
5. ReleaseControl()       // 释放控制权
```

### 场景3: 带心跳的长期控制

```
1. GainControl()          // 获取控制权
2. 启动心跳线程: 每20秒调用 HeartBeatCheck()
3. 执行多次 StartMove() 操作
4. 停止心跳线程
5. ReleaseControl()       // 释放控制权
```

### 场景4: 实时触发监听

```
1. GainControl()          // 获取控制权
2. GetTriggerInfo()       // 建立流连接
3. 处理实时触发数据
4. 发送 "STOP" 或断开连接
5. ReleaseControl()       // 释放控制权
```

---

## 错误码说明

| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | - |
| 权限被拒绝 | 无控制权或权限不足 | 先调用 GainControl() |
| 无效参数 | 请求参数错误 | 检查参数范围和格式 |
| 硬件错误 | 设备故障 | 检查硬件连接和状态 |
| 超时 | 操作超时 | 检查目标参数是否合理 |
| 设备被占用 | 其他客户端控制中 | 等待或协调其他客户端释放 |

---

## 最佳实践

1. **控制权管理**: 使用完毕后及时释放控制权，避免长期占用
2. **心跳机制**: 长期控制时必须发送心跳，建议间隔 10-20 秒
3. **错误处理**: 始终检查返回的错误码，根据错误类型进行相应处理
4. **状态监控**: 定期调用 GetSystemStatusInfo() 监控系统状态
5. **优雅断开**: 程序退出前主动释放控制权和关闭流连接

---

## 接口参数详细说明

### StartMove 参数

| 参数 | 类型 | 说明 | 取值范围 |
|------|------|------|----------|
| contextUID | string | 上下文标识 | 任意字符串 |
| mode | MotionMode | 运动模式 | PositionMode/VelocityMode/HomingMode |
| bedType | BedType | 床类型 | Primary/Secondary/Both |
| position | float | 目标位置 (mm) | 根据床位配置 |
| velocity | float | 目标速度 (mm/s) | 根据床位配置 |

### GetPostId 参数

| 参数 | 类型 | 说明 | 取值范围 |
|------|------|------|----------|
| contextUID | string | 上下文标识 | 任意字符串 |
| bedType | BedType | 床类型 | Primary/Secondary |

### GetPostId 返回值

| 字段 | 类型 | 说明 |
|------|------|------|
| VID | uint32 | 厂商ID，用于识别设备制造商 |
| DID | uint32 | 设备ID，用于识别具体设备型号 |
| HWID | uint32 | 硬件ID，用于识别硬件版本 |
| RID | uint32 | 区域ID，用于识别设备所在区域 |

### 客户端类型识别

客户端类型通过 gRPC metadata 中的 "client-type" 字段识别：
- "CT" - CT 扫描仪
- "PET" - PET 扫描仪
- "SPECT" - SPECT 扫描仪

### 状态枚举说明

**BedMotionStatus (床位运动状态)**:
- Ready(0): 就绪状态，可以接受新的运动命令
- Moving(1): 运动中，正在执行运动命令
- Estop(2): 紧急停止状态
- Error(3): 错误状态，需要排查故障

**BedOwnerType (控制权类型)**:
- CT(0): CT 扫描仪控制
- PET(1): PET 扫描仪控制
- SPECT(2): SPECT 扫描仪控制
- NONE(3): 无控制权（可获取状态）
