# CANopen电机控制GUI客户端

这是一个基于Python的GUI客户端程序，用于与CANopen电机控制系统进行交互。通过gRPC协议与服务端通信，提供友好的图形用户界面来控制和监控电机。

## 功能特点

- 连接gRPC服务器
- 查看所有节点信息和状态
- 控制电机（使能/禁用）
- 设置操作模式
- 位置控制
- 速度控制
- 扭矩控制
- 回零操作
- 紧急停止
- 故障清除
- 状态监控和自动刷新

## 安装要求

- Python 3.6 或更高版本
- PyQt5
- gRPC

## 安装步骤

1. 克隆或下载此仓库到本地

2. 安装所需的依赖包：

```bash
pip install grpcio grpcio-tools protobuf PyQt5
```

3. 生成Proto文件的Python代码：

```bash
python generate_proto.py
```
  
此命令将根据proto/motor_control.proto文件生成以下两个文件：
- motor_control_pb2.py
- motor_control_pb2_grpc.py

## 使用方法

1. 启动程序：

```bash
python motor_control_gui.py
```

2. 在界面顶部输入gRPC服务器地址（默认为localhost:50051），然后点击"连接"按钮

3. 连接成功后，可以在各个标签页中执行不同的操作：

   - **节点状态**：查看所有已连接的节点信息
   - **电机控制**：选择一个节点，进行基本的电机控制（使能/禁用、设置模式等）
   - **位置控制**：执行位置控制指令
   - **速度控制**：执行速度控制指令
   - **扭矩控制**：执行扭矩控制指令
   - **回零操作**：执行回零指令

## 调试提示

- 如果连接服务器失败，请检查服务器地址是否正确，以及服务器是否已经启动
- 如果看不到节点信息，请点击"刷新节点信息"按钮
- 在执行任何控制操作前，请确保已经成功连接到服务器并选择了一个节点
- 紧急情况下可以使用"紧急停止"按钮立即停止电机

## 项目结构

- `motor_control_gui.py` - 主程序和GUI界面实现
- `generate_proto.py` - 用于生成Proto相关Python文件的脚本
- `proto/motor_control.proto` - gRPC服务和消息定义文件
- `motor_control_pb2.py` - 由proto文件生成的消息类（运行generate_proto.py后生成）
- `motor_control_pb2_grpc.py` - 由proto文件生成的服务类（运行generate_proto.py后生成）

## 问题反馈

如有任何问题或建议，请提交issue或联系管理员。


## 第三方依赖库 (Dependencies)

本项目依赖以下第三方库和工具，请确保在编译和运行前正确安装和配置这些依赖。

### C++核心依赖

| 库名称 | 功能简介 | 版本要求 | 官方下载地址 | 许可证 |
|--------|----------|----------|--------------|--------|
| **gRPC** | 高性能、开源的通用RPC框架，用于客户端-服务器通信 | >= 1.30.0 | [https://grpc.io/](https://grpc.io/) <br> [GitHub](https://github.com/grpc/grpc) | Apache 2.0 |
| **Protocol Buffers** | Google开发的数据序列化库，用于结构化数据的序列化 | >= 3.12.0 | [https://developers.google.com/protocol-buffers](https://developers.google.com/protocol-buffers) <br> [GitHub](https://github.com/protocolbuffers/protobuf) | BSD 3-Clause |
| **CANopenNode** | 免费开源的CANopen协议栈，用于CAN总线通信 | v4.x | [https://github.com/CANopenNode/CANopenNode](https://github.com/CANopenNode/CANopenNode) | Apache 2.0 |
| **Abseil** | Google开发的C++库集合，gRPC的依赖库 | >= 20210324.2 | [https://abseil.io/](https://abseil.io/) <br> [GitHub](https://github.com/abseil/abseil-cpp) | Apache 2.0 |

### 测试框架依赖

| 库名称 | 功能简介 | 版本要求 | 官方下载地址 | 许可证 |
|--------|----------|----------|--------------|--------|
| **Google Test** | Google开发的C++单元测试框架 | >= 1.10.0 | [https://github.com/google/googletest](https://github.com/google/googletest) | BSD 3-Clause |
| **Google Mock** | Google Test的Mock框架，用于创建模拟对象 | >= 1.10.0 | [https://github.com/google/googletest](https://github.com/google/googletest) | BSD 3-Clause |

### Python GUI工具依赖

| 库名称 | 功能简介 | 版本要求 | 官方下载地址 | 许可证 |
|--------|----------|----------|--------------|--------|
| **PyQt5** | Python的跨平台GUI工具包，用于构建图形用户界面 | >= 5.12.0 | [https://www.riverbankcomputing.com/software/pyqt/](https://www.riverbankcomputing.com/software/pyqt/) <br> [PyPI](https://pypi.org/project/PyQt5/) | GPL v3 / Commercial |
| **grpcio** | gRPC的Python实现，用于Python客户端通信 | >= 1.30.0 | [https://grpc.io/docs/languages/python/](https://grpc.io/docs/languages/python/) <br> [PyPI](https://pypi.org/project/grpcio/) | Apache 2.0 |
| **grpcio-tools** | gRPC Python工具，包含protoc编译器 | >= 1.30.0 | [PyPI](https://pypi.org/project/grpcio-tools/) | Apache 2.0 |
| **protobuf** | Protocol Buffers的Python绑定 | >= 3.12.0 | [PyPI](https://pypi.org/project/protobuf/) | BSD 3-Clause |

### 系统依赖

| 工具名称 | 功能简介 | 版本要求 | 官方下载地址 | 许可证 |
|----------|----------|----------|--------------|--------|
| **ARM交叉编译工具链** | 用于在x86平台编译ARM目标代码 | GCC >= 7.0 | [Poky SDK](https://www.yoctoproject.org/) | GPL v3 |
| **CMake** | 跨平台构建系统 | >= 3.10 | [https://cmake.org/](https://cmake.org/) | BSD 3-Clause |
| **Threads** | POSIX线程库，用于多线程编程 | 系统自带 | 系统自带 | LGPL |

### 安装说明

#### C++依赖安装
```bash
# Ubuntu/Debian系统
sudo apt-get update
sudo apt-get install -y cmake build-essential

# 安装gRPC和protobuf (推荐从源码编译以确保版本兼容性)
# 详细安装步骤请参考官方文档
```

#### Python依赖安装
```bash
# 安装Python依赖
pip install grpcio grpcio-tools protobuf PyQt5
```

#### 交叉编译环境
项目支持ARM平台交叉编译，需要安装相应的交叉编译工具链。具体路径配置请参考 `toolchain.cmake` 文件。

### 注意事项

1. **版本兼容性**：建议使用指定版本或更高版本，以确保API兼容性
2. **编译顺序**：某些库之间存在依赖关系，建议按照 protobuf → abseil → gRPC 的顺序编译
3. **交叉编译**：ARM平台编译时需要确保所有依赖库都使用相同的交叉编译工具链
4. **许可证合规**：使用GPL许可证的库时请注意许可证兼容性要求

# 修复windows与linux行尾结束符问题
dos2unix ./Create_Vitrtual_Can_Device.sh
