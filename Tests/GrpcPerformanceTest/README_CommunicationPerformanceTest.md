# BedMaster GRPC通信性能测试使用指南

## 概述

本测试方案专注于BedMaster项目GRPC接口的通信层面性能测试，基于现有的`GrpcServerTest.cpp`进行简化扩展，避免复杂框架，专注于三个核心测试目标。

## 测试目标

### 1. 单次接口交互耗时测试
- 测试所有8个GRPC接口的单次调用响应时间
- 重点关注P50、P95、P99延迟指标
- 测试不同并发客户端数量下的性能表现

### 2. 长时间通信稳定性测试
- 验证GRPC服务在长时间运行下的性能稳定性
- 监控延迟趋势、错误率变化
- 重点测试HeartBeatCheck接口的长期稳定性

### 3. 流式接口延时测试
- 专门测试GetTriggerInfo双向流接口的通信延时
- 测量流建立时间、消息传输延迟
- 验证多个并发流的性能表现

## 快速开始

### 编译和运行

```bash
# 方法1: 使用运行脚本 (推荐)
cd Tests
./run_communication_perf_test.sh

# 方法2: 手动编译运行
cd Tests
mkdir build_perf && cd build_perf
cp ../CMakeLists_Performance.txt ./CMakeLists.txt
cmake . -DCMAKE_BUILD_TYPE=Release
make grpc_communication_performance_test
./grpc_communication_performance_test
```

### 测试选项

```bash
# 快速测试 (仅单次接口延迟，约2分钟)
./run_communication_perf_test.sh --quick

# 完整测试 (默认，约10分钟)
./run_communication_perf_test.sh --full

# 长时间测试 (1小时)
./run_communication_perf_test.sh --long

# 自定义测试时长 (30分钟)
./run_communication_perf_test.sh -d 1800

# 清理重建
./run_communication_perf_test.sh --clean

# 详细输出
./run_communication_perf_test.sh -v
```

### 环境变量控制

```bash
# 设置长时间测试持续时间 (秒)
export PERF_TEST_DURATION=3600

# 设置单次测试请求数量
export PERF_TEST_REQUESTS=200

# 运行测试
./grpc_communication_performance_test
```

## 测试结果

### 输出文件

测试完成后会生成以下文件：

1. **performance_results.csv** - 详细的性能数据
2. **performance_summary.txt** - 摘要报告

### CSV数据格式

```csv
TestName,ConcurrentClients,TotalRequests,P50_ms,P95_ms,P99_ms,Avg_ms,Max_ms,Min_ms,StdDev_ms,ErrorCount,ErrorRate,Duration_s
StartMove_Latency,1,100,8.2,15.4,23.1,9.8,25.6,4.2,0.00,0.00,45.2
StartMove_Latency,5,500,12.5,28.3,45.2,15.7,52.1,8.9,0.00,0.00,89.1
...
```

### 摘要报告示例

```
=== BedMaster GRPC通信性能测试报告 ===
测试时间: 2024-01-15 14:30:00

== 单次接口延迟测试结果 ==
✓ StartMove: P95=28.3ms (目标<50ms) - 通过
✓ StopMove: P95=26.1ms (目标<50ms) - 通过  
✓ HeartBeatCheck: P95=18.7ms (目标<20ms) - 通过
⚠ GetSystemStatusInfo: P95=105.2ms (目标<100ms) - 轻微超标

== 长时间稳定性测试结果 ==
✓ HeartBeat长期稳定性: P95延迟增长4.2%

== 流式接口测试结果 ==
✓ 1streams: 流建立=85ms, 消息延迟P95=6.2ms
✓ 5streams: 流建立=92ms, 消息延迟P95=8.1ms
✓ 10streams: 流建立=98ms, 消息延迟P95=12.3ms

== 总体评估 ==
测试通过率: 92.3% (12/13)
✓ 通信性能优秀，满足设计要求
```

## 性能基准

### 接口延迟目标

| 接口 | P50目标 | P95目标 | P99目标 | 说明 |
|------|---------|---------|---------|------|
| StartMove/StopMove | <15ms | <50ms | <100ms | 实时控制要求 |
| HeartBeatCheck | <10ms | <20ms | <50ms | 高频调用 |
| GetSystemStatusInfo | <25ms | <100ms | <200ms | 状态查询 |
| GainControl/ReleaseControl | <20ms | <75ms | <150ms | 权限控制 |
| GetPostId | <15ms | <50ms | <100ms | 设备查询 |

### 稳定性标准

- **延迟稳定性**: 标准差 < 平均延迟的30%
- **长期稳定性**: 24小时测试中P95延迟增长 < 20%
- **连接稳定性**: 错误率 < 0.1%

### 流式接口标准

- **流建立时间** < 100ms
- **消息传输延迟** < 10ms (P95)
- **并发流支持** ≥ 10个流
- **流稳定性** > 99.9%

## 测试用例说明

### SingleInterfaceLatencyTest

测试所有8个GRPC接口在不同并发客户端数量下的延迟性能：

- 测试接口: StartMove, StopMove, HeartBeatCheck, GetSystemStatusInfo, GainControl, ReleaseControl, GetPostId
- 并发客户端: 1, 5, 10个
- 每个客户端请求数: 100次
- 关键指标: P50, P95, P99延迟，错误率

### LongTermStabilityTest

测试HeartBeatCheck接口的长时间稳定性：

- 默认测试时长: 1小时 (可通过环境变量调整)
- 采样间隔: 30秒
- 监控指标: P95延迟趋势，错误率变化
- 稳定性判断: P95延迟增长 < 20%

### StreamingLatencyTest

测试GetTriggerInfo流式接口的性能：

- 并发流数量: 1, 3, 5, 10个
- 每个流消息数: 50条
- 测试时长: 60秒
- 关键指标: 流建立时间，消息传输延迟

## 故障排查

### 常见问题

1. **编译失败**
   ```bash
   # 检查依赖
   cmake --version  # 需要 >= 3.16
   g++ --version    # 需要支持C++17
   
   # 检查gRPC库路径
   ls /home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/
   ```

2. **测试启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 50051
   
   # 检查Mock库
   ldd grpc_communication_performance_test | grep mock
   ```

3. **性能异常**
   ```bash
   # 检查系统负载
   htop
   
   # 检查网络延迟
   ping localhost
   
   # 检查磁盘I/O
   iostat -x 1
   ```

### 调试选项

```bash
# 启用详细日志
./grpc_communication_performance_test --gtest_verbose

# 运行特定测试
./grpc_communication_performance_test --gtest_filter="*SingleInterface*"

# 使用GDB调试
gdb ./grpc_communication_performance_test
```

## 扩展和定制

### 添加新的测试接口

在`callInterface`方法中添加新的接口调用逻辑：

```cpp
} else if (interface_name == "NewInterface") {
    // 添加新接口的调用逻辑
    motor_control::NewRequest request;
    motor_control::NewResponse response;
    // ... 设置请求参数
    grpc::Status status = stub->NewInterface(&context, request, &response);
    return status.ok() && response.errorcode() == 0;
}
```

### 调整性能目标

修改测试用例中的`p95_targets`映射：

```cpp
std::map<std::string, double> p95_targets = {
    {"StartMove", 30.0},  // 调整为30ms
    // ... 其他接口
};
```

### 自定义测试参数

通过环境变量控制测试行为：

```cpp
const char* requests_env = std::getenv("PERF_TEST_REQUESTS");
int request_count = requests_env ? std::atoi(requests_env) : 100;
```

## 注意事项

1. **测试环境**: 确保测试环境相对稳定，避免其他高负载程序影响结果
2. **Mock配置**: 测试使用Mock对象，专注于通信层性能，不涉及真实硬件
3. **结果解读**: P95延迟比平均延迟更能反映用户体验
4. **长时间测试**: 长时间测试可能需要较长时间完成，建议在空闲时段运行
5. **资源监控**: 测试过程中可以使用htop等工具监控系统资源使用情况

## 总结

这个简化的通信性能测试方案专注于GRPC接口的核心通信性能，避免了复杂的框架设计，基于现有代码结构，易于使用和维护。通过三个核心测试用例，可以全面评估BedMaster项目GRPC接口的通信性能表现。
