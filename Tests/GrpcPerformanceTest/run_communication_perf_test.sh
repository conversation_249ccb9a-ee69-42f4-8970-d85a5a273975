#!/bin/bash

# BedMaster GRPC通信性能测试运行脚本
# 简化版本，专注于核心功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BUILD_DIR="build_perf"
TEST_DURATION=300  # 默认5分钟
CLEAN_BUILD=false
VERBOSE=false

# 打印帮助信息
print_help() {
    echo "BedMaster GRPC通信性能测试运行脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -d, --duration SECONDS  设置长时间测试持续时间 (默认: 300秒)"
    echo "  -v, --verbose           启用详细输出"
    echo "  --clean                 清理构建并重新编译"
    echo "  --quick                 只运行快速测试 (单次接口延迟)"
    echo "  --full                  运行完整测试 (默认)"
    echo "  --long                  运行长时间测试 (1小时)"
    echo ""
    echo "示例:"
    echo "  $0                      # 运行默认完整测试"
    echo "  $0 --quick              # 只运行快速测试"
    echo "  $0 --long               # 运行1小时长时间测试"
    echo "  $0 -d 1800              # 运行30分钟测试"
    echo "  $0 --clean -v           # 清理重建并详细输出"
    echo ""
}

# 打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%H:%M:%S')] ${message}${NC}"
}

print_info() {
    print_message $BLUE "$1"
}

print_success() {
    print_message $GREEN "$1"
}

print_warning() {
    print_message $YELLOW "$1"
}

print_error() {
    print_message $RED "$1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    local tools=("cmake" "make" "g++")
    for tool in "${tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            print_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    print_success "依赖检查完成"
}

# 构建项目
build_project() {
    print_info "构建通信性能测试项目..."
    
    if $CLEAN_BUILD && [ -d "$BUILD_DIR" ]; then
        print_info "清理现有构建目录..."
        rm -rf "$BUILD_DIR"
    fi
    
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # 使用性能测试专用的CMakeLists.txt
    if [ -f "../CMakeLists_Performance.txt" ]; then
        cp "../CMakeLists_Performance.txt" "./CMakeLists.txt"
    else
        print_error "找不到 CMakeLists_Performance.txt 文件"
        exit 1
    fi
    
    # 配置CMake
    print_info "配置CMake..."
    cmake . -DCMAKE_BUILD_TYPE=Release
    
    # 编译
    print_info "编译项目..."
    make -j$(nproc) grpc_communication_performance_test
    
    cd ..
    print_success "构建完成"
}

# 运行性能测试
run_performance_test() {
    local test_type=$1
    
    print_info "开始运行GRPC通信性能测试..."
    print_info "测试类型: $test_type"
    
    cd "$BUILD_DIR"
    
    # 清理之前的结果文件
    rm -f performance_results.csv performance_summary.txt
    
    # 构建测试命令
    local test_cmd="./grpc_communication_performance_test"
    
    # 根据测试类型设置参数
    case $test_type in
        "quick")
            test_cmd="$test_cmd --gtest_filter=*SingleInterfaceLatency*"
            print_info "运行快速测试 (仅单次接口延迟测试)"
            ;;
        "full")
            export PERF_TEST_DURATION=$TEST_DURATION
            print_info "运行完整测试 (持续时间: ${TEST_DURATION}秒)"
            ;;
        "long")
            export PERF_TEST_DURATION=3600
            print_info "运行长时间测试 (持续时间: 1小时)"
            ;;
    esac
    
    # 添加详细输出
    if $VERBOSE; then
        test_cmd="$test_cmd --gtest_verbose"
    fi
    
    print_info "执行命令: $test_cmd"
    
    # 运行测试
    if $test_cmd; then
        print_success "性能测试完成"
        
        # 显示结果文件
        if [ -f "performance_results.csv" ]; then
            print_success "详细结果已保存到: performance_results.csv"
        fi
        
        if [ -f "performance_summary.txt" ]; then
            print_success "摘要报告已保存到: performance_summary.txt"
            echo ""
            print_info "摘要报告内容:"
            echo "----------------------------------------"
            cat performance_summary.txt
            echo "----------------------------------------"
        fi
        
    else
        print_error "性能测试失败"
        cd ..
        return 1
    fi
    
    cd ..
}

# 清理函数
cleanup() {
    print_info "清理测试环境..."
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 解析命令行参数
TEST_TYPE="full"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_help
            exit 0
            ;;
        -d|--duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --quick)
            TEST_TYPE="quick"
            shift
            ;;
        --full)
            TEST_TYPE="full"
            shift
            ;;
        --long)
            TEST_TYPE="long"
            shift
            ;;
        *)
            print_error "未知选项: $1"
            print_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    echo "BedMaster GRPC通信性能测试"
    echo "========================="
    echo ""
    
    check_dependencies
    build_project
    
    if run_performance_test "$TEST_TYPE"; then
        print_success "所有测试完成！"
        echo ""
        print_info "测试结果文件位置:"
        echo "  - $BUILD_DIR/performance_results.csv (详细数据)"
        echo "  - $BUILD_DIR/performance_summary.txt (摘要报告)"
    else
        print_error "测试失败！"
        exit 1
    fi
}

# 运行主函数
main
