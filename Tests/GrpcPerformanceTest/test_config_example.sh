#!/bin/bash

# BedMaster GRPC通信性能测试配置示例
# 这个文件展示了如何通过环境变量调整测试参数

echo "BedMaster GRPC通信性能测试配置示例"
echo "=================================="

# ===========================================
# 基础配置示例
# ===========================================

echo ""
echo "1. 快速测试配置 (适用于开发阶段快速验证)"
echo "----------------------------------------"
cat << 'EOF'
# 只运行单次接口延迟测试，每个接口50次请求
export PERF_TEST_REQUESTS=50
./run_communication_perf_test.sh --quick
EOF

echo ""
echo "2. 标准测试配置 (适用于日常性能验证)"
echo "----------------------------------------"
cat << 'EOF'
# 运行完整测试，长时间测试5分钟
export PERF_TEST_DURATION=300
export PERF_TEST_REQUESTS=100
./run_communication_perf_test.sh --full
EOF

echo ""
echo "3. 压力测试配置 (适用于性能极限测试)"
echo "----------------------------------------"
cat << 'EOF'
# 增加请求数量，延长测试时间
export PERF_TEST_DURATION=1800  # 30分钟
export PERF_TEST_REQUESTS=500   # 每个接口500次请求
./run_communication_perf_test.sh --full
EOF

echo ""
echo "4. 长时间稳定性测试配置 (适用于稳定性验证)"
echo "----------------------------------------"
cat << 'EOF'
# 24小时长时间测试
export PERF_TEST_DURATION=86400  # 24小时
./run_communication_perf_test.sh --long
EOF

# ===========================================
# 高级配置示例
# ===========================================

echo ""
echo "5. 自定义并发测试配置"
echo "----------------------------------------"
cat << 'EOF'
# 可以通过修改源码中的concurrent_clients数组来调整并发数
# 在GrpcCommunicationPerformanceTest.cpp中找到:
# const std::vector<int> concurrent_clients = {1, 5, 10};
# 修改为: const std::vector<int> concurrent_clients = {1, 5, 10, 20, 50};
EOF

echo ""
echo "6. 特定接口测试配置"
echo "----------------------------------------"
cat << 'EOF'
# 只测试特定接口 (通过gtest filter)
./grpc_communication_performance_test --gtest_filter="*SingleInterfaceLatency*"

# 只测试长时间稳定性
./grpc_communication_performance_test --gtest_filter="*LongTermStability*"

# 只测试流式接口
./grpc_communication_performance_test --gtest_filter="*StreamingLatency*"
EOF

echo ""
echo "7. 调试和详细输出配置"
echo "----------------------------------------"
cat << 'EOF'
# 启用详细输出
./run_communication_perf_test.sh -v

# 或者直接运行测试程序
./grpc_communication_performance_test --gtest_verbose
EOF

# ===========================================
# 性能目标调整示例
# ===========================================

echo ""
echo "8. 性能目标调整说明"
echo "----------------------------------------"
cat << 'EOF'
如需调整性能目标，请修改 GrpcCommunicationPerformanceTest.cpp 中的 p95_targets:

std::map<std::string, double> p95_targets = {
    {"StartMove", 50.0},        // 调整StartMove的P95目标为50ms
    {"StopMove", 50.0},         // 调整StopMove的P95目标为50ms
    {"HeartBeatCheck", 20.0},   // 调整HeartBeat的P95目标为20ms
    {"GetSystemStatusInfo", 100.0}, // 调整状态查询的P95目标为100ms
    {"GainControl", 75.0},      // 调整控制权获取的P95目标为75ms
    {"ReleaseControl", 75.0},   // 调整控制权释放的P95目标为75ms
    {"GetPostId", 50.0}         // 调整PostId查询的P95目标为50ms
};
EOF

# ===========================================
# 实际使用示例
# ===========================================

echo ""
echo "9. 实际使用示例"
echo "----------------------------------------"

echo ""
echo "示例1: 开发阶段快速验证"
echo "export PERF_TEST_REQUESTS=50"
echo "./run_communication_perf_test.sh --quick"

echo ""
echo "示例2: 集成测试阶段完整验证"
echo "export PERF_TEST_DURATION=600"
echo "export PERF_TEST_REQUESTS=200"
echo "./run_communication_perf_test.sh --full"

echo ""
echo "示例3: 发布前长时间稳定性测试"
echo "export PERF_TEST_DURATION=7200  # 2小时"
echo "./run_communication_perf_test.sh --long"

echo ""
echo "示例4: 性能回归测试"
echo "# 先运行基准测试并保存结果"
echo "./run_communication_perf_test.sh --full"
echo "cp build_perf/performance_results.csv baseline_results.csv"
echo ""
echo "# 代码修改后再次测试"
echo "./run_communication_perf_test.sh --full"
echo "# 然后对比两个CSV文件的结果"

# ===========================================
# 结果分析示例
# ===========================================

echo ""
echo "10. 结果分析示例"
echo "----------------------------------------"
cat << 'EOF'
# 查看摘要报告
cat build_perf/performance_summary.txt

# 分析CSV数据 (可以用Excel或其他工具)
# 关注以下关键指标:
# - P95_ms: 95%请求的响应时间
# - ErrorRate: 错误率
# - StdDev_ms: 延迟标准差 (稳定性指标)

# 使用命令行工具快速分析
echo "StartMove接口P95延迟统计:"
grep "StartMove_Latency" build_perf/performance_results.csv | cut -d',' -f4

echo "HeartBeat接口错误率统计:"
grep "HeartBeatCheck_Latency" build_perf/performance_results.csv | cut -d',' -f11
EOF

echo ""
echo "=================================="
echo "配置示例展示完成"
echo ""
echo "使用方法:"
echo "1. 根据需要选择合适的配置"
echo "2. 设置相应的环境变量"
echo "3. 运行测试脚本"
echo "4. 分析测试结果"
echo ""
echo "更多详细信息请参考: README_CommunicationPerformanceTest.md"
