# 测试目录的CMakeLists.txt

cmake_minimum_required(VERSION 3.16)

set(CMAKE_CXX_STANDARD 17)

# 设置x86包搜索路径
set(GRPC_INSTALL_PATH_X86 "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/")
set(absl_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/absl")
set(Protobuf_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/protobuf")
set(gRPC_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/grpc")

set(CMAKE_C_COMPILER /usr/bin/cc)
set(CMAKE_CXX_COMPILER /usr/bin/c++)

project(gRpcServerTest)

# 查找必要的包
find_package(GTest REQUIRED)
find_package(Protobuf CONFIG REQUIRED)
find_package(gRPC CONFIG REQUIRED)
find_package(Threads REQUIRED)

# 包含目录
# 设置CANopenNode相关目录
set(CANOPENNODE_DIR ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenNode)
include_directories(
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc
    ${CMAKE_SOURCE_DIR}/../../Business/ServoControl
    ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenMaster
    ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate
    ${CMAKE_SOURCE_DIR}/../../Common/Utils
    ${CANOPENNODE_DIR}
    ${CANOPENNODE_DIR}/301
    ${CANOPENNODE_DIR}/303
    ${CANOPENNODE_DIR}/304
    ${CANOPENNODE_DIR}/305
    ${CANOPENNODE_DIR}/309
    ${CANOPENNODE_DIR}/extra
    ${CANOPENNODE_DIR}/storage
    ${CANOPENNODE_DIR}/linux
    ${CANOPENNODE_DIR}/od
)
link_directories("/mnt/hgfs/E/BedMaster/CANopenNodePorting/cmake-build-debug")

set(PROTOBUF_GEN_SOURCES
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/motor_control.pb.cc
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/motor_control.grpc.pb.cc
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/ServoConfigParameters.pb.cc
)

# 创建测试可执行文件
add_executable(grpc_server_test
    GrpcServerTest.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/GrpcServer.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/ErrorCodeMapper.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/ProtobufConverter.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Utils/SyslogManager.cpp
    ${CMAKE_SOURCE_DIR}/../../Business/ServoControl/ServoControlManager.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenMaster/CANopenMaster.cpp
    ${PROTOBUF_GEN_SOURCES}
)

# 链接库

target_link_libraries(grpc_server_test
    GTest::gtest
    GTest::gtest_main
    gmock
    gmock_main
    protobuf::libprotobuf
    gRPC::grpc++
    Threads::Threads
    canopennode
)

# 添加测试
enable_testing()
add_test(NAME GrpcServerTest COMMAND grpc_server_test)

# 设置编译选项
target_compile_features(grpc_server_test PRIVATE cxx_std_17)
target_compile_options(grpc_server_test PRIVATE -Wall -Wextra)
