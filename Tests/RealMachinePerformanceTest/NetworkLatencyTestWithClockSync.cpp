#include <iostream>
#include <chrono>
#include <vector>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <thread>
#include <grpcpp/grpcpp.h>
#include "motor_control.grpc.pb.h"

/**
 * @brief 带时钟同步检测的网络延迟测试程序
 * 测量从服务端simulateProcessingDelay函数执行完毕到客户端接收响应的延迟
 * 包含时钟偏移检测和补偿功能
 */
class NetworkLatencyTesterWithClockSync {
public:
    struct ClockOffsetResult {
        double offset_us = 0.0;
        double rtt_us = 0.0;
        bool valid = false;
    };
    
    NetworkLatencyTesterWithClockSync(const std::string& server_address) 
        : server_address_(server_address) {
        auto channel = grpc::CreateChannel(server_address_, 
                                         grpc::InsecureChannelCredentials());
        stub_ = motor_control::BedMasterAppService::NewStub(channel);
    }
    
    // 测量时钟偏移量
    ClockOffsetResult measureClockOffset() {
        ClockOffsetResult result;
        
        try {
            grpc::ClientContext context;
            context.AddMetadata("client-type", "CLOCK_SYNC_TEST");
            context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(2));

            motor_control::StartMoveDescription request;
            motor_control::StartMoveStatus response;
            request.set_contextuid("clock-sync-" + 
                                  std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
            request.set_mode(motor_control::MotionMode::PositionMode);
            request.set_bedtype(motor_control::BedType::Primary);
            auto *motion_info = request.mutable_targetmotioninfo();
            motion_info->set_postion(100.0);
            motion_info->set_velocity(50.0);

            // 记录请求发送时间
            auto request_start = std::chrono::high_resolution_clock::now();
            int64_t client_send_timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                request_start.time_since_epoch()).count();

            grpc::Status status = stub_->StartMove(&context, request, &response);
            
            // 记录响应接收时间
            auto response_received = std::chrono::high_resolution_clock::now();
            int64_t client_receive_timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                response_received.time_since_epoch()).count();

            if (!status.ok() || response.errorcode() != 0) {
                return result; // valid = false
            }

            // 从metadata中获取服务端处理完成时间戳
            auto trailing_metadata = context.GetServerTrailingMetadata();
            auto timestamp_iter = trailing_metadata.find("server-processing-complete-timestamp");
            
            if (timestamp_iter == trailing_metadata.end()) {
                return result; // valid = false
            }

            int64_t server_complete_timestamp = std::stoll(
                std::string(timestamp_iter->second.data(), timestamp_iter->second.length()));

            // 计算往返时间和时钟偏移
            result.rtt_us = client_receive_timestamp - client_send_timestamp;
            
            // 假设网络延迟对称，估算服务端时间应该在请求中点
            int64_t estimated_server_time = client_send_timestamp + result.rtt_us / 2.0;
            result.offset_us = server_complete_timestamp - estimated_server_time;
            result.valid = true;

            return result;

        } catch (const std::exception &e) {
            std::cerr << "时钟偏移测量异常: " << e.what() << std::endl;
            return result; // valid = false
        }
    }
    
    // 估算时钟偏移量
    std::pair<double, bool> estimateClockOffset(int measurement_count = 5) {
        std::vector<double> valid_offsets;
        std::vector<double> rtts;
        
        std::cout << "开始时钟偏移检测 (测量次数: " << measurement_count << ")" << std::endl;
        
        for (int i = 0; i < measurement_count; ++i) {
            ClockOffsetResult measurement = measureClockOffset();
            
            if (measurement.valid) {
                valid_offsets.push_back(measurement.offset_us);
                rtts.push_back(measurement.rtt_us);
                
                std::cout << "  测量 " << (i + 1) << ": 偏移=" << std::fixed << std::setprecision(1) 
                         << measurement.offset_us << "μs, RTT=" << measurement.rtt_us << "μs" << std::endl;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (valid_offsets.size() < measurement_count / 2) {
            std::cout << "时钟偏移检测成功率过低" << std::endl;
            return {0.0, false};
        }
        
        // 计算平均偏移量和标准差
        double sum = std::accumulate(valid_offsets.begin(), valid_offsets.end(), 0.0);
        double avg_offset = sum / valid_offsets.size();
        
        double variance = 0.0;
        for (double offset : valid_offsets) {
            variance += (offset - avg_offset) * (offset - avg_offset);
        }
        double std_dev = std::sqrt(variance / valid_offsets.size());
        
        // 判断时钟同步是否可靠
        double avg_rtt = std::accumulate(rtts.begin(), rtts.end(), 0.0) / rtts.size();
        bool reliable = std_dev < (avg_rtt * 0.1);
        
        std::cout << "时钟偏移估算结果:" << std::endl;
        std::cout << "  平均偏移: " << std::fixed << std::setprecision(1) << avg_offset << "μs" << std::endl;
        std::cout << "  标准差: " << std_dev << "μs" << std::endl;
        std::cout << "  平均RTT: " << avg_rtt << "μs" << std::endl;
        std::cout << "  可靠性: " << (reliable ? "可靠" : "不可靠") << std::endl;
        
        return {avg_offset, reliable};
    }
    
    // 测量单次网络传输延迟（支持时钟偏移补偿）
    double measureSingleNetworkLatency(double clock_offset_us = 0.0) {
        try {
            grpc::ClientContext context;
            context.AddMetadata("client-type", "NETWORK_LATENCY_TEST");
            context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(5));

            motor_control::StartMoveDescription request;
            motor_control::StartMoveStatus response;
            request.set_contextuid("latency-test-" + 
                                  std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
            request.set_mode(motor_control::MotionMode::PositionMode);
            request.set_bedtype(motor_control::BedType::Primary);
            auto *motion_info = request.mutable_targetmotioninfo();
            motion_info->set_postion(100.0);
            motion_info->set_velocity(50.0);

            // 发送请求
            grpc::Status status = stub_->StartMove(&context, request, &response);
            
            // 立即记录客户端接收时间戳
            int64_t client_receive_timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                std::chrono::high_resolution_clock::now().time_since_epoch()).count();

            if (!status.ok() || response.errorcode() != 0) {
                std::cerr << "StartMove调用失败: " << status.error_message() << std::endl;
                return -1.0;
            }

            // 从trailing metadata中提取服务端处理完成时间戳
            auto trailing_metadata = context.GetServerTrailingMetadata();
            auto timestamp_iter = trailing_metadata.find("server-processing-complete-timestamp");
            
            if (timestamp_iter == trailing_metadata.end()) {
                std::cerr << "未找到服务端处理完成时间戳" << std::endl;
                return -1.0;
            }

            int64_t server_complete_timestamp = std::stoll(
                std::string(timestamp_iter->second.data(), timestamp_iter->second.length()));

            // 计算网络传输延迟（微秒），应用时钟偏移补偿
            double raw_network_latency_us = client_receive_timestamp - server_complete_timestamp;
            double compensated_network_latency_us = raw_network_latency_us - clock_offset_us;
            
            return compensated_network_latency_us;

        } catch (const std::exception &e) {
            std::cerr << "测量网络延迟时发生异常: " << e.what() << std::endl;
            return -1.0;
        }
    }
    
    // 执行完整的延迟测试（包含时钟同步检测）
    void runLatencyTest(int test_count = 20) {
        std::cout << "StartMove网络传输延迟测试（带时钟同步检测）" << std::endl;
        std::cout << "======================================================" << std::endl;
        std::cout << "服务器地址: " << server_address_ << std::endl;
        std::cout << "测试次数: " << test_count << std::endl << std::endl;
        
        // 1. 时钟偏移检测
        auto [clock_offset, is_reliable] = estimateClockOffset();
        
        // 2. 网络延迟测量
        std::vector<double> raw_latencies;
        std::vector<double> compensated_latencies;
        int negative_count = 0;
        
        std::cout << "\n开始网络延迟测量..." << std::endl;
        
        for (int i = 0; i < test_count; ++i) {
            double raw_latency = measureSingleNetworkLatency(0.0);
            double compensated_latency = measureSingleNetworkLatency(clock_offset);
            
            if (raw_latency != -1.0) {
                raw_latencies.push_back(raw_latency);
                
                if (raw_latency < 0) {
                    negative_count++;
                }
                
                if (compensated_latency > 0) {
                    compensated_latencies.push_back(compensated_latency);
                    
                    std::cout << "测试 " << (i+1) << ": 原始=" << std::fixed 
                             << std::setprecision(1) << raw_latency 
                             << "μs, 补偿后=" << compensated_latency << "μs" << std::endl;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 3. 统计分析
        if (!compensated_latencies.empty()) {
            printDetailedStatistics(raw_latencies, compensated_latencies, 
                                   clock_offset, is_reliable, negative_count);
        } else {
            std::cout << "\n错误: 没有获得有效的测量数据" << std::endl;
        }
    }
    
private:
    std::string server_address_;
    std::unique_ptr<motor_control::BedMasterAppService::Stub> stub_;
    
    void printDetailedStatistics(const std::vector<double>& raw_latencies,
                                const std::vector<double>& compensated_latencies,
                                double clock_offset, bool is_reliable, int negative_count) {
        // 计算补偿后延迟的统计
        std::vector<double> sorted_compensated = compensated_latencies;
        std::sort(sorted_compensated.begin(), sorted_compensated.end());
        
        double sum = std::accumulate(sorted_compensated.begin(), sorted_compensated.end(), 0.0);
        double avg = sum / sorted_compensated.size();
        double min_val = sorted_compensated.front();
        double max_val = sorted_compensated.back();
        double p50 = sorted_compensated[sorted_compensated.size() * 0.5];
        double p95 = sorted_compensated[sorted_compensated.size() * 0.95];
        double p99 = sorted_compensated[sorted_compensated.size() * 0.99];
        
        std::cout << "\n=== 网络传输延迟统计结果 ===" << std::endl;
        std::cout << "有效测试次数: " << compensated_latencies.size() << std::endl;
        std::cout << "平均延迟: " << std::fixed << std::setprecision(1) << avg << "μs (已补偿)" << std::endl;
        std::cout << "延迟范围: " << min_val << "μs - " << max_val << "μs" << std::endl;
        std::cout << "P50延迟: " << p50 << "μs" << std::endl;
        std::cout << "P95延迟: " << p95 << "μs" << std::endl;
        std::cout << "P99延迟: " << p99 << "μs" << std::endl;
        
        std::cout << "\n=== 时钟同步状态 ===" << std::endl;
        std::cout << "时钟偏移补偿: " << clock_offset << "μs" << std::endl;
        std::cout << "时钟同步可靠性: " << (is_reliable ? "可靠" : "不可靠") << std::endl;
        std::cout << "负延迟检测次数: " << negative_count << "/" << raw_latencies.size() << std::endl;
        
        if (negative_count > 0) {
            double negative_rate = (double)negative_count / raw_latencies.size() * 100.0;
            std::cout << "负延迟比例: " << std::setprecision(1) << negative_rate << "%" << std::endl;
        }
        
        std::cout << "\n说明:" << std::endl;
        std::cout << "- 测量的是从服务端simulateProcessingDelay函数执行完毕" << std::endl;
        std::cout << "  到客户端接收到响应的网络传输时间" << std::endl;
        std::cout << "- 已应用时钟偏移补偿，提高测量准确性" << std::endl;
        std::cout << "- 负延迟表明存在时钟同步问题" << std::endl;
        
        if (!is_reliable) {
            std::cout << "\n[警告] 时钟同步不可靠，建议:" << std::endl;
            std::cout << "1. 检查服务端和客户端的时钟同步状态" << std::endl;
            std::cout << "2. 使用NTP服务同步时钟" << std::endl;
            std::cout << "3. 在稳定网络环境下重新测试" << std::endl;
        }
    }
};

int main(int argc, char* argv[]) {
    std::string server_address = "localhost:50051";
    int test_count = 20;
    
    if (argc > 1) {
        server_address = argv[1];
    }
    if (argc > 2) {
        test_count = std::atoi(argv[2]);
    }
    
    NetworkLatencyTesterWithClockSync tester(server_address);
    tester.runLatencyTest(test_count);
    
    return 0;
}
