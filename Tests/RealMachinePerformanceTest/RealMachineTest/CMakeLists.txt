cmake_minimum_required(VERSION 3.10)

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置x86包搜索路径
set(GRPC_INSTALL_PATH_X86 "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/")
set(absl_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/absl")
set(Protobuf_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/protobuf")
set(gRPC_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/grpc")

set(CMAKE_C_COMPILER /usr/bin/cc)
set(CMAKE_CXX_COMPILER /usr/bin/c++)

# 项目名称
project(RealMachinePerformanceTest CXX C)

# 编译选项
add_compile_options(-Wextra -Wno-dev -g)
add_compile_options(-Wno-comment -Wno-type-limits -Wno-sign-compare -Wno-unused-parameter -Wno-write-strings -Wno-missing-field-initializers)

# 查找线程库
find_package(Threads REQUIRED)

# 包含头文件目录
include_directories(
        .
        ../../../Common/Protobuf/ProtobufGenerate
)

# 查找gRPC和Protobuf
set(protobuf_MODULE_COMPATIBLE TRUE)
find_package(Protobuf CONFIG REQUIRED)
message(STATUS "Using protobuf ${Protobuf_VERSION}")
set(_PROTOBUF_LIBPROTOBUF protobuf::libprotobuf)
set(_REFLECTION gRPC::grpc++_reflection)

find_package(gRPC CONFIG REQUIRED)
message(STATUS "Using gRPC ${gRPC_VERSION}")
set(_GRPC_GRPCPP gRPC::grpc++)

# 查找Google Test
find_package(GTest REQUIRED)
include_directories(${GTEST_INCLUDE_DIRS})

# 设置Proto生成文件目录
set(PROTO_GEN_DIR ../../../Common/Protobuf/ProtobufGenerate)

# 查找生成的Proto文件
file(GLOB PROTO_SRCS "${PROTO_GEN_DIR}/*.pb.cc")
file(GLOB PROTO_HDRS "${PROTO_GEN_DIR}/*.pb.h")
file(GLOB GRPC_SRCS "${PROTO_GEN_DIR}/*.grpc.pb.cc")
file(GLOB GRPC_HDRS "${PROTO_GEN_DIR}/*.grpc.pb.h")

message("Proto Sources: ")
foreach(item ${PROTO_SRCS})
    message(${item})
endforeach()

message("gRPC Sources: ")
foreach(item ${GRPC_SRCS})
    message(${item})
endforeach()

# 真机测试源文件
set(REAL_MACHINE_TEST_SOURCES
        RealMachinePerformanceTest.cpp
        TestConfig.cpp
        NetworkLatencyMeasurer.cpp
    ${PROTO_SRCS}
    ${PROTO_HDRS}
    ${GRPC_SRCS}
    ${GRPC_HDRS}
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${REAL_MACHINE_TEST_SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Threads::Threads
    ${_PROTOBUF_LIBPROTOBUF}
    ${_GRPC_GRPCPP}
    ${_REFLECTION}
    ${GTEST_LIBRARIES}
    gtest_main
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

install(FILES test_config.json
    DESTINATION bin
)

# 添加测试目标
enable_testing()
add_test(NAME RealMachineGrpcPerformanceTest COMMAND ${PROJECT_NAME})

message(STATUS "真机gRPC性能测试程序配置完成")
message(STATUS "构建目标: ${PROJECT_NAME}")
message(STATUS "专用于真机环境的gRPC通信性能测试")
