#include "NetworkLatencyMeasurer.hpp"
#include <iostream>
#include <sstream>
#include <regex>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <thread>
#include <chrono>
#include <cstdio>
#include <iomanip>

double NetworkLatencyMeasurer::measureRTT(const std::string& server_ip, int ping_count) {
    std::vector<double> rtts = executePing(server_ip, ping_count);
    
    if (rtts.empty()) {
        return -1.0;
    }
    
    // 计算平均RTT
    double sum = std::accumulate(rtts.begin(), rtts.end(), 0.0);
    return sum / rtts.size();
}

NetworkLatencyMeasurer::NetworkQuality 
NetworkLatencyMeasurer::measureNetworkQuality(const std::string& server_ip, int ping_count) {
    std::vector<double> rtts = executePing(server_ip, ping_count);
    return calculateStatistics(rtts);
}

std::vector<double> NetworkLatencyMeasurer::monitorLatency(const std::string& server_ip, 
                                                          int duration_seconds, 
                                                          int interval_ms) {
    std::vector<double> latencies;
    auto start_time = std::chrono::steady_clock::now();
    auto end_time = start_time + std::chrono::seconds(duration_seconds);
    
    std::cout << "[INFO] 开始监控网络延迟: " << server_ip 
             << ", 持续时间: " << duration_seconds << "s" << std::endl;
    
    while (std::chrono::steady_clock::now() < end_time) {
        double rtt = measureRTT(server_ip, 1);
        if (rtt > 0) {
            latencies.push_back(rtt);
            std::cout << "\r[MONITOR] RTT: " << std::fixed << std::setprecision(2) 
                     << rtt << "ms" << std::flush;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms));
    }
    
    std::cout << std::endl;
    std::cout << "[INFO] 网络延迟监控完成，共收集 " << latencies.size() << " 个样本" << std::endl;
    
    return latencies;
}

bool NetworkLatencyMeasurer::isReachable(const std::string& server_ip, int timeout_ms) {
    std::vector<double> rtts = executePing(server_ip, 1, timeout_ms);
    return !rtts.empty();
}

void NetworkLatencyMeasurer::printNetworkQualityReport(const NetworkQuality& quality) const {
    std::cout << "\n=== 网络质量报告 ===" << std::endl;
    std::cout << "连通性: " << (quality.is_reachable ? "可达" : "不可达") << std::endl;
    
    if (quality.is_reachable) {
        std::cout << "平均RTT: " << std::fixed << std::setprecision(2) 
                 << quality.avg_rtt_ms << "ms" << std::endl;
        std::cout << "最小RTT: " << quality.min_rtt_ms << "ms" << std::endl;
        std::cout << "最大RTT: " << quality.max_rtt_ms << "ms" << std::endl;
        std::cout << "丢包率: " << std::fixed << std::setprecision(1) 
                 << (quality.packet_loss_rate * 100) << "%" << std::endl;
        std::cout << "样本数量: " << quality.rtt_samples.size() << std::endl;
    }
    
    std::cout << "===================" << std::endl;
}

std::vector<double> NetworkLatencyMeasurer::executePing(const std::string& server_ip, 
                                                       int count, 
                                                       int timeout_ms) {
    try {
        // 构建ping命令
        std::ostringstream cmd;
#ifdef _WIN32
        cmd << "ping -n " << count << " -w " << timeout_ms << " " << server_ip;
#else
        cmd << "ping -c " << count << " -W " << (timeout_ms / 1000) << " " << server_ip << " 2>/dev/null";
#endif
        
        // 执行命令
        FILE* pipe = popen(cmd.str().c_str(), "r");
        if (!pipe) {
            std::cerr << "[ERROR] 无法执行ping命令" << std::endl;
            return {};
        }
        
        // 读取输出
        char buffer[256];
        std::string result;
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }
        
        int return_code = pclose(pipe);
        
        // 解析ping输出
        return parsePingOutput(result);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] ping执行异常: " << e.what() << std::endl;
        return {};
    }
}

std::vector<double> NetworkLatencyMeasurer::parsePingOutput(const std::string& ping_output) {
    std::vector<double> rtts;
    
    try {
        // 使用正则表达式解析ping输出中的时间信息
        // 支持Linux和Windows的ping输出格式
        std::regex time_regex(R"(time[<=](\d+\.?\d*)\s*ms)");
        std::sregex_iterator iter(ping_output.begin(), ping_output.end(), time_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            std::smatch match = *iter;
            if (match.size() > 1) {
                double rtt = std::stod(match[1].str());
                rtts.push_back(rtt);
            }
        }
        
        // 如果上面的正则表达式没有匹配到，尝试其他格式
        if (rtts.empty()) {
            std::regex alt_regex(R"((\d+\.?\d*)\s*ms)");
            std::sregex_iterator alt_iter(ping_output.begin(), ping_output.end(), alt_regex);
            
            for (; alt_iter != end; ++alt_iter) {
                std::smatch match = *alt_iter;
                if (match.size() > 1) {
                    double rtt = std::stod(match[1].str());
                    // 过滤掉明显不合理的值
                    if (rtt > 0 && rtt < 10000) {
                        rtts.push_back(rtt);
                    }
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] 解析ping输出失败: " << e.what() << std::endl;
    }
    
    return rtts;
}

NetworkLatencyMeasurer::NetworkQuality 
NetworkLatencyMeasurer::calculateStatistics(const std::vector<double>& samples) {
    NetworkQuality quality;
    quality.rtt_samples = samples;
    quality.is_reachable = !samples.empty();
    
    if (samples.empty()) {
        return quality;
    }
    
    // 计算基本统计信息
    quality.min_rtt_ms = *std::min_element(samples.begin(), samples.end());
    quality.max_rtt_ms = *std::max_element(samples.begin(), samples.end());
    
    double sum = std::accumulate(samples.begin(), samples.end(), 0.0);
    quality.avg_rtt_ms = sum / samples.size();
    
    // 计算丢包率（简化处理，基于期望的样本数量）
    // 在实际实现中，这需要从ping命令的统计输出中解析
    quality.packet_loss_rate = 0.0; // 简化处理
    
    return quality;
}
