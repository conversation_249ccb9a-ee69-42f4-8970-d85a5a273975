#ifndef NETWORK_LATENCY_MEASURER_HPP
#define NETWORK_LATENCY_MEASURER_HPP

#include <string>
#include <vector>

/**
 * @brief 网络延迟测量工具
 * 
 * 用于测量到目标服务器的网络延迟，帮助分析gRPC通信性能中的网络因素
 */
class NetworkLatencyMeasurer {
public:
    NetworkLatencyMeasurer() = default;
    ~NetworkLatencyMeasurer() = default;

    /**
     * @brief 测量到指定服务器的RTT延迟
     * @param server_ip 服务器IP地址
     * @param ping_count ping次数，默认5次
     * @return 平均RTT延迟（毫秒），失败返回-1.0
     */
    double measureRTT(const std::string& server_ip, int ping_count = 5);

    /**
     * @brief 测量网络质量指标
     * @param server_ip 服务器IP地址
     * @param ping_count ping次数
     * @return 网络质量统计信息
     */
    struct NetworkQuality {
        double avg_rtt_ms = 0.0;
        double min_rtt_ms = 0.0;
        double max_rtt_ms = 0.0;
        double packet_loss_rate = 0.0;
        bool is_reachable = false;
        std::vector<double> rtt_samples;
    };
    
    NetworkQuality measureNetworkQuality(const std::string& server_ip, int ping_count = 10);

    /**
     * @brief 持续监控网络延迟
     * @param server_ip 服务器IP地址
     * @param duration_seconds 监控持续时间（秒）
     * @param interval_ms 监控间隔（毫秒）
     * @return 监控期间的延迟统计
     */
    std::vector<double> monitorLatency(const std::string& server_ip, 
                                      int duration_seconds, 
                                      int interval_ms = 1000);

    /**
     * @brief 检查网络连通性
     * @param server_ip 服务器IP地址
     * @param timeout_ms 超时时间（毫秒）
     * @return 是否可达
     */
    bool isReachable(const std::string& server_ip, int timeout_ms = 3000);

    /**
     * @brief 打印网络质量报告
     * @param quality 网络质量数据
     */
    void printNetworkQualityReport(const NetworkQuality& quality) const;

private:
    /**
     * @brief 执行ping命令并解析结果
     * @param server_ip 服务器IP
     * @param count ping次数
     * @param timeout_ms 超时时间
     * @return ping结果
     */
    std::vector<double> executePing(const std::string& server_ip, int count, int timeout_ms = 3000);

    /**
     * @brief 解析ping命令输出
     * @param ping_output ping命令的输出
     * @return 延迟值列表
     */
    std::vector<double> parsePingOutput(const std::string& ping_output);

    /**
     * @brief 计算统计信息
     * @param samples 延迟样本
     * @return 统计结果
     */
    NetworkQuality calculateStatistics(const std::vector<double>& samples);
};

#endif // NETWORK_LATENCY_MEASURER_HPP
