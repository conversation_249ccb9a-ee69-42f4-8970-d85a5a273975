#include <gtest/gtest.h>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <thread>
#include <chrono>
#include <vector>
#include <map>
#include <string>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <atomic>
#include <future>
#include <condition_variable>
#include <mutex>
#include <fstream>
#include <sstream>

#include "motor_control.grpc.pb.h"
#include "TestConfig.hpp"
#include "NetworkLatencyMeasurer.hpp"

// 详细时间统计结构
struct DetailedTimingStats {
    double servo_processing_time_us = 0.0;      // 伺服处理时间
    double motion_completion_time_us = 0.0;     // 运动完成时间
    double response_transmission_time_us = 0.0; // 响应传输时间
    double total_end_to_end_time_us = 0.0;      // 端到端总时间
};

// 性能统计结构
struct PerformanceStats {
    std::vector<double> latencies_us;
    double p50 = 0.0;
    double p95 = 0.0;
    double p99 = 0.0;
    double avg = 0.0;
    double max = 0.0;
    double min = 0.0;
    double std_deviation = 0.0;
    size_t total_requests = 0;
    size_t error_count = 0;
    double error_rate = 0.0;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;

    // 真机测试特有字段
    int background_load_clients = 0;            // 背景负载客户端数量
    std::string motion_mode;                    // 运动模式
    DetailedTimingStats detailed_timing;        // 详细时间分解
    double network_rtt_us = 0.0;                // 网络RTT延迟

    double getDurationSeconds() const {
        return std::chrono::duration<double>(end_time - start_time).count();
    }
};

/**
 * @brief 背景负载模拟器（真机版本）
 *
 * 模拟多客户端以指定频率持续获取系统状态，用于测试在背景负载下的接口性能
 */
class BackgroundLoadSimulator {
public:
    BackgroundLoadSimulator(const std::string &server_address)
            : server_address_(server_address), running_(false) {}

    ~BackgroundLoadSimulator() {
        stopBackgroundLoad();
    }

    // 启动背景负载
    void startBackgroundLoad(int client_count, int frequency_ms) {
        if (running_.load()) {
            return; // 已经在运行
        }

        client_count_ = client_count;
        frequency_ms_ = frequency_ms;
        running_.store(true);

        // 重置统计数据
        total_requests_.store(0);
        error_count_.store(0);
        latencies_.clear();

        std::cout << "[INFO] 启动背景负载: " << client_count << " 个客户端, "
                  << frequency_ms << "ms 频率, 服务器: " << server_address_ << std::endl;

        // 创建客户端线程
        for (int i = 0; i < client_count; ++i) {
            client_threads_.emplace_back([this, i]() {
                runBackgroundClient(i);
            });
        }
    }

    // 停止背景负载
    void stopBackgroundLoad() {
        if (!running_.load()) {
            return;
        }

        running_.store(false);

        // 等待所有客户端线程结束
        for (auto &thread: client_threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        client_threads_.clear();

        std::cout << "[INFO] 背景负载已停止" << std::endl;
    }

    // 获取背景负载统计
    PerformanceStats getBackgroundLoadStats() {
        PerformanceStats stats;

        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats.latencies_us = latencies_;
        }

        stats.total_requests = total_requests_.load();
        stats.error_count = error_count_.load();
        stats.error_rate = stats.total_requests > 0 ?
                           static_cast<double>(stats.error_count) / stats.total_requests : 0.0;
        stats.background_load_clients = client_count_;

        return stats;
    }

private:
    void runBackgroundClient(int client_id) {
        // 为每个客户端创建独立连接
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        auto stub = motor_control::BedMasterAppService::NewStub(channel);

        while (running_.load()) {
            auto start_time = std::chrono::high_resolution_clock::now();

            // 调用GetSystemStatusInfo接口
            grpc::ClientContext context;
            context.AddMetadata("client-type", "BACKGROUND_LOAD");
            context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(2));

            motor_control::CommonDescription request;
            motor_control::SystemStatusInfoStatus response;
            request.set_contextuid("bg-load-" + std::to_string(client_id) + "-" +
                                   std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

            grpc::Status status = stub->GetSystemStatusInfo(&context, request, &response);

            auto end_time = std::chrono::high_resolution_clock::now();
            double latency_us = std::chrono::duration<double, std::micro>(end_time - start_time).count();

            // 更新统计数据
            {
                std::lock_guard<std::mutex> lock(stats_mutex_);
                latencies_.push_back(latency_us);
            }

            total_requests_++;
            if (!status.ok()) {
                error_count_++;
                std::cerr << "[WARNING] 背景负载客户端 " << client_id
                          << " 请求失败: " << status.error_message() << std::endl;
            }

            // 按指定频率休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(frequency_ms_));
        }
    }

    std::string server_address_;
    std::atomic<bool> running_;
    std::vector<std::thread> client_threads_;
    int client_count_ = 0;
    int frequency_ms_ = 10;

    // 统计数据
    std::atomic<size_t> total_requests_{0};
    std::atomic<size_t> error_count_{0};
    std::vector<double> latencies_;
    std::mutex stats_mutex_;
};

/**
 * @brief 真机gRPC通信性能测试类
 *
 * 基于现有RealMachinePerformanceTest改写，专注于真机环境的网络通信性能测试
 */
class RealMachinePerformanceTest : public ::testing::Test {
protected:

    void SetUp() override {
        // 加载测试配置
        if (!loadTestConfig()) {
            FAIL() << "无法加载测试配置文件";
        }

        // 测量网络延迟
        network_latency_measurer_ = std::make_unique<NetworkLatencyMeasurer>();
        double rtt = network_latency_measurer_->measureRTT(test_config_.server_address);
        if (rtt > 0) {
            network_rtt_us_ = rtt * 1000.0; // 转换为微秒
            std::cout << "[INFO] 网络RTT: " << std::fixed << std::setprecision(2)
                      << network_rtt_us_ << "μs" << std::endl;
        } else {
            std::cout << "[WARNING] 无法测量网络RTT" << std::endl;
        }

        // 构建服务器地址
        server_address_ = test_config_.server_address + ":" + std::to_string(test_config_.server_port);

        // 创建客户端连接
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        stub_ = motor_control::BedMasterAppService::NewStub(channel);

        // 验证服务器连接
        if (!checkServerConnection()) {
            FAIL() << "无法连接到ARM板卡gRPC服务: " << server_address_;
        }

        // 创建背景负载模拟器
        background_load_simulator_ = std::make_unique<BackgroundLoadSimulator>(server_address_);

        std::cout << "[INFO] 真机gRPC通信性能测试环境初始化完成" << std::endl;
        std::cout << "[INFO] 服务器地址: " << server_address_ << std::endl;
        std::cout << "[INFO] 最大并发客户端: " << test_config_.max_concurrent_clients << std::endl;
    }

    void TearDown() override {
        // 确保背景负载被停止
        if (background_load_simulator_) {
            background_load_simulator_->stopBackgroundLoad();
        }

        // 保存所有测试结果
        saveAllResultsToCsv();
        printFinalSummaryReport();

        std::cout << "[INFO] 真机性能测试结果已保存:" << std::endl;
        std::cout << "  - real_machine_performance_results.csv (易读格式)" << std::endl;
        std::cout << "  - real_machine_performance_summary.txt (摘要报告)" << std::endl;
    }

    // 加载测试配置
    bool loadTestConfig() {
        test_config_ = TestConfig::loadFromFile("test_config.json");
        return !test_config_.server_address.empty();
    }

    // 检查服务器连接
    bool checkServerConnection() {
        try {
            grpc::ClientContext context;
            context.set_deadline(std::chrono::system_clock::now() +
                                 std::chrono::milliseconds(test_config_.connection_timeout_ms));
            context.AddMetadata("client-type", "NONE");

            motor_control::CommonDescription request;
            motor_control::CommonStatus response;
            request.set_contextuid(
                    "connection-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

            grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

            if (status.ok()) {
                std::cout << "[INFO] 服务器连接验证成功" << std::endl;
                return true;
            } else {
                std::cerr << "[ERROR] 服务器连接验证失败: " << status.error_message() << std::endl;
                return false;
            }
        } catch (const std::exception &e) {
            std::cerr << "[ERROR] 连接检查异常: " << e.what() << std::endl;
            return false;
        }
    }

    // 计算性能统计
    PerformanceStats calculateStats(const std::vector<double> &latencies) {
        PerformanceStats stats;
        stats.latencies_us = latencies;
        stats.total_requests = latencies.size();
        stats.network_rtt_us = network_rtt_us_;

        if (latencies.empty()) {
            return stats;
        }

        // 排序以计算百分位数
        std::vector<double> sorted_latencies = latencies;
        std::sort(sorted_latencies.begin(), sorted_latencies.end());

        // 计算百分位数
        stats.p50 = calculatePercentile(sorted_latencies, 0.50);
        stats.p95 = calculatePercentile(sorted_latencies, 0.95);
        stats.p99 = calculatePercentile(sorted_latencies, 0.99);

        // 计算基本统计
        stats.min = sorted_latencies.front();
        stats.max = sorted_latencies.back();
        stats.avg = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();

        // 计算标准差
        double variance = 0.0;
        for (double latency: latencies) {
            variance += std::pow(latency - stats.avg, 2);
        }
        stats.std_deviation = std::sqrt(variance / latencies.size());

        return stats;
    }

    // 计算百分位数
    double calculatePercentile(const std::vector<double> &sorted_values, double percentile) {
        if (sorted_values.empty()) return 0.0;

        double index = percentile * (sorted_values.size() - 1);
        size_t lower_index = static_cast<size_t>(std::floor(index));
        size_t upper_index = static_cast<size_t>(std::ceil(index));

        if (lower_index == upper_index) {
            return sorted_values[lower_index];
        }

        double weight = index - lower_index;
        return sorted_values[lower_index] * (1.0 - weight) + sorted_values[upper_index] * weight;
    }

    // 调用指定的接口（带重试机制）
    bool callInterface(const std::string &interface_name, motor_control::BedMasterAppService::Stub *stub,
                       int max_retries = 3) {
        for (int retry = 0; retry < max_retries; ++retry) {
            try {
                grpc::ClientContext context;
                context.AddMetadata("client-type", "REAL_MACHINE_TEST");
                context.set_deadline(std::chrono::system_clock::now() +
                                     std::chrono::milliseconds(test_config_.request_timeout_ms));

                if (interface_name == "StartMove") {
                    motor_control::StartMoveDescription request;
                    motor_control::StartMoveStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
                    request.set_mode(motor_control::MotionMode::PositionMode);
                    request.set_bedtype(motor_control::BedType::Primary);
                    auto *motion_info = request.mutable_targetmotioninfo();
                    motion_info->set_postion(100.0);
                    motion_info->set_velocity(50.0);

                    grpc::Status status = stub->StartMove(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else if (interface_name == "GetSystemStatusInfo") {
                    motor_control::CommonDescription request;
                    motor_control::SystemStatusInfoStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->GetSystemStatusInfo(&context, request, &response);
                    return status.ok();

                } else if (interface_name == "HeartBeatCheck") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->HeartBeatCheck(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else if (interface_name == "GainControl") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->GainControl(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else if (interface_name == "GetPostId") {
                    motor_control::GetPostIdDescription request;
                    motor_control::GetPostIdStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
                    request.set_bedtype(motor_control::BedType::Primary);

                    grpc::Status status = stub->GetPostId(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;
                } else if (interface_name == "StopMove") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->StopMove(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;
                } else if (interface_name == "ReleaseControl") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("real-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->ReleaseControl(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;
                }

                return false;
            } catch (const std::exception &e) {
                std::cerr << "[WARNING] 接口调用异常 (重试 " << retry + 1 << "/" << max_retries
                          << "): " << e.what() << std::endl;
                if (retry < max_retries - 1) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (retry + 1)));
                }
            }
        }
        return false;
    }

    // 测量StartMove接口的网络传输延迟（从服务端处理完成到客户端接收响应）
    // 返回延迟时间（微秒），失败时返回-1.0
    double measureStartMoveLatencyFromMetadata(motor_control::BedMasterAppService::Stub *stub) {
        try {
            grpc::ClientContext context;
            context.AddMetadata("client-type", "NETWORK_LATENCY_TEST");
            context.set_deadline(std::chrono::system_clock::now() +
                               std::chrono::milliseconds(test_config_.request_timeout_ms));

            motor_control::StartMoveDescription request;
            motor_control::StartMoveStatus response;
            request.set_contextuid("network-latency-test-" +
                                  std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
            request.set_mode(motor_control::MotionMode::PositionMode);
            request.set_bedtype(motor_control::BedType::Primary);
            auto *motion_info = request.mutable_targetmotioninfo();
            motion_info->set_postion(100.0);
            motion_info->set_velocity(50.0);

            // 发送请求
            grpc::Status status = stub->StartMove(&context, request, &response);

            // 立即记录客户端接收时间戳
            int64_t client_receive_timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                std::chrono::high_resolution_clock::now().time_since_epoch()).count();

            if (!status.ok() || response.errorcode() != 0) {
                std::cerr << "[WARNING] StartMove调用失败: " << status.error_message() << std::endl;
                return -1.0;
            }

            // 从trailing metadata中提取服务端处理完成时间戳
            auto trailing_metadata = context.GetServerTrailingMetadata();
            auto timestamp_iter = trailing_metadata.find("server-processing-complete-timestamp");

            if (timestamp_iter == trailing_metadata.end()) {
                std::cerr << "[WARNING] 未找到服务端处理完成时间戳" << std::endl;
                return -1.0;
            }

            int64_t server_complete_timestamp = std::stoll(
                std::string(timestamp_iter->second.data(), timestamp_iter->second.length()));

            // 计算网络传输延迟（微秒）
            double network_latency_us = client_receive_timestamp - server_complete_timestamp;

            return network_latency_us;

        } catch (const std::exception &e) {
            std::cerr << "[ERROR] 测量网络延迟时发生异常: " << e.what() << std::endl;
            return -1.0;
        }
    }



    // 测试接口的串行延迟
    PerformanceStats measureInterfaceLatency(const std::string &interface_name, int request_count) {
        std::vector<double> latencies;
        std::atomic<int> completed_requests{0};
        std::atomic<int> error_count{0};

        auto start_time = std::chrono::steady_clock::now();

        std::cout << "[INFO] 开始测试 " << interface_name << " 接口延迟 (请求数: " << request_count << ")" << std::endl;

        for (int i = 0; i < request_count; ++i) {
            double latency_us = -1.0;

            // 对StartMove接口使用metadata时间戳测量延迟
            if (interface_name == "StartMove") {
                latency_us = measureStartMoveLatencyFromMetadata(stub_.get());
            } else {
                // 对其他接口使用传统的端到端延迟测量
                auto request_start = std::chrono::high_resolution_clock::now();
                bool success = callInterface(interface_name, stub_.get());
                auto request_end = std::chrono::high_resolution_clock::now();

                if (success) {
                    latency_us = std::chrono::duration<double, std::micro>(request_end - request_start).count();
                }
            }

            if (latency_us > 0) {
                latencies.push_back(latency_us);
            } else {
                error_count++;
            }

            completed_requests++;

            // 显示进度
            if (completed_requests % 10 == 0 || completed_requests == request_count) {
                std::cout << "\r[PROGRESS] " << interface_name << " "
                          << completed_requests << "/" << request_count
                          << " (" << (completed_requests * 100 / request_count) << "%)" << std::flush;
            }
        }

        std::cout << std::endl;

        auto end_time = std::chrono::steady_clock::now();

        // 计算统计结果
        PerformanceStats stats = calculateStats(latencies);
        stats.start_time = start_time;
        stats.end_time = end_time;
        stats.error_count = error_count.load();
        stats.error_rate = static_cast<double>(stats.error_count) / request_count;

        std::cout << "[INFO] " << interface_name << " 测试完成: 成功=" << latencies.size()
                  << ", 错误=" << error_count.load() << ", P95=" << std::fixed << std::setprecision(1)
                  << stats.p95 << "μs" << std::endl;

        return stats;
    }

    // 测试接口的并发延迟性能
    PerformanceStats measureInterfaceConcurrentLatency(const std::string &interface_name,
                                                       int request_count, int concurrent_clients) {
        std::vector<double> all_latencies;
        std::atomic<int> completed_requests{0};
        std::atomic<int> error_count{0};
        std::mutex latencies_mutex;

        auto start_time = std::chrono::steady_clock::now();

        std::cout << "[INFO] 开始测试 " << interface_name << " 并发延迟 (并发数: "
                  << concurrent_clients << ", 总请求数: " << request_count << ")" << std::endl;

        // 创建并发任务
        std::vector<std::future<std::vector<double>>> futures;
        int requests_per_client = request_count / concurrent_clients;

        for (int client = 0; client < concurrent_clients; ++client) {
            futures.push_back(std::async(std::launch::async, [=, &completed_requests, &error_count]() {
                std::vector<double> client_latencies;

                // 为每个客户端创建独立的连接
                auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
                auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

                for (int i = 0; i < requests_per_client; ++i) {
                    auto request_start = std::chrono::high_resolution_clock::now();

                    bool success = callInterface(interface_name, client_stub.get());

                    auto request_end = std::chrono::high_resolution_clock::now();

                    if (success) {
                        double latency_us = std::chrono::duration<double, std::micro>(
                                request_end - request_start).count();
                        client_latencies.push_back(latency_us);
                    } else {
                        error_count++;
                    }

                    completed_requests++;

                    // 更新进度
                    if (completed_requests % 20 == 0) {
                        std::cout << "\r[PROGRESS] " << interface_name << " "
                                  << completed_requests << "/" << request_count
                                  << " (" << (completed_requests * 100 / request_count) << "%)" << std::flush;
                    }
                }

                return client_latencies;
            }));
        }

        // 收集所有客户端的结果
        for (auto &future: futures) {
            auto client_latencies = future.get();
            std::lock_guard<std::mutex> lock(latencies_mutex);
            all_latencies.insert(all_latencies.end(), client_latencies.begin(), client_latencies.end());
        }

        std::cout << std::endl;

        auto end_time = std::chrono::steady_clock::now();

        // 计算统计结果
        PerformanceStats stats = calculateStats(all_latencies);
        stats.start_time = start_time;
        stats.end_time = end_time;
        stats.error_count = error_count.load();
        stats.error_rate = static_cast<double>(stats.error_count) / request_count;

        std::cout << "[INFO] " << interface_name << " 并发测试完成: 成功=" << all_latencies.size()
                  << ", 错误=" << error_count.load() << ", P95=" << std::fixed << std::setprecision(1)
                  << stats.p95 << "μs" << std::endl;

        return stats;
    }

    // 测试GetTriggerInfo流式接口性能
    PerformanceStats measureGetTriggerInfoStreamingLatency(int messages_per_stream, int duration_seconds) {
        std::vector<double> message_latencies;
        std::atomic<int> total_messages{0};
        std::atomic<int> error_count{0};
        std::mutex latencies_mutex;

        auto test_start = std::chrono::steady_clock::now();
        auto test_end_time = test_start + std::chrono::seconds(duration_seconds);

        std::cout << "[INFO] 开始GetTriggerInfo流式接口测试 (持续时间: " << duration_seconds << "s)" << std::endl;

        // 创建独立的客户端连接
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

        grpc::ClientContext context;
        context.AddMetadata("client-type", "TRIGGER_STREAMING_TEST");

        // 建立流连接
        auto stream_start = std::chrono::high_resolution_clock::now();
        auto stream = client_stub->GetTriggerInfo(&context);
        auto stream_establish_time = std::chrono::high_resolution_clock::now();

        double stream_setup_time_us = std::chrono::duration<double, std::micro>(
                stream_establish_time - stream_start).count();

        std::cout << "[INFO] 流建立时间: " << std::fixed << std::setprecision(1)
                  << stream_setup_time_us << "μs" << std::endl;

        // 进入消息接收循环
        motor_control::TriggerInfoStatus response;
        int messages_received = 0;

        while (std::chrono::steady_clock::now() < test_end_time &&
               messages_received < messages_per_stream) {

            auto read_start = std::chrono::high_resolution_clock::now();

            if (stream->Read(&response)) {
                auto read_end = std::chrono::high_resolution_clock::now();

                // 计算消息接收延迟
                double latency_us = std::chrono::duration<double, std::micro>(
                        read_end - read_start).count();

                {
                    std::lock_guard<std::mutex> lock(latencies_mutex);
                    message_latencies.push_back(latency_us);
                }

                messages_received++;
                total_messages++;

                // 定期更新进度
                if (messages_received % 50 == 0) {
                    std::cout << "\r[PROGRESS] 已接收消息: " << messages_received << std::flush;
                }

            } else {
                // 读取失败
                error_count++;
                std::cerr << "\n[ERROR] GetTriggerInfo 读取消息失败" << std::endl;
                break;
            }
        }

        std::cout << std::endl;

        // 关闭流
        stream->WritesDone();

        // 继续读取剩余数据
        while (stream->Read(&response)) {
            total_messages++;
        }

        auto finish_status = stream->Finish();
        if (!finish_status.ok()) {
            error_count++;
            std::cerr << "[ERROR] GetTriggerInfo 流结束时出错: "
                      << finish_status.error_message() << std::endl;
        }

        auto test_end = std::chrono::steady_clock::now();

        // 计算统计结果
        PerformanceStats stats;
        {
            std::lock_guard<std::mutex> lock(latencies_mutex);
            stats = calculateStats(message_latencies);
        }

        stats.start_time = test_start;
        stats.end_time = test_end;
        stats.error_count = error_count.load();
        stats.total_requests = total_messages.load();
        stats.error_rate = stats.total_requests > 0 ?
                           static_cast<double>(stats.error_count) / stats.total_requests : 0.0;

        // 将流建立时间存储在min字段中
        stats.min = stream_setup_time_us;

        std::cout << "[INFO] GetTriggerInfo流式测试完成: 接收消息数=" << total_messages.load()
                  << ", 错误数=" << error_count.load() << ", 流建立时间=" << std::fixed
                  << std::setprecision(1) << stream_setup_time_us << "μs" << std::endl;

        return stats;
    }

    // 测试流式接口性能（支持多并发流）
    PerformanceStats testStreamPerformance(int concurrent_streams, int messages_per_stream, int duration_seconds) {
        std::mutex metrics_mutex;
        std::vector<double> stream_setup_times;
        std::vector<double> message_latencies;
        std::atomic<int> total_messages{0};
        std::atomic<int> error_count{0};
        std::atomic<bool> test_running{true};

        auto test_start = std::chrono::steady_clock::now();

        std::cout << "[INFO] 开始多并发流测试 (并发流数: " << concurrent_streams
                  << ", 持续时间: " << duration_seconds << "s)" << std::endl;

        // 创建并发流任务
        std::vector<std::future<void>> stream_futures;

        for (int stream_id = 0; stream_id < concurrent_streams; ++stream_id) {
            stream_futures.push_back(std::async(std::launch::async,
                                                [=, &metrics_mutex, &stream_setup_times, &message_latencies, &total_messages, &error_count, &test_running]() {
                                                    try {
                                                        // 创建独立的客户端连接
                                                        auto channel = grpc::CreateChannel(server_address_,
                                                                                           grpc::InsecureChannelCredentials());
                                                        auto client_stub = motor_control::BedMasterAppService::NewStub(
                                                                channel);

                                                        grpc::ClientContext context;
                                                        context.AddMetadata("client-type", "CONCURRENT_STREAM_TEST");

                                                        // 建立流连接
                                                        auto stream_start = std::chrono::high_resolution_clock::now();
                                                        auto stream = client_stub->GetTriggerInfo(&context);

                                                        auto establish_end = std::chrono::high_resolution_clock::now();
                                                        auto establishment_time_us = std::chrono::duration<double, std::micro>(
                                                                establish_end - stream_start).count();
                                                        {
                                                            std::lock_guard<std::mutex> lock(metrics_mutex);
                                                            stream_setup_times.push_back(establishment_time_us);
                                                        }

                                                        // 进入消息接收循环
                                                        motor_control::TriggerInfoStatus response;
                                                        int messages_received = 0;

                                                        while (test_running.load() &&
                                                               (messages_received < messages_per_stream)) {
                                                            auto read_start = std::chrono::high_resolution_clock::now();

                                                            if (stream->Read(&response)) {
                                                                auto read_end = std::chrono::high_resolution_clock::now();

                                                                // 记录消息接收延迟
                                                                auto latency_us = std::chrono::duration<double, std::micro>(
                                                                        read_end - read_start).count();

                                                                {
                                                                    std::lock_guard<std::mutex> lock(metrics_mutex);
                                                                    message_latencies.push_back(latency_us);
                                                                }

                                                                messages_received++;
                                                                total_messages++;

                                                            } else {
                                                                // 读取失败
                                                                if (test_running.load()) {
                                                                    error_count++;
                                                                    std::cerr << "[ERROR] 流 " << stream_id << " 读取消息失败"
                                                                              << std::endl;
                                                                }
                                                                break;
                                                            }
                                                        }

                                                        // 发送结束请求
                                                        stream->WritesDone();

                                                        // 继续读取剩余数据
                                                        while (stream->Read(&response)) {
                                                            total_messages++;
                                                        }

                                                        auto finish_status = stream->Finish();
                                                        if (!finish_status.ok()) {
                                                            error_count++;
                                                            std::cerr << "[ERROR] 流 " << stream_id << " 结束时出错: "
                                                                      << finish_status.error_message() << std::endl;
                                                        }

                                                        std::cout << "[INFO] 流 " << stream_id << " 完成，接收消息数: "
                                                                  << messages_received << std::endl;

                                                    } catch (const std::exception &e) {
                                                        error_count++;
                                                        std::cerr << "[ERROR] 流 " << stream_id << " 异常: " << e.what()
                                                                  << std::endl;
                                                    }
                                                }));
        }

        // 运行指定时间后停止测试
        std::this_thread::sleep_for(std::chrono::seconds(duration_seconds));
        test_running.store(false);

        // 等待所有流完成
        for (auto &future: stream_futures) {
            future.wait();
        }

        auto test_end = std::chrono::steady_clock::now();

        // 计算统计结果
        PerformanceStats stats;
        {
            std::lock_guard<std::mutex> lock(metrics_mutex);
            stats = calculateStats(message_latencies);
        }

        stats.start_time = test_start;
        stats.end_time = test_end;
        stats.error_count = error_count.load();
        stats.total_requests = total_messages.load();

        // 计算错误率
        stats.error_rate = static_cast<double>(stats.error_count) /
                           static_cast<double>(stats.total_requests + stats.error_count);

        // 将流建立时间存储在min字段中（平均流建立时间）
        {
            std::lock_guard<std::mutex> lock(metrics_mutex);
            if (!stream_setup_times.empty()) {
                stats.min = std::accumulate(stream_setup_times.begin(), stream_setup_times.end(), 0.0) /
                            stream_setup_times.size();
            }
        }

        std::cout << "[INFO] 多并发流测试完成: 总消息数=" << total_messages.load()
                  << ", 错误数=" << error_count.load() << ", 平均流建立时间="
                  << std::fixed << std::setprecision(1) << stats.min << "μs" << std::endl;

        return stats;
    }

    // 测试StartMove接口的单客户端异步并发性能（支持多种运动模式）
    PerformanceStats measureStartMoveConcurrentLatency(const std::string &motion_mode,
                                                       int concurrent_requests, int total_requests) {
        std::vector<double> all_latencies;
        std::atomic<int> completed_requests{0};
        std::atomic<int> error_count{0};
        std::mutex latencies_mutex;

        auto start_time = std::chrono::steady_clock::now();

        std::cout << "[INFO] 开始StartMove单客户端异步并发测试: " << motion_mode
                  << ", 并发数=" << concurrent_requests
                  << ", 总请求数=" << total_requests << std::endl;

        // 创建单个客户端连接
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

        // 计算需要执行的批次数
        int batches = total_requests / concurrent_requests;

        for (int batch = 0; batch < batches; ++batch) {
            // 创建并发任务
            std::vector<std::future<std::pair<double, bool>>> futures;

            for (int i = 0; i < concurrent_requests; ++i) {
                int request_id = batch * concurrent_requests + i;
                futures.push_back(std::async(std::launch::async,
                                             [=, &client_stub, &motion_mode]() -> std::pair<double, bool> {
                                                 double latency_us = -1.0;
                                                 bool success = false;

                                                 try {
                                                     // 构造StartMove请求
                                                     grpc::ClientContext context;
                                                     context.AddMetadata("client-type", "CONCURRENT_STARTMOVE_TEST");
                                                     context.set_deadline(std::chrono::system_clock::now() +
                                                                          std::chrono::milliseconds(
                                                                                  test_config_.request_timeout_ms));

                                                     motor_control::StartMoveDescription request;
                                                     motor_control::StartMoveStatus response;

                                                     request.set_contextuid(
                                                             "startmove-concurrent-" + std::to_string(request_id));

                                                     // 根据运动模式设置请求参数
                                                     if (motion_mode == "PositionMode") {
                                                         request.set_mode(motor_control::MotionMode::PositionMode);
                                                     } else if (motion_mode == "VelocityMode") {
                                                         request.set_mode(motor_control::MotionMode::VelocityMode);
                                                     } else if (motion_mode == "HomingMode") {
                                                         request.set_mode(motor_control::MotionMode::HomingMode);
                                                     } else {
                                                         request.set_mode(
                                                                 motor_control::MotionMode::VelocityMode); // 默认速度模式
                                                     }

                                                     request.set_bedtype(motor_control::BedType::Primary);

                                                     auto *motion_info = request.mutable_targetmotioninfo();
                                                     motion_info->set_postion(100.0f + request_id); // 变化的位置
                                                     motion_info->set_velocity(50.0);

                                                     // 发送请求
                                                     grpc::Status status = client_stub->StartMove(&context, request, &response);

                                                     // 立即记录客户端接收时间戳
                                                     int64_t client_receive_timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                                                         std::chrono::high_resolution_clock::now().time_since_epoch()).count();

                                                     if (status.ok() && response.errorcode() == 0) {
                                                         // 从trailing metadata中提取服务端处理完成时间戳
                                                         auto trailing_metadata = context.GetServerTrailingMetadata();
                                                         auto timestamp_iter = trailing_metadata.find("server-processing-complete-timestamp");

                                                         if (timestamp_iter != trailing_metadata.end()) {
                                                             int64_t server_complete_timestamp = std::stoll(
                                                                 std::string(timestamp_iter->second.data(), timestamp_iter->second.length()));

                                                             // 计算网络传输延迟（微秒）
                                                             latency_us = client_receive_timestamp - server_complete_timestamp;
                                                             success = true;
                                                         }
                                                     }
                                                 } catch (const std::exception &e) {
                                                     // 异常处理
                                                 }

                                                 return std::make_pair(latency_us, success);
                                             }));
            }

            // 收集当前批次的结果
            for (auto &future: futures) {
                auto result = future.get();
                double latency_us = result.first;
                bool success = result.second;

                if (success && latency_us > 0) {
                    std::lock_guard<std::mutex> lock(latencies_mutex);
                    all_latencies.push_back(latency_us);
                } else {
                    error_count++;
                }

                completed_requests++;
            }

            // 更新进度
            if (batch % 5 == 0 || batch == batches - 1) {
                std::cout << "\r[PROGRESS] StartMove_" << motion_mode << "_Concurrent "
                          << completed_requests << "/" << total_requests
                          << " (" << (completed_requests * 100 / total_requests) << "%)" << std::flush;
            }

            // 批次间稍微休息，避免过度压力
            if (batch < batches - 1) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }

        std::cout << std::endl;

        auto end_time = std::chrono::steady_clock::now();

        // 计算统计结果
        PerformanceStats stats = calculateStats(all_latencies);
        stats.start_time = start_time;
        stats.end_time = end_time;
        stats.error_count = error_count.load();
        stats.error_rate = static_cast<double>(stats.error_count) / total_requests;
        stats.motion_mode = motion_mode;

        std::cout << "[INFO] StartMove单客户端异步并发测试完成: 成功请求数=" << all_latencies.size()
                  << ", 错误数=" << error_count.load() << ", 运动模式=" << motion_mode << std::endl;

        return stats;
    }

    // 扩展的接口调用方法，支持更多接口（注意：StartMove接口已改为使用metadata时间戳测量）
    bool callInterfaceExtended(const std::string &interface_name,
                               motor_control::BedMasterAppService::Stub *stub,
                               const std::string &motion_mode = "", int max_retries = 3) {
        for (int retry = 0; retry < max_retries; ++retry) {
            try {
                grpc::ClientContext context;
                context.AddMetadata("client-type", "EXTENDED_INTERFACE_TEST");
                context.set_deadline(std::chrono::system_clock::now() +
                                     std::chrono::milliseconds(test_config_.request_timeout_ms));

                if (interface_name == "StartMove") {
                    motor_control::StartMoveDescription request;
                    motor_control::StartMoveStatus response;
                    request.set_contextuid("extended-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    // 根据运动模式设置参数
                    if (motion_mode == "PositionMode") {
                        request.set_mode(motor_control::MotionMode::PositionMode);
                    } else if (motion_mode == "VelocityMode") {
                        request.set_mode(motor_control::MotionMode::VelocityMode);
                    } else if (motion_mode == "HomingMode") {
                        request.set_mode(motor_control::MotionMode::HomingMode);
                    } else {
                        request.set_mode(motor_control::MotionMode::PositionMode); // 默认位置模式
                    }

                    request.set_bedtype(motor_control::BedType::Primary);
                    auto *motion_info = request.mutable_targetmotioninfo();
                    motion_info->set_postion(100.0);
                    motion_info->set_velocity(50.0);

                    grpc::Status status = stub->StartMove(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else if (interface_name == "StopMove") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("extended-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->StopMove(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else if (interface_name == "ReleaseControl") {
                    motor_control::CommonDescription request;
                    motor_control::CommonStatus response;
                    request.set_contextuid("extended-test-" +
                                           std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                    grpc::Status status = stub->ReleaseControl(&context, request, &response);
                    return status.ok() && response.errorcode() == 0;

                } else {
                    // 调用原有的接口方法
                    return callInterface(interface_name, stub, max_retries);
                }

            } catch (const std::exception &e) {
                std::cerr << "[WARNING] 扩展接口调用异常 (重试 " << retry + 1 << "/" << max_retries
                          << "): " << e.what() << std::endl;
                if (retry < max_retries - 1) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (retry + 1)));
                }
            }
        }
        return false;
    }

    // 保存结果到CSV文件
    void saveAllResultsToCsv() {
        std::ofstream file("real_machine_performance_results.csv");

        file << "=== 真机gRPC通信性能测试结果 ===" << std::endl;
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream time_ss;
        time_ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        file << "测试时间," << time_ss.str() << std::endl;
        file << "服务器地址," << server_address_ << std::endl;
        file << "网络RTT," << std::fixed << std::setprecision(2) << network_rtt_us_ << ",μs" << std::endl;
        file << std::endl;

        for (const auto &result: all_test_results_) {
            const auto &stats = result.second;

            file << "=== " << result.first << " ===" << std::endl;
            file << "指标项,数值,单位" << std::endl;
            file << "总请求数," << stats.total_requests << ",个" << std::endl;
            file << "P50延迟," << std::fixed << std::setprecision(2) << stats.p50 << ",μs" << std::endl;
            file << "P95延迟," << stats.p95 << ",μs" << std::endl;
            file << "P99延迟," << stats.p99 << ",μs" << std::endl;
            file << "平均延迟," << stats.avg << ",μs" << std::endl;
            file << "最大延迟," << stats.max << ",μs" << std::endl;
            file << "最小延迟," << stats.min << ",μs" << std::endl;
            file << "错误数量," << stats.error_count << ",个" << std::endl;
            file << "错误率," << std::fixed << std::setprecision(4) << (stats.error_rate * 100) << ",%" << std::endl;
            file << "网络RTT," << stats.network_rtt_us << ",μs" << std::endl;
            file << "持续时间," << stats.getDurationSeconds() << ",s" << std::endl;

            if (stats.getDurationSeconds() > 0) {
                double qps = static_cast<double>(stats.total_requests) / stats.getDurationSeconds();
                file << "QPS," << std::fixed << std::setprecision(1) << qps << ",req/s" << std::endl;
            }

            file << std::endl;
        }

        file.close();
    }

    // 打印最终摘要报告
    void printFinalSummaryReport() {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "真机gRPC通信性能测试摘要报告" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        std::cout << "服务器地址: " << server_address_ << std::endl;
        std::cout << "网络RTT: " << std::fixed << std::setprecision(2) << network_rtt_us_ << "μs" << std::endl;
        std::cout << "总测试项目: " << all_test_results_.size() << std::endl;

        // 生成文本摘要报告
        std::ofstream report("real_machine_performance_summary.txt");
        report << "=== 真机gRPC通信性能测试报告 ===" << std::endl;
        report << "服务器地址: " << server_address_ << std::endl;
        report << "网络RTT: " << std::fixed << std::setprecision(2) << network_rtt_us_ << "μs" << std::endl;
        report << std::endl;

        for (const auto &result: all_test_results_) {
            const auto &stats = result.second;

            // 检查是否达到性能目标
            bool passed = true;
            std::string status = "✓";

            // 根据接口类型检查性能目标
            if (result.first.find("StartMove") != std::string::npos) {
                passed = stats.p95 <= test_config_.performance_targets.StartMove_p95_ms * 1000.0;
            } else if (result.first.find("GetSystemStatusInfo") != std::string::npos) {
                passed = stats.p95 <= test_config_.performance_targets.GetSystemStatusInfo_p95_ms * 1000.0;
            } else if (result.first.find("HeartBeatCheck") != std::string::npos) {
                passed = stats.p95 <= test_config_.performance_targets.HeartBeatCheck_p95_ms * 1000.0;
            }

            passed = passed && (stats.error_rate <= test_config_.performance_targets.max_error_rate);

            if (!passed) status = "⚠";

            std::cout << status << " " << result.first << ": P95="
                      << std::fixed << std::setprecision(1) << stats.p95
                      << "μs, 错误率=" << std::setprecision(3) << (stats.error_rate * 100) << "%" << std::endl;

            report << status << " " << result.first << ": P95="
                   << std::fixed << std::setprecision(1) << stats.p95
                   << "μs, 错误率=" << std::setprecision(3) << (stats.error_rate * 100) << "%" << std::endl;
        }

        report.close();
        std::cout << std::string(60, '=') << std::endl;
    }

    // 成员变量
    TestConfig test_config_;
    std::unique_ptr<motor_control::BedMasterAppService::Stub> stub_;
    std::string server_address_;
    double network_rtt_us_ = 0.0;

    // 网络延迟测量器
    std::unique_ptr<NetworkLatencyMeasurer> network_latency_measurer_;

    // 背景负载模拟器
    std::unique_ptr<BackgroundLoadSimulator> background_load_simulator_;

    // 存储所有测试结果
    std::map<std::string, PerformanceStats> all_test_results_;
};

// ==================== 测试用例 ====================

/**
 * @brief 基础连通性测试
 */
TEST_F(RealMachinePerformanceTest, BasicConnectivityTest) {
    std::cout << "\n[TEST] 基础连通性测试" << std::endl;

// 测试HeartBeatCheck接口
    PerformanceStats stats = measureInterfaceLatency("HeartBeatCheck", 10);
    all_test_results_["BasicConnectivity_HeartBeatCheck"] = stats;

// 验证基本性能要求
    EXPECT_LT(stats.p95, test_config_.performance_targets.HeartBeatCheck_p95_ms * 1000.0)
                        << "HeartBeatCheck P95延迟超过目标: " << stats.p95 << "μs";
    EXPECT_LT(stats.error_rate, test_config_.performance_targets.max_error_rate)
                        << "错误率超过目标: " << (stats.error_rate * 100) << "%";

    std::cout << "[RESULT] 基础连通性测试完成" << std::endl;
}

/**
 * @brief GetSystemStatusInfo并发处理性能测试
 */
TEST_F(RealMachinePerformanceTest, GetSystemStatusInfoConcurrencyTest) {
    std::cout << "\n[INFO] 开始GetSystemStatusInfo接口并发性能测试..." << std::endl;

    // 测试参数
    const int requests_per_client = 30;
    const std::vector<int> concurrent_client_counts = {1, 3, 5}; // 多客户端多请求

    // 性能目标：P95 < 20000μs
    const double p95_target_us = 10000.0;
    const double p99_target_us = 20000.0;
    const double avg_target_us = 10000.0;

    for (int client_count: concurrent_client_counts) {
        std::cout << "\n[INFO] 测试配置: 并发客户端数=" << client_count << std::endl;

        // 执行并发性能测试
        auto stats = measureInterfaceConcurrentLatency("GetSystemStatusInfo", client_count * requests_per_client,
                                                       client_count);

        // 设置测试结果的额外信息
        stats.background_load_clients = 0; // 这个测试本身就是并发测试
        stats.motion_mode = "SystemStatusInfo";

        // 保存结果
        std::string test_name = "GetSystemStatusInfo_Concurrent" + std::to_string(client_count);
        all_test_results_[test_name] = stats;

        // 打印结果
        std::cout << "[RESULT] " << test_name << ": "
                  << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "μs, "
                  << "P95=" << stats.p95 << "μs, "
                  << "P99=" << stats.p99 << "μs, "
                  << "Avg=" << stats.avg << "μs, "
                  << "Min=" << stats.min << "μs, "
                  << "Max=" << stats.max << "μs, "
                  << "总请求数=" << stats.total_requests << ", "
                  << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

        // 计算QPS
        double duration_sec = stats.getDurationSeconds();
        if (duration_sec > 0) {
            double qps = static_cast<double>(stats.total_requests) / duration_sec;
            std::cout << "[INFO] QPS: " << std::setprecision(1) << qps << std::endl;
        }

        // 检查性能目标
        if (stats.p95 <= p95_target_us) {
            std::cout << "[✓] 性能目标达成 (P95 <= " << p95_target_us << "μs)" << std::endl;
        } else {
            std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_target_us << "μs)" << std::endl;
        }

        // 验证基本性能要求
        EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
        EXPECT_GT(stats.p50, 0.0) << test_name << " P50延迟异常";
        EXPECT_LT(stats.p99, p99_target_us) << test_name << " P99延迟过高";
        EXPECT_LT(stats.p95, p95_target_us) << test_name << " P95延迟过高";
        EXPECT_LT(stats.avg, avg_target_us) << test_name << " 平均延迟过高";

        // 验证并发扩展性（简化处理）
        if (client_count > 1) {
            EXPECT_LT(stats.p95, p95_target_us * 3.0) << test_name << " 并发扩展性较差";
        }
    }

    std::cout << "\n[INFO] GetSystemStatusInfo接口并发性能测试完成" << std::endl;
}

/**
 * @brief StartMove串行处理性能测试
 */
TEST_F(RealMachinePerformanceTest, StartMoveSerialTest) {
    std::cout << "\n[TEST] StartMove接口延迟测试（多运动模式）" << std::endl;

// 1. 串行测试（保持原有功能）
    std::cout << "\n[INFO] 执行StartMove串行测试..." << std::endl;
    PerformanceStats serial_stats = measureInterfaceLatency("StartMove", 50);
    all_test_results_["StartMove_Serial"] = serial_stats;

// 验证串行性能要求
    EXPECT_LT(serial_stats.p95, 50000.0)
                        << "StartMove串行P95延迟超过目标: " << serial_stats.p95 << "μs";
    EXPECT_LT(serial_stats.error_rate, 0.01)
                        << "StartMove串行错误率超过目标: " << 0.01 << "%";

    std::cout << "[RESULT] StartMove串行测试完成: P95=" << std::fixed
              << std::setprecision(1) << serial_stats.p95 << "μs" << std::endl;
    std::cout << "[INFO] 注意：StartMove延迟测量已改为从服务端处理完成到客户端接收响应的网络传输时间" << std::endl;
}

/**
 * @brief StartMove并发性能测试
 */
TEST_F(RealMachinePerformanceTest, StartMoveConcurrentTest) {
    std::cout << "\n[TEST] StartMove并发专项测试" << std::endl;

    std::vector<std::string> motion_modes = {"PositionMode"};


    // 1. 单客户端异步并发测试
    std::cout << "\n[INFO] 执行单客户端异步并发测试..." << std::endl;

    int concurrent_request = 3;
    int total_request = 30;
    for (const std::string &mode: motion_modes) {
        std::cout << "\n[INFO] 测试" << mode << "异步并发性能..." << std::endl;

        // 测试参数：3个并发请求，总共30个请求
        PerformanceStats concurrent_stats = measureStartMoveConcurrentLatency(mode, concurrent_request, total_request);

        std::string test_name = "StartMove_AsyncConcurrent";
        all_test_results_[test_name] = concurrent_stats;

        // 验证异步并发性能（允许稍高的延迟）
        EXPECT_LT(concurrent_stats.p95, 50000.0)
                            << "StartMove " << mode << " 异步并发P95延迟过高: " << concurrent_stats.p95 << "μs";
        EXPECT_LT(concurrent_stats.error_rate, 0.01)
                            << "StartMove " << mode << " 异步并发错误率过高: " << (concurrent_stats.error_rate * 100)
                            << "%";

        std::cout << "[RESULT] " << mode << " 异步并发测试: P95=" << std::fixed
                  << std::setprecision(1) << concurrent_stats.p95 << "μs, 错误率="
                  << std::setprecision(3) << (concurrent_stats.error_rate * 100) << "%" << std::endl;
    }
}

/**
 * @brief 背景负载下的StartMove并发性能测试
 */
TEST_F(RealMachinePerformanceTest, StartMoveConcurrentTestWithBG) {

    std::cout << "\n[TEST] 开始背景负载下的StartMove并发性能测试" << std::endl;

    const std::vector<int> background_client_counts = {0, 1, 3, 5}; // 包括无背景负载的基准测试
    int concurrent_request = 3;
    int total_request = 30;
    std::string motion_mode{"VelocityMode"};

    for (int bg_clients: background_client_counts) {
        std::cout << "\n[INFO] 测试配置: 背景负载客户端=" << bg_clients
                  << ", 并发请求数=" << concurrent_request
                  << ", 总请求数=" << total_request << std::endl;

        // 启动背景负载（如果需要）
        if (bg_clients > 0) {
            background_load_simulator_->startBackgroundLoad(bg_clients, 10); // 10ms频率
            std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待背景负载稳定
        }

        // 执行StartMove并发性能测试
        auto stats = measureStartMoveConcurrentLatency(motion_mode, concurrent_request, total_request);

        // 停止背景负载
        if (bg_clients > 0) {
            background_load_simulator_->stopBackgroundLoad();
        }

        // 设置测试结果的额外信息
        stats.background_load_clients = bg_clients;
        stats.motion_mode = motion_mode;

        // 保存结果
        std::string test_name = "StartMove_Concurrent_BG" + std::to_string(bg_clients);
        all_test_results_[test_name] = stats;

        // 获取背景负载统计
        PerformanceStats bg_stats = background_load_simulator_->getBackgroundLoadStats();
        all_test_results_["BackgroundLoad_Stats"] = bg_stats;

        // 打印结果
        std::cout << "[RESULT] " << test_name << ": "
                  << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "μs, "
                  << "P95=" << stats.p95 << "μs, "
                  << "P99=" << stats.p99 << "μs, "
                  << "Avg=" << stats.avg << "μs, "
                  << "Min=" << stats.min << "μs, "
                  << "Max=" << stats.max << "μs, "
                  << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%, "
                  << "QPS=" << std::setprecision(1)
                  << (stats.getDurationSeconds() > 0 ? stats.total_requests / stats.getDurationSeconds() : 0)
                  << std::endl;


        // 验证基本性能要求
        EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
        EXPECT_GT(stats.p50, 0.0) << test_name << " P50延迟异常";
        EXPECT_LT(stats.avg, 5000.0) << test_name << " 平均延迟过高";
    }
}

/**
 * @brief GetTriggerInfo流式接口专项测试（包含三种测试场景）
 */
TEST_F(RealMachinePerformanceTest, GetTriggerInfoStreamingTestAll) {
    std::cout << "\n[TEST] GetTriggerInfo流式接口专项测试" << std::endl;

    // 1. 多并发流性能测试
    std::vector<int> concurrent_stream_counts = {1};

    for (int stream_count: concurrent_stream_counts) {
        std::cout << "\n[INFO] 执行" << stream_count << "并发流测试..." << std::endl;

        PerformanceStats concurrent_stats = testStreamPerformance(stream_count, 500, 5);
        concurrent_stats.motion_mode = "ConcurrentStreams_" + std::to_string(stream_count);

        std::string test_name = "GetTriggerInfo_Concurrent_" + std::to_string(stream_count) + "_Streams";
        all_test_results_[test_name] = concurrent_stats;

        // 验证并发流性能
        EXPECT_LT(concurrent_stats.p95, 50000.0)
                            << test_name << " P95延迟过高: " << concurrent_stats.p95 << "μs";
        EXPECT_LT(concurrent_stats.error_rate, 0.01)
                            << test_name << " 错误率过高: " << (concurrent_stats.error_rate * 100) << "%";

        std::cout << "[RESULT] " << stream_count << "并发流测试完成: 平均流建立时间="
                  << std::fixed << std::setprecision(1) << concurrent_stats.min
                  << "μs, P95延迟=" << concurrent_stats.p95 << "μs" << std::endl;
    }

    // 2. 背景负载下的流式接口性能测试
    std::cout << "\n[INFO] 执行背景负载下的流式接口测试..." << std::endl;

    // 启动背景负载
    background_load_simulator_->startBackgroundLoad(5, 10);

    // 等待背景负载稳定
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 在背景负载下测试流式接口
    PerformanceStats bg_stream_stats = measureGetTriggerInfoStreamingLatency(200, 5);

    bg_stream_stats.motion_mode = "WithBackgroundLoad";
    bg_stream_stats.background_load_clients = 5;
    all_test_results_["GetTriggerInfo_WithBackgroundLoad"] = bg_stream_stats;

    // 停止背景负载
    background_load_simulator_->stopBackgroundLoad();

    // 验证背景负载下的性能
    EXPECT_LT(bg_stream_stats.p95, 50000.0)
                        << "背景负载下GetTriggerInfo P95延迟过高: " << bg_stream_stats.p95 << "μs";
    EXPECT_LT(bg_stream_stats.error_rate, 0.01)
                        << "背景负载下GetTriggerInfo错误率过高: " << 0.01 << "%";

    std::cout << "[RESULT] 背景负载下流式测试完成: P95延迟=" << std::fixed
              << std::setprecision(1) << bg_stream_stats.p95 << "μs" << std::endl;

    std::cout << "\n[RESULT] GetTriggerInfo流式接口专项测试完成" << std::endl;
}

/**
 * @brief GetTriggerInfo流式接口长时间稳定性测试
 */
TEST_F(RealMachinePerformanceTest, GetTriggerInfoStreamingStableTest) {
    // 单流长时间稳定性测试（10秒持续测试）
    std::cout << "\n[INFO] 执行单流长时间稳定性测试..." << std::endl;
    PerformanceStats single_stream_stats = measureGetTriggerInfoStreamingLatency(200, 10);
    single_stream_stats.motion_mode = "SingleStream_10s";
    all_test_results_["GetTriggerInfo_SingleStream_10s"] = single_stream_stats;

    // 验证单流性能
    EXPECT_LT(single_stream_stats.p95, 50000)
                        << "GetTriggerInfo单流P95延迟超过目标: " << single_stream_stats.p95 << "μs";
    EXPECT_LT(single_stream_stats.error_rate, 0.01)
                        << "GetTriggerInfo单流错误率超过目标: " << (single_stream_stats.error_rate * 100) << "%";
    EXPECT_LT(single_stream_stats.min, 100000.0)
                        << "流建立时间过长: " << single_stream_stats.min << "μs";

    std::cout << "[RESULT] 单流测试完成: 流建立时间=" << std::fixed << std::setprecision(1)
              << single_stream_stats.min << "μs, P95延迟=" << single_stream_stats.p95 << "μs" << std::endl;
}

/**
 * @brief GetTriggerInfo流式接口在背景负载下的测试
 */
TEST_F(RealMachinePerformanceTest, GetTriggerInfoStreamingTestWithBG) {
    std::cout << "\n[INFO] 开始GetTriggerInfo接口专项性能测试..." << std::endl;

    // 测试参数
    const int messages_per_stream = 200;
    const int test_duration_seconds = 10;
    const std::vector<int> background_client_counts = {1, 3, 5};

    // 性能目标：P95 < 30000μs
    const double p95_target_us = 5000.0;
    const double p99_target_us = 10000.0;
    const double avg_target_us = 5000.0;

    for (int bg_clients: background_client_counts) {
        std::cout << "\n[INFO] 测试配置: 背景负载客户端=" << bg_clients << std::endl;

        // 启动背景负载（如果需要）
        if (bg_clients > 0) {
            background_load_simulator_->startBackgroundLoad(bg_clients, 10); // 10ms频率
            std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待背景负载稳定
        }

        // 执行GetTriggerInfo性能测试
        auto stats = measureGetTriggerInfoStreamingLatency(messages_per_stream, test_duration_seconds);

        // 停止背景负载
        if (bg_clients > 0) {
            background_load_simulator_->stopBackgroundLoad();
        }

        // 设置测试结果的额外信息
        stats.background_load_clients = bg_clients;
        stats.motion_mode = "TriggerInfo";

        // 保存结果
        std::string test_name = "GetTriggerInfo_BG" + std::to_string(bg_clients);
        all_test_results_[test_name] = stats;

        // 打印结果
        std::cout << "[RESULT] " << test_name << ": "
                  << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "μs, "
                  << "P95=" << stats.p95 << "μs, "
                  << "P99=" << stats.p99 << "μs, "
                  << "Avg=" << stats.avg << "μs, "
                  << "Min=" << stats.min << "μs, "
                  << "Max=" << stats.max << "μs, "
                  << "消息数=" << stats.total_requests << ", "
                  << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

        // 检查性能目标
        if (stats.p95 <= p95_target_us) {
            std::cout << "[✓] 性能目标达成 (P95 <= " << p95_target_us << "μs)" << std::endl;
        } else {
            std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_target_us << "μs)" << std::endl;
        }

        // 验证基本性能要求
        EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
        EXPECT_GT(stats.total_requests, 0) << test_name << " 未接收到消息";
        EXPECT_LT(stats.p99, p99_target_us) << test_name << " P99延迟过高";
        EXPECT_LT(stats.p95, p95_target_us) << test_name << " P95延迟过高";
        EXPECT_LT(stats.avg, avg_target_us) << test_name << " 平均延迟过高";
    }

    std::cout << "\n[INFO] GetTriggerInfo接口专项性能测试完成" << std::endl;
}

/**
 * @brief GetTriggerInfo流式接口基础测试(支持并发测试)
 */
TEST_F(RealMachinePerformanceTest, GetTriggerInfoStreamingTestBasic) {
    std::cout << "\n[INFO] 开始流式接口延时测试..." << std::endl;

    const std::vector<int> concurrent_streams = {1};
    const int messages_per_stream = 5000;
    const int test_duration_seconds = 5;

    for (int stream_count: concurrent_streams) {
        std::cout << "\n[INFO] 测试并发流数量: " << stream_count << std::endl;

        auto stream_stats = testStreamPerformance(stream_count, messages_per_stream, test_duration_seconds);

        // 保存结果
        std::string test_name = "StreamingLatency_" + std::to_string(stream_count) + "streams";
        all_test_results_[test_name] = stream_stats;

        std::cout << "[RESULT] 并发流(" << stream_count << "): "
                  << "流建立=" << std::fixed << std::setprecision(1) << stream_stats.min << "μs, "
                  << "消息延迟P50=" << stream_stats.p50 << "μs, "
                  << "消息延迟P95=" << stream_stats.p95 << "μs, "
                  << "错误率=" << std::setprecision(2) << (stream_stats.error_rate * 100) << "%" << std::endl;

        // 验证流式接口性能要求
        EXPECT_LT(stream_stats.min, 100000.0) << "流建立时间过长";
        EXPECT_LT(stream_stats.p95, 5000.0) << "消息传输延迟过高";
        EXPECT_LT(stream_stats.avg, 5000.0) << "平均延迟过高";
        EXPECT_LE(stream_stats.error_rate, 0.001) << "流式接口错误率过高";

        // 检查性能目标
        if (stream_stats.min < 100000.0 && stream_stats.p95 < 10000.0) {
            std::cout << "[✓] 流式接口性能优秀" << std::endl;
        } else if (stream_stats.min < 100000.0 && stream_stats.p95 < 50000.0) {
            std::cout << "[✓] 流式接口性能良好" << std::endl;
        } else {
            std::cout << "[⚠] 流式接口性能需要优化" << std::endl;
        }
    }

    std::cout << "\n[INFO] 流式接口延时测试完成" << std::endl;
}

/**
 * @brief 轻量级接口延迟测试
 */
TEST_F(RealMachinePerformanceTest, LightInterfaceLatencyTest) {
    std::cout << "\n[TEST] 轻量级接口延迟测试" << std::endl;

    // 定义所有需要测试的接口及其性能目标
    struct InterfaceTarget {
        std::string name;
        double p95_target_us;
    };

    std::vector<InterfaceTarget> interfaces = {
            {"StopMove",       50000.0},
            {"HeartBeatCheck", 50000.0},
            {"GainControl",    50000.0},
            {"ReleaseControl", 50000.0},
            {"GetPostId",      50000.0}
    };
    const int total_request = 30;

    for (const auto &interface: interfaces) {
        std::cout << "\n[INFO] 测试接口: " << interface.name << std::endl;

        PerformanceStats stats;
        // 使用标准方法测试其他接口
        stats = measureInterfaceLatency(interface.name, total_request);


        std::string test_name = interface.name;
        all_test_results_[test_name] = stats;

        // 性能目标验证和可视化显示
        bool p95_passed = stats.p95 <= interface.p95_target_us;
        bool error_rate_passed = stats.error_rate <= 0.01;
        bool overall_passed = p95_passed && error_rate_passed;

        std::string status_symbol = overall_passed ? "✓" : "⚠";

        std::cout << "[RESULT] " << status_symbol << " " << interface.name
                  << ": P95=" << std::fixed << std::setprecision(1) << stats.p95
                  << "μs (目标≤" << interface.p95_target_us << "μs), "
                  << "错误率=" << std::setprecision(3) << (stats.error_rate * 100) << "%" << std::endl;

// 验证性能要求
        EXPECT_LE(stats.p95, interface.p95_target_us)
                            << interface.name << " P95延迟超过目标: " << stats.p95 << "μs > " << interface.p95_target_us
                            << "μs";
        EXPECT_LE(stats.error_rate, 0.01)
                            << interface.name << " 错误率超过目标: " << (stats.error_rate * 100) << "%";
    }

    std::cout << "\n[RESULT] 完整的单接口延迟测试完成" << std::endl;
}

/**
 * @brief 网络质量监控测试
 */
TEST_F(RealMachinePerformanceTest, NetworkQualityTest) {
    std::cout << "\n[TEST] 网络质量监控测试" << std::endl;

// 测量网络质量
    auto quality = network_latency_measurer_->measureNetworkQuality(test_config_.server_address, 10);
    network_latency_measurer_->printNetworkQualityReport(quality);

// 验证网络质量
    EXPECT_TRUE(quality.is_reachable) << "服务器不可达";
    if (quality.is_reachable) {
        EXPECT_LT(quality.avg_rtt_ms, 50.0) << "网络RTT过高: " << quality.avg_rtt_ms << "ms";
        EXPECT_LT(quality.packet_loss_rate, 0.01) << "丢包率过高: " << (quality.packet_loss_rate * 100) << "%";
    }

    std::cout << "[RESULT] 网络质量测试完成" << std::endl;
}

// 主函数
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);

    std::cout << "========================================" << std::endl;
    std::cout << "真机gRPC通信性能测试程序" << std::endl;
    std::cout << "========================================" << std::endl;

    return RUN_ALL_TESTS();
}
