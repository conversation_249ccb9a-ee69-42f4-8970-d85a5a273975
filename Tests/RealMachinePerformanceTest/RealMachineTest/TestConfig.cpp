#include "TestConfig.hpp"
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>

// 简单的JSON解析（仅用于基本配置项）
TestConfig TestConfig::loadFromFile(const std::string& filename) {
    TestConfig config = createDefault();
    
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "[WARNING] 无法打开配置文件: " << filename 
                     << ", 使用默认配置" << std::endl;
            return config;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // 移除空格和制表符
            line.erase(0, line.find_first_not_of(" \t"));
            line.erase(line.find_last_not_of(" \t") + 1);
            
            // 跳过注释和空行
            if (line.empty() || line[0] == '#' || line.find("//") == 0) {
                continue;
            }
            
            // 简单的键值对解析
            if (line.find("\"server_address\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                size_t first_quote = line.find("\"", start);
                size_t second_quote = line.find("\"", first_quote + 1);
                if (first_quote != std::string::npos && second_quote != std::string::npos) {
                    config.server_address = line.substr(first_quote + 1, second_quote - first_quote - 1);
                }
            }
            else if (line.find("\"server_port\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.server_port = std::stoi(value);
            }
            else if (line.find("\"connection_timeout_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.connection_timeout_ms = std::stoi(value);
            }
            else if (line.find("\"request_timeout_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.request_timeout_ms = std::stoi(value);
            }
            else if (line.find("\"max_concurrent_clients\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.max_concurrent_clients = std::stoi(value);
            }
            // 性能目标解析
            else if (line.find("\"StartMove_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.StartMove_p95_ms = std::stod(value);
            }
            else if (line.find("\"GetSystemStatusInfo_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.GetSystemStatusInfo_p95_ms = std::stod(value);
            }
            else if (line.find("\"HeartBeatCheck_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.HeartBeatCheck_p95_ms = std::stod(value);
            }
            else if (line.find("\"max_error_rate\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.max_error_rate = std::stod(value);
            }
            else if (line.find("\"StopMove_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.StopMove_p95_ms = std::stod(value);
            }
            else if (line.find("\"GainControl_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.GainControl_p95_ms = std::stod(value);
            }
            else if (line.find("\"ReleaseControl_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.ReleaseControl_p95_ms = std::stod(value);
            }
            else if (line.find("\"GetPostId_p95_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.performance_targets.GetPostId_p95_ms = std::stod(value);
            }
            // 测试场景解析
            else if (line.find("\"background_clients\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.test_scenarios.background_clients = std::stoi(value);
            }
            else if (line.find("\"background_frequency_ms\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.test_scenarios.background_frequency_ms = std::stoi(value);
            }
            else if (line.find("\"test_duration_seconds\"") != std::string::npos) {
                size_t start = line.find(":") + 1;
                std::string value = line.substr(start);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t,") + 1);
                config.test_scenarios.test_duration_seconds = std::stoi(value);
            }
        }
        
        file.close();
        std::cout << "[INFO] 配置文件加载成功: " << filename << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] 解析配置文件失败: " << e.what() 
                 << ", 使用默认配置" << std::endl;
        return createDefault();
    }
    
    return config;
}

TestConfig TestConfig::createDefault() {
    TestConfig config;
    
    // 基本配置
    config.test_environment = "REAL_MACHINE";
    config.server_address = "*************";
    config.server_port = 50051;
    config.connection_timeout_ms = 10000;
    config.request_timeout_ms = 15000;
    config.max_concurrent_clients = 5;
    
    // 性能目标（针对真机环境调整）
    config.performance_targets.StartMove_p95_ms = 100.0;
    config.performance_targets.GetTriggerInfo_p95_ms = 100.0;
    config.performance_targets.GetSystemStatusInfo_p95_ms = 150.0;
    config.performance_targets.HeartBeatCheck_p95_ms = 50.0;
    config.performance_targets.StopMove_p95_ms = 60.0;
    config.performance_targets.GainControl_p95_ms = 50.0;
    config.performance_targets.ReleaseControl_p95_ms = 50.0;
    config.performance_targets.GetPostId_p95_ms = 80.0;
    config.performance_targets.max_error_rate = 0.005;
    
    // 测试场景
    config.test_scenarios.enable_background_load = true;
    config.test_scenarios.background_clients = 5;
    config.test_scenarios.background_frequency_ms = 10;
    config.test_scenarios.test_duration_seconds = 300;
    
    return config;
}

bool TestConfig::saveToFile(const std::string& filename) const {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        file << "{\n";
        file << "  \"test_environment\": \"" << test_environment << "\",\n";
        file << "  \"server_address\": \"" << server_address << "\",\n";
        file << "  \"server_port\": " << server_port << ",\n";
        file << "  \"connection_timeout_ms\": " << connection_timeout_ms << ",\n";
        file << "  \"request_timeout_ms\": " << request_timeout_ms << ",\n";
        file << "  \"max_concurrent_clients\": " << max_concurrent_clients << ",\n";
        file << "  \"performance_targets\": {\n";
        file << "    \"StartMove_p95_ms\": " << performance_targets.StartMove_p95_ms << ",\n";
        file << "    \"GetTriggerInfo_p95_ms\": " << performance_targets.GetTriggerInfo_p95_ms << ",\n";
        file << "    \"GetSystemStatusInfo_p95_ms\": " << performance_targets.GetSystemStatusInfo_p95_ms << ",\n";
        file << "    \"HeartBeatCheck_p95_ms\": " << performance_targets.HeartBeatCheck_p95_ms << ",\n";
        file << "    \"StopMove_p95_ms\": " << performance_targets.StopMove_p95_ms << ",\n";
        file << "    \"GainControl_p95_ms\": " << performance_targets.GainControl_p95_ms << ",\n";
        file << "    \"ReleaseControl_p95_ms\": " << performance_targets.ReleaseControl_p95_ms << ",\n";
        file << "    \"GetPostId_p95_ms\": " << performance_targets.GetPostId_p95_ms << ",\n";
        file << "    \"max_error_rate\": " << performance_targets.max_error_rate << "\n";
        file << "  },\n";
        file << "  \"test_scenarios\": {\n";
        file << "    \"enable_background_load\": " << (test_scenarios.enable_background_load ? "true" : "false") << ",\n";
        file << "    \"background_clients\": " << test_scenarios.background_clients << ",\n";
        file << "    \"background_frequency_ms\": " << test_scenarios.background_frequency_ms << ",\n";
        file << "    \"test_duration_seconds\": " << test_scenarios.test_duration_seconds << "\n";
        file << "  }\n";
        file << "}\n";
        
        file.close();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] 保存配置文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool TestConfig::isValid() const {
    return !server_address.empty() && 
           server_port > 0 && server_port < 65536 &&
           connection_timeout_ms > 0 &&
           request_timeout_ms > 0 &&
           max_concurrent_clients > 0;
}

void TestConfig::printConfig() const {
    std::cout << "\n=== 真机测试配置 ===" << std::endl;
    std::cout << "服务器地址: " << server_address << ":" << server_port << std::endl;
    std::cout << "连接超时: " << connection_timeout_ms << "ms" << std::endl;
    std::cout << "请求超时: " << request_timeout_ms << "ms" << std::endl;
    std::cout << "最大并发: " << max_concurrent_clients << std::endl;
    std::cout << "\n性能目标:" << std::endl;
    std::cout << "  StartMove P95: " << performance_targets.StartMove_p95_ms << "ms" << std::endl;
    std::cout << "  GetTriggerInfo P95: " << performance_targets.GetTriggerInfo_p95_ms << "ms" << std::endl;
    std::cout << "  GetSystemStatusInfo P95: " << performance_targets.GetSystemStatusInfo_p95_ms << "ms" << std::endl;
    std::cout << "  HeartBeatCheck P95: " << performance_targets.HeartBeatCheck_p95_ms << "ms" << std::endl;
    std::cout << "  StopMove P95: " << performance_targets.StopMove_p95_ms << "ms" << std::endl;
    std::cout << "  GainControl P95: " << performance_targets.GainControl_p95_ms << "ms" << std::endl;
    std::cout << "  ReleaseControl P95: " << performance_targets.ReleaseControl_p95_ms << "ms" << std::endl;
    std::cout << "  GetPostId P95: " << performance_targets.GetPostId_p95_ms << "ms" << std::endl;
    std::cout << "  最大错误率: " << (performance_targets.max_error_rate * 100) << "%" << std::endl;
    std::cout << "===================" << std::endl;
}
