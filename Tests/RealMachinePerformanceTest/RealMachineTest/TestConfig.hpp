#ifndef TEST_CONFIG_HPP
#define TEST_CONFIG_HPP

#include <string>

/**
 * @brief 真机测试配置结构
 */
struct PerformanceTargets {
    double StartMove_p95_ms = 100.0;
    double GetTriggerInfo_p95_ms = 100.0;
    double GetSystemStatusInfo_p95_ms = 150.0;
    double HeartBeatCheck_p95_ms = 50.0;
    double StopMove_p95_ms = 60.0;
    double GainControl_p95_ms = 50.0;
    double ReleaseControl_p95_ms = 50.0;
    double GetPostId_p95_ms = 80.0;
    double max_error_rate = 0.005;
};

struct TestScenarios {
    bool enable_background_load = true;
    int background_clients = 5;
    int background_frequency_ms = 10;
    int test_duration_seconds = 300;
};

struct TestConfig {
    std::string test_environment = "REAL_MACHINE";
    std::string server_address = "*************";
    int server_port = 50051;
    int connection_timeout_ms = 10000;
    int request_timeout_ms = 15000;
    int max_concurrent_clients = 5;
    
    PerformanceTargets performance_targets;
    TestScenarios test_scenarios;
    
    // 静态方法：从JSON文件加载配置
    static TestConfig loadFromFile(const std::string& filename);
    
    // 静态方法：创建默认配置
    static TestConfig createDefault();
    
    // 保存配置到文件
    bool saveToFile(const std::string& filename) const;
    
    // 验证配置有效性
    bool isValid() const;
    
    // 打印配置信息
    void printConfig() const;
};

#endif // TEST_CONFIG_HPP
