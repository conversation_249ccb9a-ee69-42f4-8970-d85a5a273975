{"test_environment": "REAL_MACHINE", "server_address": "*************", "server_port": 50051, "connection_timeout_ms": 10000, "request_timeout_ms": 15000, "max_concurrent_clients": 5, "performance_targets": {"StartMove_p95_ms": 100, "GetTriggerInfo_p95_ms": 100, "GetSystemStatusInfo_p95_ms": 150, "HeartBeatCheck_p95_ms": 50, "StopMove_p95_ms": 60, "GainControl_p95_ms": 50, "ReleaseControl_p95_ms": 50, "GetPostId_p95_ms": 80, "max_error_rate": 0.005}, "test_scenarios": {"enable_background_load": true, "background_clients": 5, "background_frequency_ms": 10, "test_duration_seconds": 300}, "comment": "真机gRPC性能测试配置文件", "description": {"purpose": "专用于真机环境的gRPC通信性能测试", "network_requirements": {"bandwidth": ">=100Mbps", "rtt": "<10ms", "packet_loss": "<0.1%"}, "test_focus": ["网络通信延迟", "并发处理能力", "长时间稳定性", "背景负载影响"]}}