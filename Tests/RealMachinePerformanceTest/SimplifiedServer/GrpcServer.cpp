#include "GrpcServer.hpp"
#include "SyslogManager.hpp"
#include <memory>
#include <string>
#include <chrono>
#include <map>
#include <thread>
#include <utility>
#include <atomic>

// BedMasterAppServiceImpl 实现

BedMasterAppServiceImpl::BedMasterAppServiceImpl(ServoControlManager *servo_manager)
        : servo_manager_(servo_manager) {
    if (isSimulationMode()) {
        LOG_INFO_MSG("GrpcServer", "BedMasterAppService启动在模拟模式");
    }
}

BedMasterAppServiceImpl::~BedMasterAppServiceImpl() = default;

// 模拟处理延迟
void BedMasterAppServiceImpl::simulateProcessingDelay(const std::string &interface_name) {
    if (!isSimulationMode()) return;

    // 根据接口类型模拟不同的处理延迟
    if (interface_name == "StartMove") {
        std::this_thread::sleep_for(std::chrono::microseconds(150));
    } else if (interface_name == "GetSystemStatusInfo") {
        std::this_thread::sleep_for(std::chrono::microseconds(80));
    } else if (interface_name == "HeartBeatCheck") {
        std::this_thread::sleep_for(std::chrono::microseconds(20));
    } else if (interface_name == "GainControl" || interface_name == "ReleaseControl") {
        std::this_thread::sleep_for(std::chrono::microseconds(30));
    } else if (interface_name == "GetPostId") {
        std::this_thread::sleep_for(std::chrono::microseconds(60));
    } else if (interface_name == "StopMove") {
        std::this_thread::sleep_for(std::chrono::microseconds(40));
    } else {
        std::this_thread::sleep_for(std::chrono::microseconds(50)); // 默认延迟
    }

    // 记录处理完成时间戳（微秒精度）
    processing_complete_timestamp_ = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
}

grpc::Status BedMasterAppServiceImpl::StartMove(
        grpc::ServerContext *context,
        const motor_control::StartMoveDescription *request,
        motor_control::StartMoveStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    LOG_INFO_MSG("StartMove", "Ready to Handle request!");

    try {
        // 设置响应的contextUID
        response->set_contextuid(contextUID);

        // 模拟模式：直接返回成功响应
        if (isSimulationMode()) {
            simulateProcessingDelay("StartMove");

            // 添加处理完成时间戳到trailing metadata
            context->AddTrailingMetadata("server-processing-complete-timestamp",
                                        std::to_string(processing_complete_timestamp_));

            response->set_errorcode(0); // 成功

            // 填充模拟的当前运动信息
            motor_control::MotionInfo *currentInfo = response->mutable_currentmotioninfo();
            currentInfo->set_postion(request->targetmotioninfo().postion());
            currentInfo->set_velocity(request->targetmotioninfo().velocity());

            logOperation("StartMove", contextUID, clientHost, true, "模拟模式：移动命令执行成功");
            return grpc::Status::OK;
        }

        // 原有业务逻辑（当servo_manager_不为nullptr时）
        // ... 这里可以保留原有逻辑，但在简化服务器中不会执行到

        response->set_errorcode(1); // 错误：不应该到达这里
        logOperation("StartMove", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("StartMove", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::StopMove(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    LOG_INFO_MSG("StopMove", "Ready to Handle request!");
    try {
        response->set_contextuid(contextUID);

        if (isSimulationMode()) {
            simulateProcessingDelay("StopMove");
            response->set_errorcode(0);
            logOperation("StopMove", contextUID, clientHost, true, "模拟模式：停止命令执行成功");
            return grpc::Status::OK;
        }

        response->set_errorcode(1);
        logOperation("StopMove", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("StopMove", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetPostId(
        grpc::ServerContext *context,
        const motor_control::GetPostIdDescription *request,
        motor_control::GetPostIdStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    LOG_INFO_MSG("GetPostId", "Ready to Handle request!");
    try {
        response->set_contextuid(contextUID);

        if (isSimulationMode()) {
            simulateProcessingDelay("GetPostId");

            // 填充模拟的PostId信息
            auto *postid = response->mutable_postid();
            postid->set_vid(0x12345678);
            postid->set_did(0x87654321);
            postid->set_hwid(0x11223344);
            postid->set_rid(0x44332211);
            response->set_errorcode(0);

            logOperation("GetPostId", contextUID, clientHost, true, "模拟模式：PostId获取成功");
            return grpc::Status::OK;
        }

        response->set_errorcode(1);
        logOperation("GetPostId", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("GetPostId", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GainControl(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    LOG_INFO_MSG("GainControl", "Ready to Handle request!");
    response->set_errorcode(0);
    LOG_INFO_MSG("GainControl", "Finished to Handle request!");
    return grpc::Status::OK;



    try {
        response->set_contextuid(contextUID);

        if (isSimulationMode()) {
            simulateProcessingDelay("GainControl");
            response->set_errorcode(0);
            logOperation("GainControl", contextUID, clientHost, true, "模拟模式：成功获取控制权");
            return grpc::Status::OK;
        }

        response->set_errorcode(1);
        logOperation("GainControl", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("GainControl", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::ReleaseControl(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);
    LOG_INFO_MSG("ReleaseControl", "Ready to Handle request!");
    try {
        response->set_contextuid(contextUID);

        if (isSimulationMode()) {
            simulateProcessingDelay("ReleaseControl");
            response->set_errorcode(0);
            logOperation("ReleaseControl", contextUID, clientHost, true, "模拟模式：成功释放控制权");
            return grpc::Status::OK;
        }

        response->set_errorcode(1);
        logOperation("ReleaseControl", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("ReleaseControl", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::HeartBeatCheck(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);
    LOG_INFO_MSG("HeartBeatCheck", "Ready to Handle request!");
    try {
        response->set_contextuid(contextUID);

        if (isSimulationMode()) {
            simulateProcessingDelay("HeartBeatCheck");
            response->set_errorcode(0);
            logOperation("HeartBeatCheck", contextUID, clientHost, true, "模拟模式：心跳检测成功");
            return grpc::Status::OK;
        }

        response->set_errorcode(1);
        logOperation("HeartBeatCheck", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        response->set_errorcode(1);
        logOperation("HeartBeatCheck", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetSystemStatusInfo(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::SystemStatusInfoStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);
    LOG_INFO_MSG("GetSystemStatusInfo", "Ready to Handle request!");
    try {

        if (isSimulationMode()) {
            simulateProcessingDelay("GetSystemStatusInfo");

            logOperation("GetSystemStatusInfo", contextUID, clientHost, true, "模拟模式：系统状态获取成功");
            return grpc::Status::OK;
        }

        logOperation("GetSystemStatusInfo", contextUID, clientHost, false, "意外的执行路径");
        return grpc::Status::OK;

    } catch (const std::exception &e) {
        logOperation("GetSystemStatusInfo", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetTriggerInfo(
        grpc::ServerContext *context,
        grpc::ServerReaderWriter<motor_control::TriggerInfoStatus,
                motor_control::GetTriggerInfoDescription> *stream) {

    motor_control::HostType clientHost = getClientHostType(context);

    try {
        if (isSimulationMode()) {
            LOG_INFO_MSG("GrpcServer", "模拟模式：开始GetTriggerInfo流式服务");

            std::atomic<uint64_t> counter{0};
            motor_control::GetTriggerInfoDescription request;

            std::atomic<bool> stop_stream{false};
            std::thread reader_thread = std::thread([stream, &stop_stream, context]() {
                motor_control::GetTriggerInfoDescription request;
                // stream->Read() 是一个阻塞操作，当有新消息时才会返回 true
                // 当客户端关闭写入流 (WritesDone) 或者连接断开时，返回 false
                while (stream->Read(&request)) {
                    LOG_INFO_MSG("BedMasterAppServiceImpl", "Received message from client with contextUID: %s", request.contextuid().c_str());
                }

                LOG_INFO_MSG("BedMasterAppServiceImpl", "Client has finished sending messages. Signalling writer to stop.");
                stop_stream = true; // 如果客户端关闭流，也应停止
            });

            // 持续发送模拟的触发信息
            while (!context->IsCancelled() && !stop_stream.load()) {

                motor_control::TriggerInfoStatus response;
                response.set_contextuid("trigger-sim-" + std::to_string(counter));
                response.set_errorcode(0);

                // 成功获取到新的Trigger信息
                motor_control::TriggerInfoStatus status_update;
                status_update.set_contextuid(request.contextuid()); // 使用初始请求的contextUID
                status_update.set_errorcode(0);
                status_update.set_triggerposition(counter % 1000);
                status_update.set_triggertimestamp(counter);
                status_update.set_exposuretime(counter);

                if (stop_stream.load()) {
                    break;
                }

                if (!stream->Write(response)) {
                    counter++;
                    LOG_INFO_MSG("GrpcServer", "模拟模式：客户端断开连接，停止发送触发信息");
                    break;
                }

                // 模拟10ms间隔
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }

            LOG_INFO_MSG("GrpcServer", "模拟模式：GetTriggerInfo流式服务结束, 已发送[%llu]条数据", counter.load());
            if (reader_thread.joinable())
                reader_thread.join();

            return grpc::Status::OK;
        }

        LOG_ERROR_MSG("GrpcServer", "GetTriggerInfo: 意外的执行路径");
        return grpc::Status(grpc::StatusCode::INTERNAL, "意外的执行路径");

    } catch (const std::exception &e) {
        LOG_ERROR_MSG("GrpcServer", "GetTriggerInfo异常: %s", e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

// 辅助方法实现
motor_control::HostType BedMasterAppServiceImpl::getClientHostType(grpc::ServerContext *context) {
    // 从metadata中获取客户端类型，如果没有则默认为NONE
    auto metadata = context->client_metadata();
    auto it = metadata.find("client-type");

    if (it != metadata.end()) {
        std::string clientType(it->second.data(), it->second.length());
        if (clientType == "CT") return motor_control::HostType::CT;
        if (clientType == "PET") return motor_control::HostType::PET;
        if (clientType == "SPECT") return motor_control::HostType::SPECT;
    }

    return motor_control::HostType::NONE;
}

void BedMasterAppServiceImpl::logOperation(const std::string &operation, const std::string &contextUID,
                                           motor_control::HostType hostType, bool success, const std::string &details) {
    std::string hostTypeStr;
    switch (hostType) {
        case motor_control::HostType::CT: hostTypeStr = "CT"; break;
        case motor_control::HostType::PET: hostTypeStr = "PET"; break;
        case motor_control::HostType::SPECT: hostTypeStr = "SPECT"; break;
        default: hostTypeStr = "NONE"; break;
    }

    if (success) {
        LOG_INFO_MSG("GrpcServer", "[%s] %s - %s: %s",
                     hostTypeStr.c_str(), operation.c_str(), contextUID.c_str(), details.c_str());
    } else {
        LOG_ERROR_MSG("GrpcServer", "[%s] %s - %s: %s",
                      hostTypeStr.c_str(), operation.c_str(), contextUID.c_str(), details.c_str());
    }
}

// GrpcServer类实现
GrpcServer::GrpcServer(std::string server_address, ServoControlManager *servo_manager)
        : server_address_(std::move(server_address)), running_(false), servo_manager_(servo_manager) {
    bed_service_ = std::make_unique<BedMasterAppServiceImpl>(servo_manager);
}

GrpcServer::~GrpcServer() {
    stop();
}

bool GrpcServer::start() {
    try {
        grpc::ServerBuilder builder;

        // 监听地址
        builder.AddListeningPort(server_address_, grpc::InsecureServerCredentials());

        // 注册服务
        builder.RegisterService(bed_service_.get());

        // 构建并启动服务器
        server_ = builder.BuildAndStart();
        if (!server_) {
            LOG_ERROR_MSG("GrpcServer", "无法启动gRPC服务器在地址: %s", server_address_.c_str());
            return false;
        }

        running_.store(true);
        LOG_INFO_MSG("GrpcServer", "gRPC服务器已启动，监听地址: %s", server_address_.c_str());

        if (servo_manager_ == nullptr) {
            LOG_INFO_MSG("GrpcServer", "服务器运行在模拟模式（性能测试专用）");
        }

        return true;
    } catch (const std::exception &e) {
        LOG_ERROR_MSG("GrpcServer", "启动gRPC服务器时发生异常: %s", e.what());
        return false;
    }
}

void GrpcServer::stop() {
    if (running_.load() && server_) {
        LOG_INFO_MSG("GrpcServer", "正在停止gRPC服务器...");
        server_->Shutdown();
        running_.store(false);
        LOG_INFO_MSG("GrpcServer", "gRPC服务器已停止");
    }
}

void GrpcServer::wait() {
    if (server_) {
        server_->Wait();
    }
}
