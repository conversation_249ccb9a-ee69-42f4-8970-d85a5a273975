{"grpc_server_address": "0.0.0.0:50051", "comment": "简化gRPC性能测试服务器配置文件", "description": {"purpose": "专用于gRPC通信性能测试", "mode": "simulation_mode", "features": ["屏蔽CANopen业务逻辑", "返回模拟数据", "模拟真实处理延迟", "支持所有gRPC接口"]}, "simulated_delays": {"StartMove_us": 150, "GetSystemStatusInfo_us": 80, "GetTriggerInfo_interval_ms": 50, "HeartBeatCheck_us": 20, "GainControl_us": 30, "ReleaseControl_us": 30, "GetPostId_us": 60, "StopMove_us": 40}}