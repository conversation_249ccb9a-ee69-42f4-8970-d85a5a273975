#include "GrpcServer.hpp"
#include "SyslogManager.hpp"
#include <string>
#include <thread>
#include <chrono>
#include <csignal>
#include <vector>
#include <fstream>
#include <sstream>
#include <google/protobuf/util/json_util.h>
#include "motor_control.pb.h"

// 全局变量
volatile bool running = true;
std::unique_ptr<GrpcServer> g_grpc_server;

// 简化配置结构体
struct SimplifiedConfig {
    std::string grpc_server_address;
};

// 信号处理函数
void signalHandler(int signal) {
    LOG_INFO_MSG("main", "接收到信号: %d, 程序将退出...", signal);
    running = false;
}

// 加载简化配置文件
bool loadConfig(const std::string& filename, SimplifiedConfig& config) {
    try {
        // 打开配置文件
        std::ifstream file(filename);
        if (!file.is_open()) {
            LOG_ERROR_MSG("main", "无法打开配置文件: %s", filename.c_str());
            return false;
        }
        
        // 读取整个文件内容
        std::stringstream buffer;
        buffer << file.rdbuf();
        std::string json_str = buffer.str();
        
        // 创建配置消息
        motor_control::ConfigMessage configMsg;
        
        // 解析JSON到protobuf
        google::protobuf::util::JsonParseOptions options;
        options.ignore_unknown_fields = true;
        auto status = google::protobuf::util::JsonStringToMessage(json_str, &configMsg, options);
        
        if (!status.ok()) {
            LOG_ERROR_MSG("main", "解析配置文件失败: %s", status.ToString().c_str());
            return false;
        }
        
        // 读取配置项
        config.grpc_server_address = configMsg.grpc_server_address();
        
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR_MSG("main", "解析配置文件时发生错误: %s", e.what());
        return false;
    }
}

// 初始化简化的gRPC服务器
bool initGrpcServer(const SimplifiedConfig& config) {
    try {
        // 创建gRPC服务器，传入nullptr作为servo_manager（启用模拟模式）
        g_grpc_server = std::make_unique<GrpcServer>(config.grpc_server_address, nullptr);
        
        // 启动服务器
        LOG_INFO_MSG("main", "启动简化gRPC服务器...");
        if (!g_grpc_server->start()) {
            LOG_ERROR_MSG("main", "启动gRPC服务器失败");
            return false;
        }

        LOG_INFO_MSG("main", "简化gRPC服务器已启动在: %s", config.grpc_server_address.c_str());
        LOG_INFO_MSG("main", "服务器运行在模拟模式，专用于性能测试");
        LOG_INFO_MSG("main", "现在可以通过以下服务进行交互:");
        LOG_INFO_MSG("main", "BedMasterAppService - 动物床控制（模拟模式）");
        
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR_MSG("main", "初始化gRPC服务器时发生异常: %s", e.what());
        return false;
    }
}

// 清理资源
void cleanup() {
    LOG_INFO_MSG("main", "清理资源...");

    // 停止gRPC服务器
    if (g_grpc_server) {
        LOG_INFO_MSG("main", "停止gRPC服务器...");
        g_grpc_server->stop();
        g_grpc_server.reset();
    }

    LOG_INFO_MSG("main", "清理完成");
}

// 显示帮助信息
void showHelp(const char* program) {
    LOG_INFO_MSG("main", "用法: %s [选项]", program);
    LOG_INFO_MSG("main", "选项:");
    LOG_INFO_MSG("main", "  -h, --help         显示帮助信息");
    LOG_INFO_MSG("main", "  -c, --config FILE  指定配置文件 (默认: config.json)");
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置日志
    setLogger(argv[0]);
    
    // 解析命令行参数
    std::string configFile = "config.json";
    
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-h" || arg == "--help") {
            showHelp(argv[0]);
            return 0;
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                configFile = argv[++i];
            } else {
                LOG_ERROR_MSG("main", "错误: --config选项需要一个参数");
                return 1;
            }
        }
    }
    
    // 注册信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);
    
    LOG_INFO_MSG("main", "=============================");
    LOG_INFO_MSG("main", "简化gRPC性能测试服务器");
    LOG_INFO_MSG("main", "=============================");

    // 加载配置
    SimplifiedConfig config;
    if (!loadConfig(configFile, config)) {
        LOG_ERROR_MSG("main", "无法加载配置文件，程序退出。");
        return 1;
    }

    LOG_INFO_MSG("main", "配置已加载:");
    LOG_INFO_MSG("main", "- gRPC服务器地址: %s", config.grpc_server_address.c_str());
    
    try {
        // 初始化gRPC服务器
        if (!initGrpcServer(config)) {
            LOG_ERROR_MSG("main", "初始化gRPC服务器失败，程序退出。");
            cleanup();
            return 1;
        }

        LOG_INFO_MSG("main", "=============================");
        LOG_INFO_MSG("main", "简化服务器已启动，按Ctrl+C退出");
        LOG_INFO_MSG("main", "专用于gRPC通信性能测试");
        LOG_INFO_MSG("main", "=============================");
        
        // 主循环
        while (running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        LOG_INFO_MSG("main", "正在退出程序...");
        cleanup();
        LOG_INFO_MSG("main", "程序已退出。");

    } catch (const std::exception& e) {
        LOG_ERROR_MSG("main", "发生异常: %s", e.what());
        cleanup();
        return 1;
    }
    
    return 0;
}
