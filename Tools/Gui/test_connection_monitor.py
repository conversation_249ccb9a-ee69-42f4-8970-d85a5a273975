#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
连接监控功能测试脚本
用于验证GUI工具的连接状态检测功能
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
import grpc

# 模拟gRPC服务器状态
class MockGrpcServer:
    def __init__(self):
        self.is_running = True
        self.response_delay = 0
        
    def simulate_server_down(self):
        """模拟服务器宕机"""
        self.is_running = False
        
    def simulate_server_up(self):
        """模拟服务器恢复"""
        self.is_running = True
        
    def simulate_slow_response(self, delay=3):
        """模拟服务器响应慢"""
        self.response_delay = delay
        
    def simulate_normal_response(self):
        """模拟正常响应"""
        self.response_delay = 0

class ConnectionMonitorTest(QWidget):
    def __init__(self):
        super().__init__()
        self.mock_server = MockGrpcServer()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("连接监控功能测试")
        self.setGeometry(100, 100, 600, 400)
        
        layout = QVBoxLayout()
        
        # 状态显示
        self.status_label = QLabel("测试状态: 准备就绪")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        btn_server_down = QPushButton("模拟服务器宕机")
        btn_server_down.clicked.connect(self.test_server_down)
        layout.addWidget(btn_server_down)
        
        btn_server_up = QPushButton("模拟服务器恢复")
        btn_server_up.clicked.connect(self.test_server_up)
        layout.addWidget(btn_server_up)
        
        btn_slow_response = QPushButton("模拟响应慢")
        btn_slow_response.clicked.connect(self.test_slow_response)
        layout.addWidget(btn_slow_response)
        
        btn_normal_response = QPushButton("模拟正常响应")
        btn_normal_response.clicked.connect(self.test_normal_response)
        layout.addWidget(btn_normal_response)
        
        btn_test_connection = QPushButton("测试连接检测")
        btn_test_connection.clicked.connect(self.test_connection_check)
        layout.addWidget(btn_test_connection)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
        self.log("连接监控测试工具已启动")
        
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def test_server_down(self):
        """测试服务器宕机场景"""
        self.mock_server.simulate_server_down()
        self.status_label.setText("测试状态: 服务器宕机")
        self.log("模拟服务器宕机 - 连接检测应该失败")
        
    def test_server_up(self):
        """测试服务器恢复场景"""
        self.mock_server.simulate_server_up()
        self.status_label.setText("测试状态: 服务器正常")
        self.log("模拟服务器恢复 - 连接检测应该成功")
        
    def test_slow_response(self):
        """测试响应慢场景"""
        self.mock_server.simulate_slow_response(5)
        self.status_label.setText("测试状态: 响应慢")
        self.log("模拟服务器响应慢(5秒) - 可能触发超时")
        
    def test_normal_response(self):
        """测试正常响应场景"""
        self.mock_server.simulate_normal_response()
        self.status_label.setText("测试状态: 响应正常")
        self.log("模拟正常响应 - 连接检测应该快速成功")
        
    def test_connection_check(self):
        """测试连接检测逻辑"""
        self.log("开始测试连接检测逻辑...")
        
        if not self.mock_server.is_running:
            self.log("❌ 连接检测失败 - 服务器不可用")
            return False
            
        if self.mock_server.response_delay > 3:
            self.log("⚠️ 连接检测超时 - 响应时间过长")
            return False
            
        self.log("✅ 连接检测成功 - 服务器响应正常")
        return True

def main():
    app = QApplication(sys.argv)
    
    # 显示测试说明
    print("=" * 60)
    print("连接监控功能测试说明:")
    print("1. 点击'模拟服务器宕机'测试连接断开检测")
    print("2. 点击'模拟服务器恢复'测试连接恢复检测")
    print("3. 点击'模拟响应慢'测试超时处理")
    print("4. 点击'测试连接检测'验证检测逻辑")
    print("5. 在实际GUI中观察连接状态指示器的变化")
    print("=" * 60)
    
    window = ConnectionMonitorTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
